{"metro-runtime": "0.73.10", "@react-native/polyfills": "2.0.0", "@babel/runtime": "7.25.0", "react": "18.2.0", "react-native": "0.71.14", "invariant": "2.2.4", "stacktrace-parser": "0.1.10", "promise": "8.3.0", "regenerator-runtime": "0.13.11", "event-target-shim": "5.0.1", "base64-js": "1.5.1", "whatwg-fetch": "3.6.20", "abort-controller": "3.0.0", "scheduler": "0.23.2", "deprecated-react-native-prop-types": "3.0.2", "prop-types": "15.8.1", "memoize-one": "5.2.1", "nullthrows": "1.1.1", "use-sync-external-store": "1.2.2", "react-native-gesture-handler": "2.18.1", "react-native-reanimated": "3.3.0", "hoist-non-react-statics": "3.3.2", "react-is": "16.13.1", "react-native-toast-message": "2.2.0", "react-native-svg": "12.5.1", "css-tree": "1.1.3", "source-map": "0.6.1", "css-select": "5.1.0", "domutils": "3.1.0", "dom-serializer": "2.0.0", "domelementtype": "2.3.0", "entities": "4.5.0", "domhandler": "5.0.3", "boolbase": "1.0.0", "css-what": "6.1.0", "nth-check": "2.1.1", "@tanstack/react-query": "4.36.1", "react-redux": "8.1.3", "react-native-mmkv": "2.12.2", "redux-persist": "6.0.0", "redux": "4.2.1", "@reduxjs/toolkit": "1.9.7", "immer": "9.0.21", "reselect": "4.1.8", "redux-thunk": "2.4.2", "react-native-restart": "0.0.27", "react-native-device-info": "8.7.1", "react-native-static-safe-area-insets": "2.2.0", "react-native-style-utilities": "1.0.1", "react-native-bootsplash": "4.7.5", "react-native-screens": "3.29.0", "react-freeze": "1.0.4", "@gorhom/bottom-sheet": "4.6.4", "nanoid": "3.3.7", "@react-navigation/native": "6.1.18", "query-string": "7.1.3", "strict-uri-encode": "2.0.0", "decode-uri-component": "0.2.2", "split-on-first": "1.1.0", "filter-obj": "1.1.0", "escape-string-regexp": "4.0.0", "@react-native-async-storage/async-storage": "1.24.0", "moment": "2.30.1", "@react-native-community/netinfo": "11.3.2", "axios": "0.26.1", "axios-retry": "3.9.1", "is-retry-allowed": "2.2.0", "react-native-network-info": "5.2.1", "react-native-view-shot": "3.8.0", "react-native-safe-area-context": "4.10.9", "fbjs": "3.0.5", "react-native-fs": "2.20.0", "utf8": "3.0.0", "base-64": "0.1.0", "react-native-share": "7.9.1", "uuid-random": "1.3.2", "warn-once": "0.1.1", "react-native-touch-id": "4.4.1", "@react-native-clipboard/clipboard": "1.14.1", "clevertap-react-native": "1.2.1", "rn-qr-generator": "1.4.0", "react-native-contacts": "7.0.8", "customerio-reactnative": "3.3.2", "react-native-keychain": "8.1.3", "react-native-permissions": "3.10.1", "react-hook-form": "7.52.2", "react-native-haptic-feedback": "2.3.1", "react-native-calendars": "1.1306.0", "xdate": "0.8.3", "lodash": "4.17.21", "react-native-swipe-gestures": "1.0.5", "recyclerlistview": "4.2.1", "ts-object-utils": "0.0.5", "lodash.debounce": "4.0.8", "color": "4.2.3", "color-name": "1.1.4", "color-string": "1.9.1", "simple-swizzle": "0.2.2", "react-native-linear-gradient": "2.8.3", "react-native-document-picker": "9.3.0", "react-native-picker-select": "9.3.1", "lodash.isequal": "4.5.0", "lodash.isobject": "3.0.2", "@react-native-picker/picker": "2.7.7", "react-native-image-picker": "4.10.3", "react-native-sha256": "1.4.10", "react-native-vision-camera": "4.0.5", "react-native-keyboard-aware-scroll-view": "0.9.5", "react-native-iphone-x-helper": "1.3.1", "@credolab/react-android-account": "5.1.0", "@seontechnologies/seon-react-native-mobile-wrapper": "2.0.0", "react-native-progress-steps": "1.3.4", "normal-case-generator": "1.0.1", "@datadog/mobile-react-native": "2.4.3", "big-integer": "1.6.52", "react-native-check-version": "1.1.1", "semver": "7.6.3", "react-native-android-location-enabler": "2.0.1", "react-native-webview": "13.10.5", "lottie-react-native": "6.7.2"}