<resources>

    <!-- Base application theme. -->
      <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="android:textColor">#000000</item>
        <item name="android:forceDarkAllowed">false</item>
     <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
               <!-- Add the following line to set the default status bar color for all the app. -->
        <item name="android:statusBarColor">#fff</item>
    </style>
 <!-- BootTheme should inherit from Theme.SplashScreen -->
  <style name="BootTheme" parent="Theme.SplashScreen">
    <item name="windowSplashScreenBackground">@color/bootsplash_background</item>
    <item name="windowSplashScreenAnimatedIcon">@mipmap/bootsplash_logo</item>
    <item name="postSplashScreenTheme">@style/AppTheme</item>
  </style>
</resources>
