<manifest xmlns:android="http://schemas.android.com/apk/res/android" package="com.payforceapp">

  <uses-permission android:name="android.permission.INTERNET" />
  <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.VIBRATE" />
  <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
  <uses-permission android:name="android.permission.READ_CONTACTS" />
  <uses-permission android:name="android.permission.READ_SMS"/>

  <uses-permission
    android:name="android.permission.ACCESS_FINE_LOCATION" />
  <uses-permission android:name="android.permission.CAMERA" />
  <uses-permission android:name="android.permission.READ_PHONE_STATE" />
  <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
  <uses-permission android:name="android.permission.USE_FINGERPRINT" />

  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
    android:maxSdkVersion="29" />

  <queries>
    <package android:name="com.whatsapp" />
  </queries>


  <application
    android:name=".MainApplication"
    android:label="@string/app_name"
    android:icon="@mipmap/ic_launcher"
    android:roundIcon="@mipmap/ic_launcher_round"
    android:allowBackup="false"
    android:theme="@style/BootTheme"
  >
    <meta-data android:name="com.dieam.reactnativepushnotification.notification_foreground"
      android:value="true" />
    <meta-data android:name="com.dieam.reactnativepushnotification.notification_color"
      android:resource="@color/white" />
    <meta-data android:name="com.google.firebase.messaging.default_notification_icon"
      android:resource="@mipmap/ic_launcher_round" />
    <!-- CleverTap Credentials -->
    <meta-data
      android:name="CLEVERTAP_ACCOUNT_ID"
      android:value="@string/CLEVERTAP_ACCOUNT_ID" />
    <meta-data
      android:name="CLEVERTAP_TOKEN"
      android:value="@string/CLEVERTAP_TOKEN" />
    <!-- CleverTap Region to be confirmed -->
    <meta-data
      android:name="CLEVERTAP_REGION"
      android:value="in1" />
    <meta-data
    android:name="CLEVERTAP_USE_GOOGLE_AD_ID"
    android:value="1"/>
    <!-- end  -->
    <!--id
    capture-->
    <activity android:name="com.smileid.smileidui.SIDIDCaptureActivity" />
    <!--selfie
    capture-->
    <activity android:name="com.smileid.smileidui.SIDSelfieCaptureActivity" />
    <!--selfie
    review screen-->
    <activity android:name="com.smileid.smileidui.SIDReviewActivity" />
    <receiver android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationActions"
      android:exported="false" />
    <receiver
      android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationPublisher"
      android:exported="false" />
    <receiver
      android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationBootEventReceiver"
      android:exported="false">
      <intent-filter>
        <action android:name="android.intent.action.BOOT_COMPLETED" />
        <action android:name="android.intent.action.QUICKBOOT_POWERON" />
        <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
      </intent-filter>
    </receiver>

    <service
      android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationListenerService"
      android:exported="false">
      <intent-filter>
        <action android:name="com.google.firebase.MESSAGING_EVENT" />
      </intent-filter>
    </service>
    <activity
      android:name=".MainActivity"
      android:label="@string/app_name"
      android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
      android:launchMode="singleTask"
      android:windowSoftInputMode="adjustPan"
      android:exported="true"
    >
      <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
      </intent-filter>
    </activity>
  </application>

    <queries>
      <intent>
          <action android:name="android.intent.action.MAIN" />
      </intent>
    </queries>
</manifest>
