apply plugin: 'com.android.application'
apply plugin: 'org.jetbrains.kotlin.android'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'
apply plugin: 'com.facebook.react'

apply from: project(':react-native-config').projectDir.getPath() + '/dotenv.gradle'

import com.android.build.OutputFile



/**
 * This is the configuration block to customize your React Native Android app.
 * By default you don't need to apply any configuration, just uncomment the lines you need.
 */
react {
    /* Folders */
    //   The root of your project, i.e. where "package.json" lives. Default is '..'
    // root = file("../")
    //   The folder where the react-native NPM package is. Default is ../node_modules/react-native
    // reactNativeDir = file("../node_modules/react-native")
    //   The folder where the react-native Codegen package is. Default is ../node_modules/@react-native/codegen
    // codegenDir = file("../node_modules/@react-native/codegen")
    //   The cli.js file which is the React Native CLI entrypoint. Default is ../node_modules/react-native/cli.js
    // cliFile = file("../node_modules/react-native/cli.js")

    /* Variants */
    //   The list of variants to that are debuggable. For those we're going to
    //   skip the bundling of the JS bundle and the assets. By default is just 'debug'.
    //   If you add flavors like lite, prod, etc. you'll have to list your debuggableVariants.
    // debuggableVariants = ["liteDebug", "prodDebug"]

    /* Bundling */
    //   A list containing the node command and its flags. Default is just 'node'.
    // nodeExecutableAndArgs = ["node"]
    //
    //   The command to run when bundling. By default is 'bundle'
    // bundleCommand = "ram-bundle"
    //
    //   The path to the CLI configuration file. Default is empty.
    // bundleConfig = file(../rn-cli.config.js)
    //
    //   The name of the generated asset file containing your JS bundle
    // bundleAssetName = "MyApplication.android.bundle"
    //
    //   The entry file for bundle generation. Default is 'index.android.js' or 'index.js'
    // entryFile = file("../js/MyApplication.android.js")
    //
    //   A list of extra flags to pass to the 'bundle' commands.
    //   See https://github.com/react-native-community/cli/blob/main/docs/commands.md#bundle
    // extraPackagerArgs = []

    /* Hermes Commands */
//   The hermes compiler command to run. By default it is 'hermesc'
// hermesCommand = "$rootDir/my-custom-hermesc/bin/hermesc"
//
//   The list of flags to pass to the Hermes compiler. By default is "-O", "-output-source-map"
// hermesFlags = ["-O", "-output-source-map"]
}

/**
 * Set this to true to create four separate APKs instead of one,
 * one for each native architecture. This is useful if you don't
 * use App Bundles (https://developer.android.com/guide/app-bundle/)
 * and want to have separate APKs to upload to the Play Store.
 */
// def enableSeparateBuildPerCPUArchitecture = true

/**
 * Set this to true to Run Proguard on Release builds to minify the Java bytecode.
 */
def enableProguardInReleaseBuilds = true

/**
 * The preferred build flavor of JavaScriptCore (JSC)
 *
 * For example, to use the international variant, you can use:
 * `def jscFlavor = 'org.webkit:android-jsc-intl:+'`
 *
 * The international variant includes ICU i18n library and necessary data
 * allowing to use e.g. `Date.toLocaleString` and `String.localeCompare` that
 * give correct results when using with locales other than en-US. Note that
 * this variant is about 6MiB larger per architecture than default.
 */
def jscFlavor = 'org.webkit:android-jsc:+'



android {
    ndkVersion rootProject.ext.ndkVersion

    buildToolsVersion rootProject.ext.buildToolsVersion
    compileSdk rootProject.ext.compileSdkVersion

    namespace 'com.payforceapp'
    defaultConfig {
        applicationId  'com.payforceapp'
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 80041
        versionName '1.13.0'
    }

    signingConfigs {
        release {
            if (project.hasProperty('MYAPP_UPLOAD_STORE_FILE')) {
                storeFile file(MYAPP_UPLOAD_STORE_FILE)
                storePassword MYAPP_UPLOAD_STORE_PASSWORD
                keyAlias MYAPP_UPLOAD_KEY_ALIAS
                keyPassword MYAPP_UPLOAD_KEY_PASSWORD
            }
        }
    }

    buildTypes {
        debug {
            signingConfig signingConfigs.debug
            resValue 'string', 'CLEVERTAP_ACCOUNT_ID', 'TEST-ACCOUNT-ID'
            resValue 'string', 'CLEVERTAP_TOKEN', 'TEST-DEVELOP'
        }
        release {
            // Caution! In production, you need to generate your own keystore file.
            // see https://reactnative.dev/docs/signed-apk-android.
            // signingConfig signingConfigs.debug

            /* Add the firebaseCrashlytics extension (by default,
            * it's disabled to improve build speeds) and set
            * nativeSymbolUploadEnabled to true along with a pointer to native libs. */

            // firebaseCrashlytics {
            //     nativeSymbolUploadEnabled true
            //     unstrippedNativeLibsDir 'build/intermediates/merged_native_libs/release/out/lib'
            // }
            signingConfig signingConfigs.release
            minifyEnabled enableProguardInReleaseBuilds
            shrinkResources true
            resValue 'string', 'CLEVERTAP_ACCOUNT_ID', CLEVERTAP_ACCOUNT_ID
            resValue 'string', 'CLEVERTAP_TOKEN', CLEVERTAP_TOKEN
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'

            firebaseCrashlytics {
                nativeSymbolUploadEnabled true
                unstrippedNativeLibsDir 'build/intermediates/merged_native_libs/release/out/lib'
            }
        }
    }
}

dependencies {
    implementation('com.facebook.react:react-android')
    implementation 'com.facebook.react:react-native:+'
    implementation platform('com.google.firebase:firebase-bom:30.1.0')
    implementation project(':react-native-fs')
    implementation project(':react-native-push-notification')
    implementation project(':react-native-bootsplash')

    // CleverTap starts
    implementation 'com.clevertap.android:clevertap-android-sdk:5.0.0'
    implementation 'androidx.core:core:1.4.0'
    implementation 'androidx.fragment:fragment:1.3.6'
    implementation 'androidx.appcompat:appcompat:1.3.1'
    implementation 'androidx.recyclerview:recyclerview:1.2.1'
    implementation 'com.google.android.material:material:1.4.0'
    implementation 'com.github.bumptech.glide:glide:4.12.0'
    implementation 'androidx.viewpager:viewpager:1.0.0'
    implementation 'com.android.installreferrer:installreferrer:2.2'

    //Optional ExoPlayer Libraries for Audio/Video Inbox Messages. Audio/Video messages will be dropped without these dependencies
    implementation 'com.google.android.exoplayer:exoplayer:2.17.1'
    implementation 'com.google.android.exoplayer:exoplayer-hls:2.17.1'
    implementation 'com.google.android.exoplayer:exoplayer-ui:2.17.1'
    // CleverTap ends

    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-messaging'

    // Credolab
    // Required for core module
    implementation 'com.google.code.gson:gson:2.8.5'
    implementation 'com.credolab:modular.core:4.8.0'

    implementation 'com.credolab:modular.account:4.0.0'
    implementation 'com.credolab:modular.behavioral:4.4.1'
    implementation 'com.credolab:modular.contact:4.1.0'
    implementation 'com.credolab:modular.sms:4.0.0'

    // FraudForce module
    implementation 'com.credolab:modular.fraudforce:4.0.0'
    implementation 'com.credolab:iovation:5.1.0'



    // Optional: if you want to use logging module
    implementation 'com.credolab:modular.logging:4.0.0'

    // Credolab
    // Required for core module
    implementation 'com.google.code.gson:gson:2.8.5'
    // If your project is on Java
    implementation 'org.jetbrains.kotlin:kotlin-stdlib:1.3.0'

    implementation 'androidx.core:core-splashscreen:1.0.0-beta01'

    if (hermesEnabled.toBoolean()) {
        implementation 'com.facebook.react:hermes-android'
    } else {
        implementation jscFlavor
    }
}

// Run this once to be able to run the application with BUCK
// puts all compile dependencies into folder libs for BUCK to use
task copyDownloadableDepsToLibs(type: Copy) {
    from configurations.implementation
    into 'libs'
}

apply from: file('../../node_modules/@react-native-community/cli-platform-android/native_modules.gradle')
applyNativeModulesAppBuildGradle(project)
