buildscript {
    ext {
        buildToolsVersion = '34.0.0'
        minSdkVersion = 23
        compileSdkVersion = 34
        targetSdkVersion = 34
        ndkVersion = '26.1.10909125'
        kotlinVersion = '1.9.22'
        googleServicesVersion = '4.4.1'

        // reactNativeGradlePluginVersion = "2.1.1"
    }

    repositories {
        google()
        mavenCentral()
        maven { url 'https://www.jitpack.io' }
    
    }

    dependencies {
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
        classpath("com.android.tools.build:gradle")
        classpath("com.google.gms:google-services:$googleServicesVersion")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath 'com.google.firebase:firebase-crashlytics-gradle:3.0.3'
    }
}

allprojects {
    repositories {
        maven {
            url("$rootDir/../node_modules/react-native/android")
        }
        maven {
            url("$rootDir/../node_modules/jsc-android/dist")
        }
        mavenCentral {
            content {
                excludeGroup "com.facebook.react"
            }
        }
        google()
            //    mavenCentral()
        maven { url 'https://www.jitpack.io' }

        // credolab
          maven {
             url "https://dl.cloudsmith.io/KzfLB1RYYNCXNqr9/credolab/proxyen-sdk/maven/"
              }
              // hyperverge
            maven {
             url = "https://s3.ap-south-1.amazonaws.com/hvsdk/android/releases"
              }
    }
}

apply plugin: "com.facebook.react.rootproject"