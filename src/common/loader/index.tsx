import LottieView from 'lottie-react-native';
import React from 'react';
import { Animated, Dimensions, Modal, StyleSheet, View } from 'react-native';

import { colors } from '@/common/styles/colors';
import { PayforceText } from '@/components/PayforceText/PayforceText';

import loadingAnimation from './loadingAnimation.json';

const { height } = Dimensions.get('screen');

type LoaderProps = {
  visible: boolean;
  loadingText?: string;
};

export const FairMoneyBusinessLoader = ({
  visible,
  loadingText,
}: LoaderProps) => (
  <Modal visible={visible}>
    <View style={styles.container}>
      <LottieView
        autoPlay
        loop
        resizeMode="cover"
        source={loadingAnimation}
        style={styles.lottieView}
      />
      {loadingText
        ? (
          <View style={styles.loadingTextContainer}>
            <Animated.Text>
              <PayforceText.Body1Bold color={colors.palette.FMBlack[800]}>
                {loadingText}
              </PayforceText.Body1Bold>
            </Animated.Text>
          </View>
          )
        : null}
    </View>
  </Modal>
);

const styles = StyleSheet.create({
  container: {
    alignContent: 'center',
    backgroundColor: colors.white,
    bottom: 0,
    flex: 1,
    height,
    justifyContent: 'center',
    left: 0,
    position: 'absolute',
    right: 0,
    top: 0,
    zIndex: 999_999_999_999_999,
  },
  loadingTextContainer: {
    alignItems: 'center',
  },
  lottieView: {
    height: 400,
    width: 400,
  },
});
