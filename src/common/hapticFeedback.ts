import ReactNativeHapticFeedback from 'react-native-haptic-feedback';

type THapticMethod =
  | 'impactLight'
  | 'impactMedium'
  | 'impactHeavy'
  | 'rigid'
  | 'soft'
  | 'notificationSuccess'
  | 'notificationWarning'
  | 'notificationError';

const options = {
  enableVibrateFallback: true,
  ignoreAndroidSystemSettings: true,
};

// Trigger haptic feedback
export const runHapticFeedback = (method: THapticMethod = 'impactLight') => {
  ReactNativeHapticFeedback.trigger(method, options);
};
