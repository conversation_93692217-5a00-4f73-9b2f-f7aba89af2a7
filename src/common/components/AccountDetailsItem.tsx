import Clipboard from '@react-native-clipboard/clipboard';
import React from 'react';
import { StyleSheet, View } from 'react-native';
import Toast from 'react-native-toast-message';

import CopyIcon from '@/assets/svg/pf_copy_acc_icon.svg';
import AccountBankIcon from '@/assets/svg/pf_new_acc_bank.svg';
import { colors } from '@/common/styles/colors';
import {
  heightPixel,
  pixelSizeHorizontal,
  pixelSizeVertical,
  widthPixel,
} from '@/common/utilities/normalize';
import { PayforceButton } from '@/components/PayforceButton';
import { PayforceText } from '@/components/PayforceText/PayforceText';

const CopyButton = ({ onPress }: any) => (
  <>
    <PayforceButton onPress={onPress} type="tiny" height={34} style={styles.copyBtn}>
      <PayforceText.Decorative color={colors.palette.FMBlack[500]}>
        Copy
      </PayforceText.Decorative>
      <CopyIcon />
    </PayforceButton>
  </>
);

type Props = {
  field: string;
  value: string | number;
  addCopy?: boolean;
  icon?: React.ReactElement;
  account_name?: string;
};

export const AccountDetailsItem = ({
  field,
  value,
  icon,
  addCopy,
  account_name,
}: Props) => (
  <View style={styles.accountInfo}>
    <View style={styles.container}>
      {icon ? <View style={styles.iconBg}>{icon}</View> : <AccountBankIcon />}
      <View style={styles.textLabel}>
        <PayforceText.Decorative color={colors.palette.FMBlack[500]}>
          {field}
        </PayforceText.Decorative>
        <PayforceText.IconLabel color={colors.palette.FMBlack[800]}>
          {value}
          {' '}
          |
          {account_name}
        </PayforceText.IconLabel>
      </View>
    </View>
    {addCopy && (
      <CopyButton onPress={() => {
        Clipboard.setString(`${value}`);
        Toast.show({
          type: 'success',
          text1: 'Account number copied!',
        });
      }}
      />

    )}
  </View>
);

const styles = StyleSheet.create({
  accountInfo: {
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: 4,
    flexDirection: 'row',
    height: heightPixel(70),
    justifyContent: 'space-between',
    marginBottom: pixelSizeVertical(16),
    overflow: 'hidden',
    paddingRight: pixelSizeHorizontal(12),
  },
  container: {
    flexDirection: 'row',
    paddingLeft: pixelSizeHorizontal(12),
  },
  copyBtn: {
    backgroundColor: colors.palette.FMBlack[100],
    flexDirection: 'row',
    width: widthPixel(75),
  },
  iconBg: {
    alignItems: 'center',
    backgroundColor: colors.palette.FMBlack[900],
    borderRadius: 42 / 2,
    height: heightPixel(42),
    justifyContent: 'center',
    width: heightPixel(42),
  },
  textLabel: {
    marginLeft: pixelSizeHorizontal(8),
  },
});
