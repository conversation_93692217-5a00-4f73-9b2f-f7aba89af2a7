import React from 'react';
import Svg, { Path } from 'react-native-svg';

interface OpenEyeIconProps {
  fill?: string;
  size?: number;
}

export const OpenEyeIcon = ({
  fill = '#4A5E9B',
  size = 20,
}: OpenEyeIconProps) => (
  <Svg width={size} height={size} viewBox="0 0 20 20">
    <Path
      fill={fill}
      d="M18.267 9.667C16.583 5.758 13.417 3.333 10 3.333S3.417 5.758 1.733 9.667a.833.833 0 000 .666c1.684 3.909 4.85 6.334 8.267 6.334s6.583-2.425 8.267-6.334a.835.835 0 000-.666zM10 15c-2.642 0-5.142-1.908-6.583-5C4.858 6.908 7.358 5 10 5c2.642 0 5.142 1.908 6.583 5-1.441 3.092-3.941 5-6.583 5zm0-8.333a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm0 5a1.667 1.667 0 110-3.334 1.667 1.667 0 010 3.334z"
    />
  </Svg>
);
