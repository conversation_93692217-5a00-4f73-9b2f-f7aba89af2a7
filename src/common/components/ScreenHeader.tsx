import React from 'react';
import { StyleProp, StyleSheet, View, ViewStyle } from 'react-native';

import { colors } from '@/common/styles/colors';
import { TopNav } from '@/components/PayForceKYC';
import { PayforceText } from '@/components/PayforceText/PayforceText';

type Props = {
  body?: string;
  header?: string;
  isCancel?: boolean;
  onPress: () => void;
  children?: React.ReactNode;
  style?: StyleProp<ViewStyle>;
  topNavStyle?: StyleProp<ViewStyle>;
  textAreaStyle?: StyleProp<ViewStyle>;
  headerContent?: React.ReactNode;
  rightContent?: React.ReactNode;
  variant?: 'default' | 'row-with-icon';
};

export const ScreenHeader = ({
  style,
  header,
  children,
  onPress,
  body,
  topNavStyle,
  textAreaStyle,
  isCancel = false,
  headerContent,
  rightContent,
  variant = 'default',
}: Props) => {
  const renderTopBar = () => {
    switch (variant) {
      case 'row-with-icon':
        return (
          <View style={style}>
            <View style={[styles.rowHeaderContainer, textAreaStyle]}>
              <TopNav
                isCancel={isCancel}
                onPress={onPress}
                style={[styles.topNav, topNavStyle]}
                rightContent={rightContent}
              />
              <View style={styles.iconSide}>
                {headerContent}
              </View>
              <View style={styles.textSide}>
                {header && (
                  <PayforceText.Body3Medium color={colors.palette.FMBlack[800]}>
                    {header}
                  </PayforceText.Body3Medium>
                )}
                {body && (
                  <PayforceText.Body1
                    color={colors.palette.FMBlack[500]}
                    style={styles.body}
                  >
                    {body}
                  </PayforceText.Body1>
                )}
              </View>
            </View>
          </View>
        );

      case 'default':
      default:
        return (
          <View style={style}>
            <TopNav
              isCancel={isCancel}
              onPress={onPress}
              style={[styles.topNav, topNavStyle]}
              rightContent={rightContent}
            />
            <View style={[styles.textArea, textAreaStyle]}>
              {headerContent
                ? (
                  <>{headerContent}</>
                  )
                : (
                  <PayforceText.Body3Medium color={colors.palette.FMBlack[800]}>
                    {header}
                  </PayforceText.Body3Medium>
                  )}
              {children}
              {body
                ? (
                  <PayforceText.Body1
                    color={colors.palette.FMBlack[500]}
                    style={styles.body}
                  >
                    {body}
                  </PayforceText.Body1>
                  )
                : null}
            </View>
          </View>
        );
    }
  };

  return <View>{renderTopBar()}</View>;
};

const styles = StyleSheet.create({
  body: {
    marginTop: 8,
  },
  iconSide: {
    marginRight: 12,
  },
  rowHeaderContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    // marginTop: 24,
  },
  textArea: {
    marginTop: 24,
  },
  textSide: {
    flex: 1,
  },
  topNav: {
    margin: 0,
  },
});
