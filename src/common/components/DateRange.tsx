import React, { useState } from 'react';
import { StyleSheet, View } from 'react-native';

import { colors } from '@/common/styles/colors';
import {
  heightPixel,
  pixelSizeHorizontal,
  pixelSizeVertical,
} from '@/common/utilities/normalize';
import { ListSeparator } from '@/components/ListSeparator';
import { PayforceButton } from '@/components/PayforceButton';
import PayforceMultiDatePicker, {
  DateRangeProps,
} from '@/components/PayforceDatepicker/PayforceMultiDatePicker';
import { PayforceText } from '@/components/PayforceText/PayforceText';
import { formatToDayMonthTime } from '@/utils/formatDates';

type DateRangePickerProps = {
  onContinue: () => void;
  onChangeDateRange: (range: DateRangeProps) => void;
  defaultDateRange?: {
    startDate?: string;
    endDate?: string;
  };
  title?: string;
  onContinueButtonText?: string;
  titlePosition?: 'center' | 'left' | 'right';
};

export const DateRange = ({
  onContinue,
  defaultDateRange = {
    startDate: undefined,
    endDate: undefined,
  },
  title = 'SELECT RANGE',
  onContinueButtonText = 'Continue',
  onChangeDateRange = () => {},
  titlePosition = 'center',
}: DateRangePickerProps) => {
  const [range, setRange] = useState<DateRangeProps>(defaultDateRange);

  const onChange = (_range: DateRangeProps) => {
    let finalRange = {
      startDate: '',
      endDate: '',
    };
    if (_range.startDate) {
      finalRange = {
        ...finalRange,
        startDate: _range.startDate,
      };
    }

    if (_range.endDate) {
      finalRange = {
        ...finalRange,
        endDate: _range.endDate,
      };
    }

    setRange(finalRange);

    onChangeDateRange(_range);
  };

  const onProceed = () => {
    onContinue();
  };

  const textStyle = getTextStyle({ titlePosition });

  const getRange = (filter: 'startDate' | 'endDate') => {
    let dateRange = {
      startDate: '',
      endDate: '',
    };
    if (range.startDate) {
      dateRange = {
        ...dateRange,
        startDate: range.startDate,
      };
    }

    if (range.endDate) {
      dateRange = {
        ...dateRange,
        endDate: range.endDate,
      };
    }

    return dateRange[filter] || '';
  };

  return (
    <View style={styles.container}>
      <PayforceText.Body2 style={textStyle.text} color="form">
        {title}
      </PayforceText.Body2>
      <ListSeparator height={25} />
      <View style={styles.flexContainer}>
        <View style={styles.selectedRangeContainer}>
          <PayforceText.TinyText color="form">From</PayforceText.TinyText>
          <PayforceText.Body1 color="label">
            {formatToDayMonthTime(getRange('startDate'), false)}
          </PayforceText.Body1>
        </View>
        <View style={styles.selectedRangeContainer}>
          <PayforceText.TinyText color="form">To</PayforceText.TinyText>
          <PayforceText.Body1 color="label">
            {formatToDayMonthTime(getRange('endDate'), false)}
          </PayforceText.Body1>
        </View>
      </View>
      <ListSeparator height={15} />
      <View style={styles.calendarContainer}>
        <PayforceMultiDatePicker setRange={onChange} range={range} />
      </View>
      <ListSeparator height={25} />
      {range.startDate && range.endDate
        ? (
          <PayforceButton type="small" onPress={onProceed}>
            <PayforceText.Body1Medium color={colors.white}>
              {onContinueButtonText}
            </PayforceText.Body1Medium>
          </PayforceButton>
          )
        : undefined}
    </View>
  );
};

const getTextStyle = ({
  titlePosition = 'center',
}: {
  titlePosition: 'center' | 'left' | 'right';
}) => ({
  text: {
    textAlign: titlePosition,
  },
});

const styles = StyleSheet.create({
  calendarContainer: {
    borderColor: colors.palette.FMBlack[100],
    borderWidth: 1,
    paddingHorizontal: pixelSizeHorizontal(24),
    paddingVertical: pixelSizeVertical(20),
  },
  container: {
    paddingHorizontal: pixelSizeHorizontal(15),
  },
  flexContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  selectedRangeContainer: {
    borderColor: colors.palette.FMBlack[200],
    borderWidth: 1,
    height: heightPixel(60),
    paddingHorizontal: pixelSizeHorizontal(10),
    paddingVertical: pixelSizeVertical(10),
    width: '45%',
  },
});
