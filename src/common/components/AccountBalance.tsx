import React, { useMemo, useState } from 'react';
import { StyleSheet, TouchableOpacity, View, ViewProps } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';

import { colors } from '@/common/styles/colors';
import { PayforceText } from '@/components/PayforceText/PayforceText';
import { convertCurrency } from '@/functions/functions';

import { OpenEyeIcon } from './OpenEyeIcon';

type AccountBalanceProps = {
  gradientColors?: (string | number)[];
  label?: string;
  icon?: JSX.Element;
  currency?: '₦' | '$' | '£';
  amount?: number;
  showBalance?: boolean;
  children?: React.ReactNode;
} & ViewProps;

const getFormattedAmount = (
  amount: number,
  currency?: AccountBalanceProps['currency'],
) => `${convertCurrency(amount, currency).split('₦')[1]}`;

const MemoizedLinearGradient = React.memo(LinearGradient);

export const AccountBalance = ({
  amount = 0,
  style,
  currency,
  label,
  children,
  gradientColors = [],
  showBalance = true,
  ...rest
}: AccountBalanceProps) => {
  const [viewBalance, setViewBalance] = useState(showBalance);

  const finalAmount = useMemo(
    () => getFormattedAmount(amount, currency),
    [amount],
  );

  return (
    <MemoizedLinearGradient
      colors={gradientColors}
      angle={45}
      style={[styles.parentContainer, style]}
      {...rest}
    >
      <View style={styles.zeroBalSubContainer}>
        <PayforceText.Decorative color={colors.white}>
          {label}
        </PayforceText.Decorative>
        <TouchableOpacity
          style={styles.zeroHideBtn}
          onPress={() => setViewBalance(!viewBalance)}
        >
          <OpenEyeIcon fill={colors.white} />
        </TouchableOpacity>
      </View>
      <View style={styles.amountPanel}>
        <PayforceText.Body2 color={colors.white}>{currency}</PayforceText.Body2>
        <PayforceText.Body3Medium color={colors.white}>
          {viewBalance ? finalAmount : '********'}
        </PayforceText.Body3Medium>
      </View>
      <View>{children}</View>
    </MemoizedLinearGradient>
  );
};

const styles = StyleSheet.create({
  amountPanel: {
    flexDirection: 'row',
    marginTop: 2,
  },
  parentContainer: {
    borderRadius: 5,
    padding: 10,
  },
  zeroBalSubContainer: {
    alignItems: 'flex-start',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },

  zeroHideBtn: {
    borderColor: colors.palette.FMLightBlue[400],
    borderRadius: 100,
    borderWidth: 1,
  },
});
