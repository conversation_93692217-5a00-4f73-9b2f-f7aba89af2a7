import { GeolocationResponse } from '@react-native-community/geolocation';
import * as React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Linking, StyleSheet, View } from 'react-native';
import { check, PermissionStatus, request, RESULTS } from 'react-native-permissions';

import { Assets } from '@/asset/index';
import { constants } from '@/common/constants';
import { colors } from '@/common/styles/colors';
import { pixelSizeVertical } from '@/common/utilities/normalize';
import CustomBottomSheet from '@/components/BottomSheet/BottomSheet';
import { useCreateBottomSheetModal } from '@/components/BottomSheet/helper';
import { ListSeparator } from '@/components/ListSeparator';
import { PayforceButton } from '@/components/PayforceButton';
import { PayforceText } from '@/components/PayforceText/PayforceText';
import { locationOptionSheet, locationPermission } from '@/functions/functions';
import { GeoLocationService } from '@/functions/location-service';
import { navigationRef } from '@/routes/auth/constants';
import { useAppDispatch } from '@/store/hooks';
import { setUserLocation } from '@/store/user/agentSlice';
import { log } from '@/utils/log';

interface PermissionState {
  permissionStatus: PermissionStatus | null;
  isLoading: boolean;
  userDismiss: boolean;
  checkingPermission: boolean;
}

const initialState: PermissionState = {
  permissionStatus: null,
  isLoading: false,
  userDismiss: false,
  checkingPermission: true,
};

const LocationBottomSheet: React.FC = () => {
  const routeName = navigationRef.getCurrentRoute?.()?.name;
  const [sheetRef, openSheet, closeSheet] = useCreateBottomSheetModal('location_bottom_sheet');
  const dispatch = useAppDispatch();

  const [state, setState] = React.useState<PermissionState>(initialState);

  const { checkingPermission, permissionStatus, isLoading, userDismiss } = state;

  const updateState = React.useCallback((newState: Partial<PermissionState>) => {
    setState((prevState) => ({ ...prevState, ...newState }));
  }, []);

  const checkLocationPermission = React.useCallback(async () => {
    if (locationPermission) {
      const status = await check(locationPermission);
      updateState({ permissionStatus: status, checkingPermission: false });
    }
  }, []);

  const locationMutation = React.useCallback(async () => {
    if (constants.IS_ANDROID) {
      await GeoLocationService.enableLocationServices();
    }

    const geoLocationResult = await GeoLocationService.getCurrentPosition();

    if (geoLocationResult.success && geoLocationResult.position) {
      const position = (geoLocationResult.position?.coords ?? {} as GeolocationResponse);
      const { latitude = 0, longitude = 0 } = position ?? {};
      dispatch(setUserLocation({ latitude: `${latitude}`, longitude: `${longitude}` }));
      log({ name: `::[Location Access Granted]:::${latitude}---${longitude}--${routeName}` });
      closeSheet();
      setState(initialState);
    } else {
      dispatch(setUserLocation({ latitude: '0.00', longitude: '0.00' }));
      log({ name: `::[Location Access Not Granted]:::${routeName}` });
      closeSheet();
      updateState({ userDismiss: true });
    }
  }, [dispatch, closeSheet]);

  React.useEffect(() => {
    checkLocationPermission();
  }, []);

  React.useEffect(() => {
    if (userDismiss) return;
    if (permissionStatus !== 'granted') {
      openSheet();
    } else {
      closeSheet();
    }
  }, [
    permissionStatus,
    openSheet,
    closeSheet,
  ]);

  const openAppSettings = React.useCallback(() => {
    if (constants.IS_ANDROID) {
      Linking.openSettings();
    } else {
      Linking.openURL('app-settings:');
    }
    BackHandler.exitApp();
  }, []);

  const handleLocationAccess = React.useCallback(async () => {
    if (permissionStatus === 'granted') {
      sheetRef.current?.dismiss();
      closeSheet();
      updateState(initialState);
      return; // Early Return
    }
    updateState({ isLoading: true });
    try {
      if (locationPermission) {
        const result = await request(locationPermission);
        if (result === RESULTS.GRANTED) {
          updateState({ permissionStatus: result });
          await locationMutation();
        } else if (result === RESULTS.BLOCKED) {
          Alert.alert(
            'FairMoney Business Location Permission',
            'To enhance your experience and security of your account, we need access to your location. Your data is protected',
            [
              {
                text: 'Go to Settings',
                onPress: openAppSettings,
              },
              {
                text: 'Cancel',
                onPress: closeSheet,
              },
            ],
          );
        }
      }
    } catch (error) {
      log({ name: ':::[Location Access Error]:::', error });
      dispatch(setUserLocation({ latitude: '0.00', longitude: '0.00' }));
    } finally {
      updateState({ isLoading: false });
      closeSheet();
    }
  }, [dispatch, openAppSettings, locationMutation, closeSheet, updateState]);

  if (permissionStatus === 'granted' || checkingPermission) return null;

  return (
    <CustomBottomSheet
      ref={sheetRef}
      index={2}
      handleIndicatorStyle={styles.indicatorStyle}
      onChange={(index: number) => {
        if (index < 0) {
          updateState({ userDismiss: true });
          closeSheet();
        }
      }}
      {...locationOptionSheet}
    >
      <View style={styles.root}>
        <ListSeparator height={28} />
        <View style={styles.contents}>
          <Assets.GeneralIcons.LocationGlassIcon />
          <ListSeparator height={14} />
          <PayforceText.Body1Medium color={colors.palette.FMBlack[900]} style={styles.textAlign}>
            {ContentMapping.title}
          </PayforceText.Body1Medium>
          <ListSeparator height={8} />
          <PayforceText.Subheader color="form" style={styles.textAlign}>
            {ContentMapping.description}
          </PayforceText.Subheader>
        </View>
        <ListSeparator height={30} />
        <View style={styles.buttonContainer}>
          <PayforceButton
            title="Continue"
            onPress={handleLocationAccess}
            type="small"
            isLoading={isLoading}
          />
        </View>
      </View>
    </CustomBottomSheet>
  );
};

export { LocationBottomSheet };

const styles = StyleSheet.create({
  buttonContainer: {
    flex: 0.5,
    justifyContent: 'flex-end',
    width: '100%',
  },
  contents: {
    alignContent: 'center',
    alignItems: 'center',
    justifyContent: 'center',
  },
  indicatorStyle: {
    display: 'none',
    paddingBottom: pixelSizeVertical(25),
  },
  root: {
    flex: 1,
    paddingHorizontal: pixelSizeVertical(16),
  },
  textAlign: {
    alignItems: 'center',
    textAlign: 'center',
  },
});

const ContentMapping = {
  title: 'Location for Improved Experience and Security',
  description: 'We use your location data to enhance your experience by providing tailored offers based on your region and to help protect your account from fraud. Your location data is securely processed and used in accordance with our privacy policy.',
  buttonTwoTitle: 'Not now',
  cancel: false,
};
