import React from 'react';
import { SvgXml, XmlProps } from 'react-native-svg';

import { colors } from '@/common/styles/colors';

export const CTAArrowIcon = ({ fill = colors.palette.FMBlue[800], ...restProps }: Omit<XmlProps, 'xml'>) => {
  const xml = `
      <svg
      xmlns="http://www.w3.org/2000/svg"
      width="21"
      height="20"
      fill="none"
      viewBox="0 0 21 20"
    >
      <path
        fill="${fill.toString()}"
        d="M15.433 9.683a.832.832 0 00-.175-.275l-4.166-4.166a.837.837 0 00-1.184 1.183l2.75 2.742H6.333a.833.833 0 000 1.666h6.325l-2.75 2.742a.833.833 0 000 1.183.834.834 0 001.184 0l4.166-4.166a.832.832 0 00.175-.275.833.833 0 000-.634z"
      ></path>
    </svg>
      `;

  return <SvgXml xml={xml} {...restProps} />;
};
