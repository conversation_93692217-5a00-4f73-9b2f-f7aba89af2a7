import React from 'react';
import Animated, {
  Easing,
  interpolate,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';
import { Defs, LinearGradient, Rect, Stop, Svg } from 'react-native-svg';

const AnimatedRect = Animated.createAnimatedComponent(Rect);

const StartupGlow = () => {
  const opacity = useSharedValue(0);
  opacity.value = withRepeat(
    withTiming(1, {
      duration: 5000,
      easing: Easing.ease,
    }),
    -1,
    true,
  );

  const style = useAnimatedStyle(() => {
    const topOpacity = interpolate(
      opacity.value,
      [1, 0.2, 0.8, 1],
      [0, 0, 1, 1],
    );
    return {
      opacity: topOpacity,
    };
  }, []);

  const opacityRect2 = useSharedValue(0);
  opacityRect2.value = withRepeat(
    withTiming(1, {
      duration: 3000,
      easing: Easing.ease,
    }),
    -1,
    true,
  );

  const styleRect2 = useAnimatedStyle(
    () => ({
      opacity: opacityRect2.value,
    }),
    [],
  );

  return (
    <Animated.View>
      <Svg width={790} height={855} viewBox="0 0 790 855" fill="none">
        <AnimatedRect
          opacity={0.5}
          x={1}
          y={191}
          width={440}
          height={663}
          rx={89}
          stroke="url(#paint0_linear_973_47905)"
          strokeWidth={2}
          animatedProps={style}
        />
        <AnimatedRect
          opacity={0.5}
          x={349}
          y={1}
          width={440}
          height={663}
          rx={89}
          stroke="url(#paint1_linear_973_47905)"
          strokeWidth={2}
          animatedProps={styleRect2}
        />
        <Defs>
          <LinearGradient
            id="paint0_linear_973_47905"
            x1={7.71746}
            y1={434.815}
            x2={411.072}
            y2={807.363}
            gradientUnits="userSpaceOnUse"
          >
            <Stop stopColor="white" />
            <Stop offset={1} stopColor="white" stopOpacity={0} />
          </LinearGradient>
          <LinearGradient
            id="paint1_linear_973_47905"
            x1={355.717}
            y1={244.815}
            x2={759.072}
            y2={617.363}
            gradientUnits="userSpaceOnUse"
          >
            <Stop stopColor="white" />
            <Stop offset={1} stopColor="white" stopOpacity={0} />
          </LinearGradient>
        </Defs>
      </Svg>
    </Animated.View>
  );
};

export default StartupGlow;
