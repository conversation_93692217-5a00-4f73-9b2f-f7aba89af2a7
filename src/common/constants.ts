import { Dimensions, Platform } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import StaticSafeAreaInsets from 'react-native-static-safe-area-insets';

// const APP_VERSION = require('../../package.json').version as string;
const NATIVE_APP_VERSION = DeviceInfo.getVersion();
const NATIVE_APP_BUILD_NUMBER = DeviceInfo.getBuildNumber();

export const constants = {
  channel: 'fair_money_business_app',
  app_version: '1.14.0',
  native_app_version: NATIVE_APP_VERSION,
  native_app_build_number: NATIVE_APP_BUILD_NUMBER,
  full_release_name: `${NATIVE_APP_VERSION}+${Platform.select({
    ios: 'bundleVersion',
    android: 'versionCode',
  })}.${NATIVE_APP_BUILD_NUMBER}`,
  version_parameter: `${NATIVE_APP_BUILD_NUMBER} (${NATIVE_APP_VERSION})`,
  dim: {
    ...Dimensions.get('window'),
    height: Platform.select<number>({
      android:
        Dimensions.get('screen').height
        - StaticSafeAreaInsets.safeAreaInsetsBottom,
      ios: Dimensions.get('window').height,
    }) as number,
    contentSpacing: 20,
    edgeSpacing: 10,
  },
  emptyArray: Object.freeze([]) as readonly unknown[],
  emptyObject: Object.freeze({}) as readonly unknown[],
  IS_ANDROID: Platform.OS === 'android',
  IS_IOS: Platform.OS === 'ios',
};

export const displayFlag: 'flex' | undefined = Platform.select({
  android: undefined,
  ios: 'flex',
});

export const headers = {
  X_REQUEST_LOCK: 'I8uG2k0Fax31vG',
};
