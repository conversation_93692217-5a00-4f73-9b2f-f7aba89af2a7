import { MMKV } from 'react-native-mmkv';

/**
 * This doesn't have to be called in order to init the storage.
 * This is a bug workaround for iOS on MMKV.
 * On iOS when calling storage.setString (or similar) for the first time.
 * MMKV will throw an error that on the global object it wasn't able to find
 * the function to initialize.
 */
export const earlyInitStorage = (): void => {
  storage.getAllKeys(); // invoke constructor
};
export const storage = new MMKV();
