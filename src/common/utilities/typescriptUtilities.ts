export const notEmpty = <TValue>(
  value: TValue | null | undefined,
): value is TValue => value != null;

export const isNotEmptyString = (
  value: string | undefined | null,
): value is string => value != null && value.length > 0;

/**
 * Take a type and make part of it optional.
 *
 * @example
 * ```typescript
 * type Foo = {
 *   prop1: number;
 *   prop2: string;
 * };
 *
 * type FooDerivate = PartialBy<Foo, "prop2">
 * // Resulting type shape:
 * {
 *   prop1: number;
 *   prop2?: string;
 * }
 * ```
 */
export type PartialBy<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/**
 * Testing an object type for properties and narrowing down the type.
 * {@link https://fettblog.eu/typescript-hasownproperty/}
 */
export const hasOwnProperty = <X extends object, Y extends PropertyKey>(
  obj: X,
  prop: Y,
): obj is X & Record<Y, unknown> => Object.prototype.hasOwnProperty.call(obj, prop);

/**
 * Requires given properties on an object.
 * {@link https://lorefnon.tech/2020/02/02/conditionally-making-optional-properties-mandatory-in-typescript/}
 */
export type MandateProps<T extends object, K extends keyof T> = Omit<T, K> & {
  [MK in K]-?: NonNullable<T[MK]>;
};
