export const sumWorklet = (numbers?: number[]): number => {
  'worklet';

  if (numbers == null || numbers.length === 0) return 0;
  return numbers.reduce((prev, cur) => prev + cur);
};
export const sum = (numbers?: number[]): number => {
  if (numbers == null || numbers.length === 0) return 0;
  return numbers.reduce((prev, cur) => prev + cur);
};

export const getClosestWorklet = (input: number, numbers: number[]): number => {
  'worklet';

  return numbers.reduce((prev, curr) =>
    Math.abs(curr - input) < Math.abs(prev - input) ? curr : prev);
};

export const getHighestNumber = (numbers: number[]): number =>
  numbers.reduce((p, v) => (p > v ? p : v), 0);

// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math/random#getting_a_random_integer_between_two_values_inclusive
// export const getRandomIntInclusive = (min: number, max: number): number => {
//   min = Math.ceil(min);
//   max = Math.floor(max);
//   return Math.floor(Math.random() * (max - min + 1) + min);
// };

// // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math/random#getting_a_random_integer_between_two_values_inclusive
// export const getRandomIntInclusiveWorklet = (min: number, max: number): number => {
//   'worklet';

//   min = Math.ceil(min);
//   max = Math.floor(max);
//   return Math.floor(Math.random() * (max - min + 1) + min);
// };

export const compareVersions = (v1: string, v2: string): number => {
  if (!v1 || !v2) return 0; // Returning 0 when one of the versions is missing.

  const version1Parts = v1.split('.');
  const version2Parts = v2.split('.');
  const longestLength = Math.max(version1Parts.length, version2Parts.length);

  // eslint-disable-next-line no-plusplus
  for (let i = 0; i < longestLength; i++) {
    const part1 = version1Parts[i] || 0;
    const part2 = version2Parts[i] || 0;

    if (part1 !== part2) {
      return part1 > part2 ? 1 : -1;
    }
  }

  return 0;
};
