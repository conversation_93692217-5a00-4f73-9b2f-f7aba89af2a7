import React, { useCallback, useRef } from 'react';
import { TextInput } from 'react-native';

/**
 * Convenience hook that creates a ref for a TextInput
 * and returns a memoized focus function, as well as a
 * onChangeText function and a reference to the text
 * on this TextInput.
 */
export const useTextInputRef = <T extends TextInput>(): [
  React.RefObject<T>,
  () => void,
  (text: string) => void,
  React.RefObject<string | undefined>,
] => {
  const ref = useRef<T>(null);
  const textRef = useRef<string>();

  const focus = useCallback(() => {
    ref.current?.focus();
  }, []);

  const onChangeText = useCallback((text: string) => {
    textRef.current = text;
  }, []);

  return [ref, focus, onChangeText, textRef];
};
