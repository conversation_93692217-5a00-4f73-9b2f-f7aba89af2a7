import { useMutation } from '@tanstack/react-query';
import * as React from 'react';
import Toast from 'react-native-toast-message';
import { useSelector } from 'react-redux';

import { constants } from '@/common/constants';
import { isError } from '@/common/utilities/errorUtilities';
import { api, pickErrorMessage, pickResult } from '@/functions/api';
import { getDeviceIpAddress } from '@/functions/functions';
import { useAppSelector } from '@/store/hooks';
import { selectAgent, selectUserLocation } from '@/store/user/selectors';

import useDeviceInformation from '../../hooks/useDeviceInformation';

const config = {
  headers: {
    USER_KEY_SIG: 'a8f4b6e2d1c93e5b7a2f0d8c4b9e71c5',
    'App-Id': 'xH1pQ3lVXNT0wsUqGJ/s/vGCAKuy9uNR',
  },
};

// type useAuthArgs = {
//   username: string;
//   otp: string;
//   pin?: string;
//   is_trans_pin?: boolean;
//   setIsLoading: (val: boolean) => void;
//   data: { username: string; password: string };
//   setOTPError: ({ msg, err }: { msg: string; err: boolean }) => void;
// };

type otpProps = {
  code: string;
  registeredDevice: boolean;
  identifier: string;
};
type otpOnMultpleDeviceProps = {
  code: string;
  registeredDevice: boolean;
  identifier: string;
  pin: string;
  is_trans_pin: boolean;
};

export const useRequestOTP = () => {
  const { latitude, longitude } = useAppSelector(selectUserLocation);
  return useMutation(
    async (payload: { channel: string; type: string; identifier: string }) => {
      const ipAddress = await getDeviceIpAddress();
      const response = await api.get(
        `/au/mobile/agent/request-otp?identifier=${payload.identifier}&type=${payload.type
        }&channel=${payload.channel
        }&ip_address=${ipAddress}&latitude=${latitude.toString()}&longitude=${longitude.toString()}`,
      );
      return response.data;
    },
  );
};

export const useRequestMultipleDeviceOTP = () => {
  const agentDetails = useSelector(selectAgent);
  return useMutation(async () => {
    const payload = {
      identifier: agentDetails.agent_username,
      type: 11,
    };
    const resp = await api
      .get(
        `/au/mobile/agent/request-otp?type=${payload.type}&identifier=${payload.identifier}`,
        config,
      )
      .then(pickResult<ValidateOTPResponse>, pickErrorMessage);

    const { error_message, response_message } = resp;

    if (error_message) {
      Toast.show({
        type: 'error',
        autoHide: true,
        text1: error_message,
      });
    } else {
      Toast.show({
        type: 'success',
        autoHide: true,
        text1: response_message,
      });
    }
  });
};

export const useValidateOTP = () => {
  const { userDeviceInfo } = useDeviceInformation();
  return useMutation(async (data: otpProps) => {
    const payload = {
      identifier: data.identifier
        ? data.identifier
        : ``,
      otp: data.code,
      is_device_imei_otp: data.registeredDevice,
      device_imei: userDeviceInfo.deviceId,
      channel: constants.channel,
    };

    const response = await api.post(
      `/au/mobile/agent/ValidateUserOtp`,
      payload,
      config,
    );
    return response.data;
  });
};

export const useValidateMultipleDeviceOTP = () => {
  const { userDeviceInfo } = useDeviceInformation();
  const agentDetails = useSelector(selectAgent);
  return useMutation(async (data: otpOnMultpleDeviceProps) => {
    const payload = {
      otp: data.code,
      pin: data.pin,
      is_trans_pin: data?.is_trans_pin || false,
      channel: constants.channel,
      identifier: data.identifier
        ? data.identifier
        : agentDetails.agent_username,
      is_device_imei_otp: data.registeredDevice,
      device_imei: userDeviceInfo.deviceId,
    };
    const response = await api.post(
      `/au/mobile/agent/ValidateUserOtp`,
      payload,
      config,
    );
    return response.data;
  });
};

type ValidateOTPResponse = {
  error_message: string;
  is_successful: boolean;
  response_message: string;
  token: null;
  response_data: number;
};

export const useAuthCodeVerification = ({
  setOTPError,
  callback,
}: {
  setOTPError: React.Dispatch<
    React.SetStateAction<{
      err: boolean;
      msg: string | undefined;
    }>
  >;
  callback: (d: number) => void;
}) =>
  useMutation({
    mutationKey: ['verify-device-onlogin'],
    mutationFn: validateUserOTP,
    onSuccess: (data: ValidateOTPResponse) => {
      const { error_message, is_successful, response_data } = data;
      if (typeof error_message === 'string' && error_message.length > 0) {
        setOTPError({ msg: error_message, err: true });
      } else if (is_successful) {
        callback(response_data);
        setOTPError({ msg: '', err: false });
      }
    },
    onError: (error) => {
      if (isError(error)) {
        setOTPError({ msg: error.message, err: true });
      }
    },
  });

const validateUserOTP = async (body: {
  otp: string;
  pin?: string;
  is_trans_pin: boolean;
  username: string;
  device_imei: string;
}) => {
  const payload = {
    otp: body.otp,
    pin: body?.pin,
    is_trans_pin: body.is_trans_pin || false,
    channel: constants.channel,
    identifier: body.username,
    is_device_imei_otp: true,
    device_imei: body.device_imei,
  };
  return api
    .post(`/au/mobile/agent/ValidateUserOtp`, payload, config)
    .then(pickResult, pickErrorMessage);
};
