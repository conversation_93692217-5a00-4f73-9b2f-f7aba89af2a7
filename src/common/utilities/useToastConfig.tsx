import React from 'react';
import { StyleSheet, View } from 'react-native';
import {
  BaseToast,
  BaseToastProps,
  ErrorToast,
  ToastConfig,
} from 'react-native-toast-message';

import { Assets, CopyCheck } from '@/asset/index';
import { colors } from '@/common/styles/colors';
import { FONT_DMSANS } from '@/common/styles/fonts';
import {
  heightPixel,
  normalize,
  pixelSizeHorizontal,
  pixelSizeVertical,
  widthPixel,
} from '@/common/utilities/normalize';
import { ListSeparator } from '@/components/ListSeparator';
import { PayforceText } from '@/components/PayforceText/PayforceText';

export const useToastConfig = () => {
  const toastConfig: ToastConfig = {
    copied: ({ text1 }) => (
      <View style={styles.container}>
        <CopyCheck />
        <ListSeparator width={4} />
        <PayforceText.Decorative color={colors.palette.FMBlack[500]}>
          {text1}
        </PayforceText.Decorative>
      </View>
    ),
    success: (props: React.JSX.IntrinsicAttributes & BaseToastProps) => (
      <BaseToast
        {...props}
        style={{ backgroundColor: colors.palette.FMGreen[50] }}
        contentContainerStyle={{ paddingHorizontal: normalize(15) }}
        text1Style={styles.success}
      />
    ),
    /*
      Overwrite 'error' type,
      by modifying the existing `ErrorToast` component
    */
    error: (props: React.JSX.IntrinsicAttributes & BaseToastProps) => (
      <ErrorToast
        {...props}
        style={styles.error}
        text1Style={{
          fontSize: normalize(14),
        }}
        text2Style={{
          fontSize: normalize(12),
        }}
      />
    ),
    session: () => (
      <View style={styles.sessionExpiredWrapper}>
        <Assets.OnboardingIcons.FairMoneyWhiteLogoIcon />
        <PayforceText.Body3
          color={colors.white}
          style={styles.sessionExpiredText}
        >
          Session expired. Please log in again to continue. 🔐
        </PayforceText.Body3>
      </View>
    ),
    successDouble: ({ text1, text2 }) => (
      <View style={styles.toast}>
        <PayforceText.Body2 color={colors.palette.FMBlack[700]}>
          {text1}
        </PayforceText.Body2>
        <ListSeparator height={4} />
        <PayforceText.Decorative color={colors.palette.FMBlack[700]}>
          {text2}
        </PayforceText.Decorative>
      </View>
    ),
  };

  return toastConfig;
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    backgroundColor: colors.white,
    borderColor: colors.palette.FMGreen[300],
    borderRadius: 8,
    borderWidth: 1,
    flexDirection: 'row',
    height: heightPixel(38),
    justifyContent: 'center',
    width: widthPixel(179),
  },
  error: {
    backgroundColor: colors.palette.error[300],
  },
  sessionExpiredText: {
    flex: 1,
    flexWrap: 'wrap',
  },
  sessionExpiredWrapper: {
    alignItems: 'center',
    backgroundColor: colors.palette.FMBlack[800],
    borderRadius: normalize(8),
    flexDirection: 'row',
    flexWrap: 'nowrap',
    gap: 10,
    paddingHorizontal: 12,
    paddingVertical: 8,
    width: '85%',
  },
  success: {
    color: colors.palette.FMBlack[800],
    fontFamily: FONT_DMSANS,
    fontSize: normalize(15),
    fontWeight: '400',
  },
  toast: {
    alignSelf: 'center',
    backgroundColor: colors.palette.FMGreen[50],
    borderRadius: 4,
    height: heightPixel(84),
    paddingHorizontal: pixelSizeHorizontal(16),
    paddingVertical: pixelSizeVertical(14),
    width: '100%',
  },
});
