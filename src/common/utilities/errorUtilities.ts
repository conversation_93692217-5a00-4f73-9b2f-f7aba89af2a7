import { AxiosError } from 'axios';
import { AxiosExtendedError } from 'src/types/axiosError';

export function isAxiosError(
  error: any,
): error is AxiosError<AxiosExtendedError> {
  return error.isAxiosError && error.response?.data;
}

export function isError(error: unknown): error is Error {
  return (
    error instanceof Error
    || (typeof error === 'object' && error !== null && 'message' in error)
  );
}
