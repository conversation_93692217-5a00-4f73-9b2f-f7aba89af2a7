import React, { memo, useEffect, useRef, useState } from 'react';
import { AppState, AppStateStatus, StyleSheet, View } from 'react-native';

import { colors } from '@/common/styles/colors';
import { scaleHorizontal, scaleVertical } from '@/common/styles/scale';
import { PayforceText } from '@/components/PayforceText/PayforceText';

/**
 * ? Local & Shared Imports
 */
import { fontPixel } from './normalize';

type ResendOTPTimerProps = {
  seconds?: number;
  handleResendOTP: () => void;
};

const ResendOTPTimer = ({
  seconds = 60,
  handleResendOTP,
}: ResendOTPTimerProps) => {
  const [countdown, setCountdown] = useState(seconds);
  const endTimeRef = useRef<number | null>(null);
  const appState = useRef(AppState.currentState);
  const intervalRef = useRef<ReturnType<typeof setInterval> | null>(null);

  const resetTimer = () => {
    handleResendOTP();
    setCountdown(seconds);
    // Set the end time when resetting
    endTimeRef.current = Date.now() + seconds * 1000;
    startInterval();
  };

  const startInterval = () => {
    // Clear any existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Only start interval if we have a valid end time and countdown is not zero
    if (endTimeRef.current && countdown > 0) {
      intervalRef.current = setInterval(() => {
        const now = Date.now();
        const remaining = endTimeRef.current
          ? Math.max(0, Math.floor((endTimeRef.current - now) / 1000))
          : 0;

        setCountdown(remaining);

        if (remaining === 0 && intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
      }, 1000);
    }
  };

  useEffect(() => {
    // Initialize the end time when component mounts
    if (endTimeRef.current === null && countdown > 0) {
      endTimeRef.current = Date.now() + countdown * 1000;
    }

    // Handle app state changes
    const subscription = AppState.addEventListener(
      'change',
      (nextAppState: AppStateStatus) => {
        if (
          appState.current.match(/inactive|background/)
          && nextAppState === 'active'
        ) {
          // App has come to the foreground
          if (endTimeRef.current) {
            // Calculate the new countdown based on the actual elapsed time
            const now = Date.now();
            const remaining = Math.max(
              0,
              Math.floor((endTimeRef.current - now) / 1000),
            );
            setCountdown(remaining);
          }
        }
        appState.current = nextAppState;
      },
    );

    // Start the interval
    startInterval();

    // Cleanup function
    return () => {
      subscription.remove();
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, []);

  useEffect(() => {
    // If countdown reaches zero, clean up
    if (countdown === 0) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }
  }, [countdown]);

  return (
    <View style={styles.container}>
      <PayforceText.Subheader color={colors.palette.FMBlack[500]}>
        Didn&apos;t receive a code?
      </PayforceText.Subheader>
      {countdown !== 0
        ? (
          <PayforceText.Subheader
            color={colors.palette.FMBlack[500]}
            style={styles.subheader}
          >
            (
            {countdown}
            )sec
          </PayforceText.Subheader>
          )
        : (
          <PayforceText.Button
            color={colors.black}
            onPress={resetTimer}
            style={styles.button}
          >
            Resend Code
          </PayforceText.Button>
          )}
    </View>
  );
};

const styles = StyleSheet.create({
  button: {
    fontSize: fontPixel(14),
    lineHeight: 28,
    paddingHorizontal: scaleHorizontal(6),
    textAlign: 'center',
    textDecorationLine: 'underline',
  },
  container: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    marginVertical: scaleVertical(16),
  },
  subheader: {
    marginLeft: 2,
  },
});

export default memo(ResendOTPTimer);
