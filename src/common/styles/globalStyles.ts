import { FlexStyle, ImageStyle, TextStyle, ViewStyle } from 'react-native';

import { colors } from '@/common/styles/colors';

import { styleGenerator } from './styleGenerator';

/**
 * Default styles used throughout the app.
 * Contains style generating functions, which can be used in places
 * where it's tempting to use inline style:
 *
 * @example
 *
 * ```jsx
 * <View style={globalStyles.flex(1)}>{...}</View>
 * ```
 */
export const globalStyles = {
  // set each prop individually to allow for customization of the S type parameter
  flex: styleGenerator<Pick<FlexStyle, 'flex'>>('flex'),
  flexDirection:
    styleGenerator<Pick<FlexStyle, 'flexDirection'>>('flexDirection'),
  width: styleGenerator<Pick<FlexStyle, 'width'>>('width'),
  height: styleGenerator<Pick<FlexStyle | ImageStyle, 'height'>>('height'),
  minHeight:
    styleGenerator<Pick<FlexStyle | ImageStyle, 'minHeight'>>('minHeight'),
  bottom: styleGenerator<ImageStyle>('bottom'),
  top: styleGenerator<ImageStyle>('top'),
  left: styleGenerator<ImageStyle>('left'),
  right: styleGenerator<ImageStyle>('right'),
  z: styleGenerator<ImageStyle>('zIndex'),
  mt: styleGenerator<Pick<FlexStyle, 'marginTop'>>('marginTop'),
  ml: styleGenerator<Pick<FlexStyle, 'marginLeft'>>('marginLeft'),
  mr: styleGenerator<FlexStyle>('marginRight'),
  mb: styleGenerator<FlexStyle>('marginBottom'),
  mx: styleGenerator<FlexStyle>('marginHorizontal'),
  my: styleGenerator<FlexStyle>('marginVertical'),
  ma: styleGenerator<FlexStyle>('margin'),
  pt: styleGenerator<FlexStyle>('paddingTop'),
  pl: styleGenerator<FlexStyle>('paddingLeft'),
  pr: styleGenerator<FlexStyle>('paddingRight'),
  pb: styleGenerator<FlexStyle>('paddingBottom'),
  px: styleGenerator<FlexStyle>('paddingHorizontal'),
  py: styleGenerator<FlexStyle>('paddingVertical'),
  pa: styleGenerator<FlexStyle>('padding'),
  bw: styleGenerator<FlexStyle>('borderWidth'),
  aspect: styleGenerator<FlexStyle>('aspectRatio'),
  color: styleGenerator<TextStyle>('color'),
  backgroundColor:
    styleGenerator<Pick<ViewStyle, 'backgroundColor'>>('backgroundColor'),
  borderColor: styleGenerator<Pick<ViewStyle, 'borderColor'>>('borderColor'),
  borderWidth: styleGenerator<Pick<ViewStyle, 'borderWidth'>>('borderWidth'),
  opacity: styleGenerator<ImageStyle>('opacity'),
  textAlignVertically: styleGenerator<TextStyle>('textAlignVertical'),
  fontSize: styleGenerator<TextStyle>('fontSize'),
  textTransform: styleGenerator<TextStyle>('textTransform'),
  display: styleGenerator<Pick<ViewStyle, 'display'>>('display'),

  positionAbsolute: { position: 'absolute' } as Pick<FlexStyle, 'position'>,
  positionRelative: { position: 'relative' } as FlexStyle,
  flexSelfEnd: { alignSelf: 'flex-end' } as Pick<FlexStyle, 'alignSelf'>,
  flexSelfStart: { alignSelf: 'flex-start' } as Pick<FlexStyle, 'alignSelf'>,
  flexSelfCenter: { alignSelf: 'center' } as Pick<FlexStyle, 'alignSelf'>,
  alignItemsCenter: { alignItems: 'center' } as FlexStyle,
  alignItemsEnd: { alignItems: 'flex-end' } as FlexStyle,
  alignItemsStart: { alignItems: 'flex-start' } as FlexStyle,
  justifyCenter: { justifyContent: 'center' } as FlexStyle,
  justifyStart: { justifyContent: 'flex-start' } as Pick<
    FlexStyle,
    'justifyContent'
  >,
  justifyEnd: { justifyContent: 'flex-end' } as Pick<
    FlexStyle,
    'justifyContent'
  >,
  justifySpaceBetween: { justifyContent: 'space-between' } as FlexStyle,
  flexRow: { flexDirection: 'row' } as FlexStyle,
  flexRowSpaceBetween: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  } as FlexStyle,
  flexRowReverse: { flexDirection: 'row-reverse' } as FlexStyle,
  flexColumn: { flexDirection: 'column' } as FlexStyle,
  textAlignRight: { textAlign: 'right' } as TextStyle,
  textAlignLeft: { textAlign: 'left' } as TextStyle,
  textAlignCenter: { textAlign: 'center' } as TextStyle,
  overflowVisible: { overflow: 'visible' } as FlexStyle,
  overflowHidden: { overflow: 'hidden' } as FlexStyle,
  progressBarStyle: {
    borderWidth: 0,
    borderRadius: 60,
  } as ViewStyle,
  globalInlineIcon: {
    flex: 1,
    height: '100%',
    width: undefined,
    resizeMode: 'contain',
  } as ImageStyle,
  matchParent: {
    width: '100%',
    height: '100%',
  } as ImageStyle,
  matchParentAbs: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  } as ImageStyle,
  flexRowContentCenter: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  } as FlexStyle,
  flex1Row: {
    flex: 1,
    flexDirection: 'row',
  } as FlexStyle,
  flex1ChildCenter: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  } as FlexStyle,
  flex1RowReverse: {
    flex: 1,
    flexDirection: 'row-reverse',
  } as FlexStyle,
  underline: {
    borderBottomColor: colors.typography.clickable,
    borderBottomWidth: 1,
  } as FlexStyle,
};
