import { constants } from '@/common/constants';

/**
 * These are the sizes that we use in our designs.
 * We can use these values to scale components inside the app.
 * So for example, you have a design where the component is
 * 120x80. Now, the screen of the user might have a different
 * size, but we still want the component to have the same dimensions.
 *
 * @example:
 *
 * ```tsx
 * // component style guideline on a 375x667 grid is 120x80
 * const styles = {
 *  container: {
 *    width: scaleHorizontal(120),
 *    height: scaleVertical(80),
 * ```
 *
 * **Important**: If the component is designed on a different grid size,
 * you can set the reference size as second parameter.
 */
const REFERENCE_SCREEN_WIDTH = 375;
const REFERENCE_SCREEN_HEIGHT = 667;
const { height: SCREEN_HEIGHT, width: SCREEN_WIDTH } = constants.dim;

export const scaleHorizontal = (
  input: number,
  referenceWidth = REFERENCE_SCREEN_WIDTH,
): number => SCREEN_WIDTH * (input / referenceWidth);

export const scaleVertical = (
  input: number,
  referenceHeight = REFERENCE_SCREEN_HEIGHT,
): number => SCREEN_HEIGHT * (input / referenceHeight);
