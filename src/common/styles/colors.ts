const percentToHex = (percent: number): string => {
  const intValue = Math.round(percent * 255); // map percent to nearest integer (0 - 255)
  const hexValue = intValue.toString(16); // get hexadecimal representation
  return hexValue.padStart(2, '0').toUpperCase(); // format with leading 0 and upper case characters
};

// TODO: ideally the return type should be ColorValue from "react-native"
//       BUT as the color codes are used by other external components
//       this type often causes issues
export const colorWithOpacityPercentage = (
  color: string,
  percentage: number,
): string => `${color}${percentToHex(percentage)}`;

const palette = {
  // These colors are used as supporting secondary colors in backgrounds, text colors, seperators, models, etc
  FMBlack: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111111',
  },
  FMBlue: {
    50: '#eaebee',
    100: '#bcc1ca',
    200: '#9ca3b1',
    300: '#6f788d',
    400: '#535e77',
    500: '#213654',
    600: '#24314d',
    700: '#1c263c',
    800: '#161e2f',
    900: '#111724',
  },
  FMGreen: {
    50: '#ebf6f1',
    100: '#c1e3d5',
    200: '#a3d5c0',
    300: '#79c2a4',
    400: '#5fb692',
    500: '#37a477',
    600: '#32956c',
    700: '#277454',
    800: '#1e5a41',
    900: '#174531',
  },
  FMLightBlue: {
    50: '#e7ecfb',
    100: '#cfd9f7',
    200: '#b7c6f4',
    300: '#9fb3f0',
    400: '#87a0ec',
    500: '#6f8de9',
    600: '#5c75d2',
    700: '#4a5e9b',
    800: '#374674',
    900: '#252f4d',
  },
  warning: {
    24: '#F59E0B3D',
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b',
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350F',
  },
  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991B1B',
    900: '#7f1d1d',
  },
  neutral: {
    900: '#111827',
  },
  FMPurple: {
    50: '#F5EEFF',
    800: '#4D0CA1',
  },
  FMPrimary: {
    50: '#F5F7FA',
    200: '#E8EAED',
    300: '#D1D5DB',
    400: '#383A42',
  },
  FMGray: {
    100: '#DBDAE4',
    200: '#E4E7EC',
    500: '#667085',
  },
};

export const colors = {
  open: '#c96868',
  resolved: '#111111',
  closed: '#68c9a4',
  buttonBorder: '#8E95A2',
  pending: '#edb86a',
  white: '#ffffff',
  black: '#000000',
  grey: '#2F2F2F',
  transparent: 'transparent',
  accountInfo: '#1F1F1F',
  p500: palette.FMBlack[900],
  palette,
  gradients: {
    fairSave: {
      start: '#6887E8',
      end: '#5C7DE0',
    },
  },
  buttonBlue: '#283555',
  typography: {
    clickable: palette.FMGreen[400],
    clickableDisabled: colorWithOpacityPercentage(palette.FMBlue[200], 0.5),
    // not clickable elements: headers, lists, icons, labels, other data, etc
    text: palette.FMBlack[900],
    // numbers, headers, lists, icon labels, other data,
    // data: additionalUIColors.dark,
    buttonText: '#fff',
    primary: palette.FMBlack[900],
    blue: palette.FMBlue[400],
    editTextPlaceholder: palette.FMBlack[400],
    error: palette.error[500],
    label: palette.FMBlack[800],
    warning: palette.warning[500],
    security: palette.warning[600],
    form: palette.FMBlack[500],
    keys: palette.FMBlack[700],
  },
  additionalUIColors: {
    selectPlaceholder: palette.FMBlack[400],
  },
};
