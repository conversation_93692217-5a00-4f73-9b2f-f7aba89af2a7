// generates quick style objects for given style attributes
export const styleGenerator = <S>(
  attribute: keyof S,
): ((value: S[typeof attribute]) => Pick<S, keyof S>) => {
  type V = S[typeof attribute];

  // a cache for generated style objects
  const cache = new Map<V, S>();

  // return the function that gives out the styles
  return (value: V): S => {
    // get the style from the cache
    let style = cache.get(value);

    // if it doesn't exist, create and cache it
    if (style == null) {
      style = { [attribute]: value } as unknown as S;
      cache.set(value, style);
    }
    return style;
  };
};
