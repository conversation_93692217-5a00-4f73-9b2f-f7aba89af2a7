import React, { ReactNode } from 'react';

import ComingSoon from './comingSoon';
import { useFeatureFlag } from './featureFlagUtils';

interface FeatureGateProps {
  flagKey: string;
  children: ReactNode;
  Fallback?: React.FC;
  noRender?: boolean;
  comingSoonText?: string;
  comingSoonSubtitle?: string | null;
}

export const FeatureGate: React.FC<FeatureGateProps> = ({
  flagKey,
  children,
  Fallback,
  noRender = false,
  comingSoonText = 'Feature is not currently available',
  comingSoonSubtitle = null,
}) => {
  const { enabled } = useFeatureFlag(flagKey);

  if (noRender) {
    return null;
  }

  let BaseFallback = ComingSoon;

  if (Fallback) {
    BaseFallback = Fallback;
  }

  if (!enabled) {
    return <BaseFallback text={comingSoonText ?? ''} subtitle={comingSoonSubtitle ?? ''} />;
  }

  return <>{children}</>;
};

export default FeatureGate;
