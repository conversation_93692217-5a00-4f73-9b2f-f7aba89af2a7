import { useMemo } from 'react';

import { FeatureFlag } from '@/store/global/type';
import { useAppSelector } from '@/store/hooks';

const canShowFeature = (data: FeatureFlag[], flagKey: string): boolean => {
  if (!data || !Array.isArray(data)) {
    return false;
  }

  const foundFlag = data.find((flag) => flag.flagKey === flagKey);

  if (!foundFlag) {
    return false;
  }

  // Handle different flag structures
  if (foundFlag.parsed && typeof foundFlag.parsed.showBeta !== 'undefined') {
    return foundFlag.parsed.showBeta;
  }

  return false;
};

export interface FeatureFlagResult {
  enabled: boolean;
}

export function useFeatureFlag(flagKey: string): FeatureFlagResult {
  const { flags } = useAppSelector((state) => state.featureFlags);

  const result = useMemo((): FeatureFlagResult => {
    const enabled = canShowFeature(flags, flagKey);
    return { enabled };
  }, [flags, flagKey]);

  return result;
}

/**
 * Custom hook to get a specific feature flag by its key.
 * @param flagKey - The key of the feature flag to retrieve.
 * @returns The feature flag object if found, otherwise undefined.
 */
export function usePickedFeatureFlag(
  flagKey: string,
): FeatureFlag | undefined {
  const { flags } = useAppSelector((state) => state.featureFlags);

  return useMemo(() => flags.find((flag) => flag.flagKey === flagKey), [flags, flagKey]);
}
