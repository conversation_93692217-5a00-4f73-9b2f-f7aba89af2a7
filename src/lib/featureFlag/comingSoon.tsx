import React from 'react';
import { StyleSheet, TextStyle, View, ViewStyle } from 'react-native';

import { colors } from '@/common/styles/colors';
import { PayforceText } from '@/components/PayforceText/PayforceText';

interface ComingSoonProps {
  text?: string;
  subtitle?: string | null;
  style?: ViewStyle;
  textStyle?: TextStyle;
  subtitleStyle?: TextStyle;
}

const ComingSoon: React.FC<ComingSoonProps> = ({
  text = 'Coming Soon',
  subtitle = null,
}) => (
  <View style={styles.container}>
    <PayforceText.Subheader color={colors.palette.FMBlack[500]}>
      🚧
    </PayforceText.Subheader>
    <PayforceText.Header
      color={colors.palette.FMBlack[800]}
      style={styles.text}
    >
      {text}
    </PayforceText.Header>
    {subtitle && (
      <PayforceText.Subheader
        color={colors.palette.FMBlack[500]}
        style={styles.subtitle}
      >
        {subtitle}
      </PayforceText.Subheader>
    )}
  </View>
);

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
    padding: 20,
  },
  subtitle: {
    fontSize: 14,
    textAlign: 'center',
  },
  text: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
});

export default ComingSoon;
