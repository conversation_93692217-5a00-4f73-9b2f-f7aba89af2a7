import { BottomSheetModalMethods } from '@gorhom/bottom-sheet/lib/typescript/types';
import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';

import { CancelModal } from '@/asset/index';
import { colors } from '@/common/styles/colors';
import {
  fontPixel,
  heightPixel,
  pixelSizeHorizontal,
  pixelSizeVertical,
  widthPixel,
} from '@/common/utilities/normalize';
import CustomBottomSheet from '@/components/BottomSheet/BottomSheet';
import { TypedBottomSheetFlatlist } from '@/components/BottomSheet/types';
import { PayforceButton } from '@/components/PayforceButton';
import { PayforceText } from '@/components/PayforceText/PayforceText';
import { FlexedView } from '@/components/PayForceView';

import { CollateralCollectionList } from './components/data';

export type ILoanCollateralCollectionProps = {
  onClose: () => void;
};

const LoanCollateralCollection = React.forwardRef(
  (
    { onClose }: ILoanCollateralCollectionProps,
    ref: React.Ref<BottomSheetModalMethods>,
  ) => {
    const renderItem = ({ item }: { item: any }) => (
      <View
        key={item.id}
        style={{
          paddingHorizontal: pixelSizeHorizontal(15),
          paddingVertical: pixelSizeVertical(16),
        }}
      >
        <PayforceText.Body2 color={colors.palette.FMBlack[800]}>
          {item.title}
        </PayforceText.Body2>
        <PayforceText.Subheader color={colors.palette.FMBlack[500]}>
          {item.description}
        </PayforceText.Subheader>
      </View>
    );

    return (
      <>
        <CustomBottomSheet
          ref={ref}
          closeModal={onClose}
          snapBegin="85%"
          snapEnd="85%"
          snapMid="85%"
        >
          <TouchableOpacity
            onPress={() => onClose()}
            style={styles.CancelModalBtn}
          >
            <CancelModal />
          </TouchableOpacity>
          <FlexedView>
            <View style={styles.modalContainerTitle}>
              <PayforceText.Body3Bold color={colors.palette.FMBlack[800]}>
                Collateral Collection
              </PayforceText.Body3Bold>
              <PayforceText.Body1
                color={colors.palette.FMBlack[500]}
                style={styles.modalContainerDescription}
              >
                Make sure you have any of these collaterals available for review
                and collection when your Loan Agent visits.
              </PayforceText.Body1>
            </View>
            <TypedBottomSheetFlatlist
              data={CollateralCollectionList}
              renderItem={(item) => renderItem(item)}
              showsHorizontalScrollIndicator={false}
            />
            <View
              style={{
                marginHorizontal: pixelSizeHorizontal(18),
                marginVertical: pixelSizeVertical(30),
              }}
            >
              <PayforceButton type="big" onPress={() => onClose()}>
                <PayforceText.Body1Medium color={colors.white}>
                  I understand
                </PayforceText.Body1Medium>
              </PayforceButton>
            </View>
          </FlexedView>
        </CustomBottomSheet>
      </>
    );
  },
);

export default LoanCollateralCollection;

const styles = StyleSheet.create({
  CancelModalBtn: {
    alignItems: 'center',
    alignSelf: 'flex-end',
    backgroundColor: colors.palette.FMBlack[100],
    borderRadius: 50,
    height: heightPixel(45),
    justifyContent: 'center',
    marginRight: pixelSizeHorizontal(20),
    width: widthPixel(45),
  },

  modalContainerDescription: {
    color: colors.palette.FMBlack[500],
    fontSize: fontPixel(14),
    fontWeight: '700',
    lineHeight: 20,
    paddingTop: pixelSizeVertical(4),
  },
  modalContainerTitle: {
    paddingHorizontal: pixelSizeHorizontal(15),
    paddingVertical: pixelSizeVertical(12),
  },
});
