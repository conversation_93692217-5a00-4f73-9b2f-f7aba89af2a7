import { TouchableOpacity } from '@gorhom/bottom-sheet';
import React, { useEffect, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import Toast from 'react-native-toast-message';

import { Assets } from '@/asset/index';
import { ScreenHeader } from '@/common/components';
import { colors } from '@/common/styles/colors';
import {
  fontPixel,
  heightPixel,
  pixelSizeHorizontal,
  pixelSizeVertical,
  widthPixel,
} from '@/common/utilities/normalize';
import GlobalPageWrapper from '@/components/GlobalPageWrapper';
import { ListSeparator } from '@/components/ListSeparator';
import { PayforceButton } from '@/components/PayforceButton';
import { PayforceText } from '@/components/PayforceText/PayforceText';
import { useNavigation, useRoute } from '@/routes/auth/(stacks)';
// import { selectAgent } from '@/store/user/selectors';
import { useCreateBottomSheetModal } from '@/utils/helper';

import GuarantorCodeSuccessBottomSheet from '../components/GuarantorCodeSuccessBottomSheet';
import GuarantorSubmittedBottomSheet from '../components/GuarantorSubmittedBottomSheet';
import { useGenerateGuarantorCode } from '../hooks/mutations/loanApplication.mutation';
import { useGetGuarantorList } from '../hooks/queries/lookup.query';

const LoanGenerateGuarantorCode = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { applicationId } = route.params as { applicationId: string };
  // const agentDetials = useSelector(selectAgent);
  const [noGuarantorRef, openNoGuarantoRef, closeNoGuarantorRef] = useCreateBottomSheetModal();
  const [codeSuccessRef, openCodeSuccesRef, closeCodeSuccessRef] = useCreateBottomSheetModal();

  const [activeGuarantorId, setActiveGuarantorId] = useState('');
  const [isSent, setIsSent] = useState(false);

  const { data: guarantors } = useGetGuarantorList(applicationId);
  const { mutate, isLoading } = useGenerateGuarantorCode();

  const handleCodeGeneration = () => {
    if (activeGuarantorId) {
      mutate({ loanApplicationId: Number(applicationId), guarantorId: Number(activeGuarantorId) }, {
        onSuccess: (res) => {
          if (res?.status >= 400) {
            Toast.show({
              type: 'error',
              text1: res?.data?.error_message || 'An error occurred while generating the code',
            });
          }

          if (res?.status === 200) {
            setIsSent(true);
            openCodeSuccesRef();
            Toast.show({
              type: 'success',
              text1: res?.data?.result || 'Code generated successfully',
            });
          }
        },
      });
    }
  };

  useEffect(() => {
    openNoGuarantoRef();
  }, []);

  return (
    <GlobalPageWrapper>
      <ScreenHeader
        rightContent={(
          <Assets.LoansIcons.GuaranteeBadge />
        )}
        onPress={() => navigation.goBack()}
        variant="row-with-icon"
        topNavStyle={styles.topNavStyle}

      />
      <ListSeparator height={1} />
      <ScrollView showsVerticalScrollIndicator={false}>

        <View>
          <PayforceText.Body2 color={colors.palette.FMBlack[800]} style={styles.heading}>
            Generate Guarantor’s Code
          </PayforceText.Body2>
          <PayforceText.Body2 color={colors.palette.FMBlack[500]} style={styles.desc}>
            Your guarantor can only fill their form with the code you will generate and send to them. This is to keep your loan information safe.
          </PayforceText.Body2>
        </View>
        <ListSeparator height={24} />
        <View>
          {guarantors?.data.result && guarantors.data.result.data.map((guarantor: { id: string; firstName: string; lastName: string; email: string }) => (
            <View style={styles.guarantorWrapper} key={guarantor.id}>
              <View style={styles.guarantorContent}>
                <View style={styles.guarantorIcon}>
                  <Assets.LoansIcons.Guarantors />
                </View>
                <View>
                  <PayforceText.Body2 color={colors.palette.FMBlack[800]} style={styles.guarantorContentHeading}>
                    {guarantor.firstName}
                    {' '}
                    {guarantor.lastName}
                  </PayforceText.Body2>
                  <PayforceText.Body2 color={colors.palette.FMBlack[500]} style={styles.guarantorContentDesc}>
                    {guarantor.email}
                  </PayforceText.Body2>
                </View>
              </View>
              <TouchableOpacity onPress={() => {
                setActiveGuarantorId(guarantor.id);
              }}
              >
                {(() => {
                  if (isSent) {
                    return (
                      <View style={styles.codeSent}>
                        <PayforceText.Body2 color={colors.palette.FMGreen[500]}>
                          Code Sent
                        </PayforceText.Body2>
                      </View>
                    );
                  }

                  if (activeGuarantorId === guarantor.id) {
                    return <Assets.LoansIcons.Checkmark />;
                  }

                  return <Assets.LoansIcons.CheckmarkBorder />;
                })()}
              </TouchableOpacity>
            </View>
          ))}

        </View>
      </ScrollView>

      <View style={styles.footer}>
        <View style={styles.infoWrapper}>
          <Assets.LoansIcons.loanInforCircle />
          <PayforceText.Body2 color={colors.palette.FMBlack[800]} style={styles.infoDesc}>
            This code can only last for 72 hours. After that you will need to generate a new one.
          </PayforceText.Body2>
        </View>
        <PayforceButton
          type="big"
          disabled={!activeGuarantorId || isLoading || isSent}
          onPress={() => {
            handleCodeGeneration();
          }}
        >
          <PayforceText.Body1Medium color={colors.white}>
            Generate Code
          </PayforceText.Body1Medium>
        </PayforceButton>
      </View>

      <GuarantorCodeSuccessBottomSheet ref={codeSuccessRef} onClose={closeCodeSuccessRef} />
      <GuarantorSubmittedBottomSheet ref={noGuarantorRef} onClose={closeNoGuarantorRef} />
    </GlobalPageWrapper>
  );
};

export default LoanGenerateGuarantorCode;

const styles = StyleSheet.create({
  codeSent: {
    backgroundColor: colors.palette.FMGreen[100],
    borderRadius: 4,
    paddingHorizontal: pixelSizeHorizontal(8),
    paddingVertical: pixelSizeVertical(4),
  },
  desc: {
    fontSize: fontPixel(14),
  },
  footer: {
    borderTopColor: colors.palette.FMGray[100],
    borderTopWidth: 1,
    bottom: 0,
  },
  // ----- guarantor styles -----
  guarantorContent: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: 8,
  },
  guarantorContentDesc: {
    fontSize: fontPixel(12),
    lineHeight: 18,
  },
  guarantorContentHeading: {
    fontSize: fontPixel(14),
    fontWeight: '500',
    lineHeight: 20,
  },
  guarantorIcon: {
    alignContent: 'center',
    backgroundColor: colors.palette.FMGreen[100],
    borderRadius: 4,
    height: heightPixel(32),
    justifyContent: 'center',
    paddingHorizontal: pixelSizeHorizontal(4),
    paddingVertical: pixelSizeVertical(4),
    width: widthPixel(32),
  },
  guarantorWrapper: {
    alignItems: 'center',
    backgroundColor: colors.palette.FMPrimary[50],
    borderRadius: 4,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: pixelSizeVertical(8),
    paddingHorizontal: pixelSizeHorizontal(8),
    paddingVertical: pixelSizeVertical(8),
  },
  heading: {
    fontSize: fontPixel(20),
    marginBottom: pixelSizeVertical(12),
  },
  // ----- next wrapper ------
  infoDesc: {
    fontSize: fontPixel(12),
    lineHeight: 18,
    width: '95%',
  },
  infoWrapper: {
    backgroundColor: colors.palette.warning[50],
    borderRadius: 4,
    flexDirection: 'row',
    gap: 8,
    marginVertical: pixelSizeVertical(16),
    paddingHorizontal: pixelSizeHorizontal(8),
    paddingVertical: pixelSizeVertical(8),
    width: '100%',
  },
  topNavStyle: {
    alignItems: 'center',
    bottom: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    top: -5,
    width: '100%',
  },
});
