import { useNavigation } from '@react-navigation/native';
import moment from 'moment';
import React, { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { StyleSheet, View } from 'react-native';
import Toast from 'react-native-toast-message';

// import { PhoneNumberVerification } from 'src/screens/Onboarding/components/forms';
import { ScreenHeader } from '@/common/components';
import { colors } from '@/common/styles/colors';
import {
  fontPixel,
  heightPixel,
  pixelSizeHorizontal,
  pixelSizeVertical,
} from '@/common/utilities/normalize';
import GlobalPageWrapper from '@/components/GlobalPageWrapper';
import { PayforceButton } from '@/components/PayforceButton';
import PayforceDatepicker from '@/components/PayforceDatepicker/PayfoorceDatepicker';
import { PayforceNumberInput } from '@/components/PayforceNumberInputs/PayforceNumberInput';
import { PayforceText } from '@/components/PayforceText/PayforceText';
import { useRoute } from '@/routes/auth/(stacks)';
import Timepicker from '@/utils/Timepicker';

import {
  TScheduleVisitRequest,
  useScheduleVisit,
} from '../hooks/mutations/loanApplication.mutation';

type FormData = {
  PreferredDate: string;
  PreferredTime: string;
  PhoneNumber: string;
};

const convertDateTime = (date?: string, time?: string) => {
  // Handle cases where date or time might be empty
  if (!date && !time) {
    return null;
  }

  let momentObj;

  if (date && time) {
    const parsedDate = moment(date, 'DD-MM-YY').format('YYYY-MM-DD');
    momentObj = moment(`${parsedDate} ${time}`, 'YYYY-MM-DD hh:mm A');
  } else if (date && !time) {
    momentObj = moment(date, 'DD-MM-YY').startOf('day');
  } else if (!date && time) {
    const today = moment().format('YYYY-MM-DD');
    momentObj = moment(`${today} ${time}`, 'YYYY-MM-DD hh:mm A');
  }

  return momentObj?.toISOString();
};

const LoanProposeNewVisitationDate = () => {
  const navigation = useNavigation();
  const {
    applicationId,
    preferredVisitationDate: previousPreferredDate,
    preferredVisitationTime: previousPreferredTime,
    alternatePhoneNumber: previousPhoneNumber,
  } = useRoute<'LoanProposeNewVisitationDate'>().params;

  const defaultState = {
    PreferredDate: convertDateTime(previousPreferredDate) ?? '',
    PreferredTime: previousPreferredTime ?? '',
    PhoneNumber: previousPhoneNumber ?? '',
  };

  const { mutate, isLoading } = useScheduleVisit();

  const {
    control,
    handleSubmit,
    getValues,
    formState: { errors },
  } = useForm<FormData>({
    defaultValues: defaultState,
    mode: 'all',
  });

  const formInputs: {
    label: string;
    key: keyof FormData;
    placeholder: string;
    name: string;
  }[] = [
    {
      label: 'Select  preferred visitation date',
      placeholder: 'DD/MM/YY',
      key: 'PreferredDate',
      name: 'visitation date',
    },
    {
      label: 'Select preferred visitation time',
      placeholder: '08:00 AM',
      key: 'PreferredTime',
      name: 'visitation time',
    },
    {
      label: 'Enter alternative phone number',
      placeholder: 'Enter number',
      key: 'PhoneNumber',
      name: 'Phone  Number',
    },
  ];

  const getVisitationDateTime = () => {
    const original = date;
    const originalDate = moment(original).format('YYYY-MM-DD');
    const newTime = time;
    const updatedMoment = moment(
      `${originalDate} ${newTime}`,
      'YYYY-MM-DD hh:mm A',
    );
    const updatedISO = updatedMoment.toISOString();

    return updatedISO;
  };

  const scheduleVisit = (data: FormData, isAnyTime: boolean = false) => {
    const preferredVisitationDate = isAnyTime ? '' : getVisitationDateTime();
    const alternatePhoneNumber = isAnyTime ? '' : data.PhoneNumber;
    const req: TScheduleVisitRequest = {
      preferredVisitationDate,
      alternatePhoneNumber,
      loanApplicationId: applicationId,
      isAnyTime,
    };
    mutate(req, {
      onSuccess: (res) => {
        if (res.status === 400) {
          Toast.show({
            type: 'error',
            text1: res.data.error_message,
          });
        }

        if (res.status === 200) {
          navigation.goBack();
        }
      },
    });
  };

  const handleSubmitSchedule = (data: FormData) => {
    scheduleVisit(data);
  };

  const [time, setTime] = useState('');
  const [date, setDate] = useState('');

  return (
    <GlobalPageWrapper>
      <ScreenHeader
        onPress={() => navigation.goBack()}
        topNavStyle={styles.topNavStyle}
        header={previousPreferredDate ? 'Propose a new date' : 'Add preferred visitation date'}
        body="When will you like your assigned loan officer to visit this week?"
      />
      <View style={styles.caveatWrapper}>
        <View style={styles.formWrapper}>
          {formInputs.map((item, index) => (
            <Controller
              key={index}
              control={control}
              name={item.key}
              render={({
                field: { onChange, onBlur, value },
                fieldState: { error },
              }) => {
                switch (item.key) {
                  case 'PreferredDate':
                    return (
                      <View>
                        <PayforceDatepicker
                          onDateChange={(val) => {
                            const isToday = moment(val).isSame(moment(), 'day');

                            if (!isToday) {
                              setDate(val);
                            }
                          }}
                          minDate={
                            new Date(
                              moment().add(1, 'day').format('YYYY-MM-DD'),
                            )
                          }
                          value={date}
                          label={(
                            <PayforceText.Body2
                              color={colors.palette.FMPrimary[400]}
                            >
                              {item.label}
                            </PayforceText.Body2>
                          )}
                          maxDate={false}
                        />
                        {Boolean(error?.message)
                        && getValues('PreferredDate') === ''
                          ? (
                            <PayforceText.Body2 color={colors.palette.error[300]}>
                              {error?.message}
                            </PayforceText.Body2>
                            )
                          : null}
                      </View>
                    );
                  case 'PreferredTime':
                    return (
                      <View style={styles.Wrapper}>
                        <Timepicker
                          value={time}
                          label="Select preferred visitation date"
                          pickerLabel="Select label"
                          onChange={(val) => {
                            setTime(val);
                          }}
                          placeholder="08:00AM"
                        />
                        {Boolean(error?.message)
                        && getValues('PreferredTime') === ''
                          ? (
                            <PayforceText.Body2 color={colors.palette.error[300]}>
                              {error?.message}
                            </PayforceText.Body2>
                            )
                          : null}
                      </View>
                    );
                  default:
                    return (
                      <View style={styles.Wrapper}>
                        <PayforceNumberInput
                          errorMsg={error?.message}
                          maxLength={11}
                          showError={Boolean(error?.message)}
                          inputContainerStyles={{
                            borderColor: errors.PhoneNumber?.message
                              ? colors.palette.error[300]
                              : colors.palette.FMBlack[300],
                          }}
                          value={value}
                          onBlur={onBlur}
                          onChangeText={onChange}
                          placeholder={item.placeholder}
                          label={(
                            <PayforceText.Body2 color="label">
                              {item.label}
                            </PayforceText.Body2>
                          )}
                        />
                      </View>
                    );
                }
              }}
            />
          ))}
          <View style={styles.infoWrapper}>
            <PayforceText.MicroText
              color={colors.palette.warning[900]}
              style={styles.infoDesc}
            >
              <PayforceText.IconLabel
                color={colors.palette.warning[900]}
                style={styles.infoDesc}
              >
                Fraud Alert:
              </PayforceText.IconLabel>
              {' '}
              Please do not respond to any agent other than the one assigned to
              you. When your agent arrives, verify their phone number by calling
              them or kindly ask for their ID card to verify identity.
            </PayforceText.MicroText>
          </View>
        </View>

        <View style={styles.btnContainer}>
          <PayforceButton
            type="big"
            disabled={!time || !date || isLoading}
            onPress={handleSubmit(handleSubmitSchedule)}
            style={styles.submitBtn}
          >
            <PayforceText.Body1Medium color={colors.white}>
              Submit New Date
            </PayforceText.Body1Medium>
          </PayforceButton>
        </View>
      </View>
    </GlobalPageWrapper>
  );
};

export default LoanProposeNewVisitationDate;

const styles = StyleSheet.create({
  Wrapper: {
    marginTop: pixelSizeVertical(24),
  },
  btnContainer: {
    alignItems: 'flex-end',
    flex: 1,
    justifyContent: 'flex-end',
    marginVertical: pixelSizeVertical(20),
  },
  // btnWrapper: {
  //   borderColor:
  //    colors.buttonBorder, borderWidth: 1,
  // },

  caveatWrapper: {
    flex: 1,
  },
  formWrapper: {
    flex: 2,
    marginVertical: pixelSizeVertical(30),
  },

  infoDesc: {
    fontSize: fontPixel(12),
    lineHeight: heightPixel(18),
  },

  infoWrapper: {
    backgroundColor: colors.palette.warning[50],
    borderRadius: 4,
    gap: 8,
    marginVertical: pixelSizeVertical(16),
    paddingHorizontal: pixelSizeHorizontal(12),
    paddingVertical: pixelSizeVertical(8),
    width: '100%',
  },
  submitBtn: { bottom: pixelSizeVertical(10) },
  topNavStyle: {
    alignItems: 'center',
    bottom: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    top: -5,
    width: '100%',
  },
});
