import { useNavigation, useRoute } from '@react-navigation/native';
import React, { useCallback, useEffect, useMemo } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { StyleSheet, View } from 'react-native';
import { AnimatedCircularProgress } from 'react-native-circular-progress';
import { DocumentPickerResponse } from 'react-native-document-picker';
import { TouchableOpacity } from 'react-native-gesture-handler';
import Toast from 'react-native-toast-message';
// import { PhoneNumberVerification } from 'src/screens/Onboarding/components/forms';
import { useGetBankCode } from 'src/screens/ReceiveMoney/lib/useGetBankCode';

import { CancelModal } from '@/asset/index';
import { ScreenHeader } from '@/common/components';
import { colors } from '@/common/styles/colors';
import {
  fontPixel,
  heightPixel,
  pixelSizeHorizontal,
  pixelSizeVertical,
  widthPixel,
} from '@/common/utilities/normalize';
import CustomBottomSheet from '@/components/BottomSheet/BottomSheet';
import { TypedBottomSheetFlatlist } from '@/components/BottomSheet/types';
import GlobalPageWrapper from '@/components/GlobalPageWrapper';
import { PayforceButton } from '@/components/PayforceButton';
import PayforceInput from '@/components/PayforceInput';
import { PayforceNumberInput } from '@/components/PayforceNumberInputs/PayforceNumberInput';
import { PayforceText } from '@/components/PayforceText/PayforceText';
import { useCreateBottomSheetModal } from '@/utils/helper';

import { LoanSheetDropDown } from '../components';
// import FailedBankStatementCheckBottomSheet from '../components/FailedBankStatementCheckBottomSheet';
// import FailedLoanBankStatementBottomSheet from '../components/FailedLoanBankStatementBottomSheet';
import LoanBankStatementBottomSheet from '../components/LoanBankStatementBottomSheet';
import LoanBankUploadBottomSheet from '../components/LoanBankUploadBottomSheet';
import LoanBankUploadListBottomSheet from '../components/LoanBankUploadListBottomSheet';
import { useUploadDocument } from '../hooks/mutations/loanApplication.mutation';
import { useAccountInfo, useGetDocumentDefinitionList, useGetDocumentVersionList, useGetStatementAvailability } from '../hooks/queries/lookup.query';

type FormData = {
  BankCode: string;
  AccountNumber: string;
  PhoneNumber: string;
};

const LoanBankStatement = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { applicationId, isUpdate } = route.params as { applicationId: string; isUpdate?: boolean };
  const [bankNameRef, openbankNameModal, closeBankNameModal]
    = useCreateBottomSheetModal();
  const [bankUploadRef, openBankUploadRef, closeBankUploadRef] = useCreateBottomSheetModal();
  const [bankStatementRef, openBankStatementRef, closeBankStatementRef]
    = useCreateBottomSheetModal();
    // ------- Uncomment when MBS process is ready -------
  // const [failedBankStatementRef, openFailedBankStatementRef, closeFailedBankStatementRef]
  //   = useCreateBottomSheetModal();
  // const [failedBankStatementCheckRef, openFailedBankStatementCheckRef, closeFailedBankStatementCheckRef]
  //   = useCreateBottomSheetModal();
  const [bankStatementListRef, openBankStatementListRef, closeBankStatementListRef] = useCreateBottomSheetModal();

  const [bankDetails, setBankDetails] = React.useState('');
  const [document, setDocument] = React.useState<{ [k: string]: string | number }>({});
  const [file, setFile] = React.useState<DocumentPickerResponse | null>(null);

  const snapPoints = useMemo(() => ['40%', '50%'], []);
  const defaultState = {
    BankCode: '',
    AccountNumber: '',
    PhoneNumber: '',
  };

  const {
    control,
    // handleSubmit,
    formState: { errors },
    setValue,
    getValues,
  } = useForm<FormData>({
    defaultValues: defaultState,
    mode: 'all',
  });

  const formInputs: {
    label: string;
    key: keyof FormData;
    placeholder: string;
    name: string;
  }[] = [
    {
      label: 'Select Your Bank',
      placeholder: 'The one you receive alerts with',
      key: 'BankCode',
      name: 'Bank name',
    },
    {
      label: 'Acccount number',
      placeholder: 'enter account number here',
      key: 'AccountNumber',
      name: 'Acccount number',
    },
    {
      label: 'Phone Number',
      placeholder: '',
      key: 'PhoneNumber',
      name: 'Phone number',
    },
  ];

  const { data: bankList, isFetching: fetchingBanks } = useGetBankCode();
  const { data: bankInfo, isFetching: fetchingBankInfo } = useAccountInfo(getValues('AccountNumber'), getValues('BankCode'));
  const { data: statementAvailability } = useGetStatementAvailability(getValues('BankCode'), bankDetails);
  const { data: documentList } = useGetDocumentDefinitionList(applicationId);
  const { data: versionList, isFetching: fetchingVersion, isError: versionError, refetch }
  = useGetDocumentVersionList(+applicationId, document?.documentDefinitionId as number);
  const { mutate: uploadDocument, isLoading: uploading } = useUploadDocument();

  useEffect(() => {
    const bankStatementId = documentList?.data?.result.find((item: any) => item.name === 'Bank Statement');
    setDocument(bankStatementId);
  }, [documentList]);

  useEffect(() => {
    if (statementAvailability && !statementAvailability.data.result.isAvailable) {
      openBankStatementRef();
    }
  }, [statementAvailability]);

  const handleUploadDocument = () => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('DocumentDefinitionId', document?.documentDefinitionId as number ?? 0);
    formData.append('LoanApplicationId', applicationId);
    formData.append('DocumentUploadSource', 0);

    uploadDocument(formData, {
      onSuccess: (res) => {
        if (res?.status === 400) {
          Toast.show({ type: 'error', text1: res?.data?.error_message || 'Upload failed' });
          return;
        }

        Toast.show({
          type: 'success',
          text1: 'Bank statement uploaded successfully',
        });
        closeBankUploadRef();
        openBankStatementListRef();
        refetch();
        setFile(null);
      },
    });
  };

  const renderBankNameItem = useCallback(({ item }: { item: { id: number; name: string; bank_code: string } }) => (
    <TouchableOpacity
      key={item.id}
      onPress={() => {
        setValue('BankCode', item.bank_code);
        setBankDetails(item.name);
        closeBankNameModal();
      }}
      style={[
        styles.bankNameText,
        item?.name === bankDetails ? styles.activeItem : styles.inactiveItem,
      ]}
    >
      <PayforceText.Body2 color={colors.palette.FMBlack[800]}>
        {item.name}
      </PayforceText.Body2>
    </TouchableOpacity>
  ), [bankList]);

  return (
    <GlobalPageWrapper>
      <View style={styles.headerContainer}>
        <ScreenHeader
          header="Provide bank Statement"
          onPress={() => navigation.goBack()}
          topNavStyle={styles.topNavStyle}
        />
        <AnimatedCircularProgress
          size={50}
          width={7}
          fill={60}
          rotation={160}
          tintColor={colors.palette.FMGreen[500]}
          onAnimationComplete={() => {}}
          backgroundColor={colors.palette.FMGreen[50]}
        >
          {() => (
            <View style={styles.ProgressBarFluid}>
              <PayforceText.Body2Bold color={colors.palette.FMBlack[800]}>
                2/
              </PayforceText.Body2Bold>
              <PayforceText.Body2Bold color={colors.palette.FMBlack[500]}>
                3
              </PayforceText.Body2Bold>
            </View>
          )}
        </AnimatedCircularProgress>
      </View>

      <View>
        <PayforceText.Body2 color={colors.palette.FMBlack[500]} style={styles.headingDesc}>
          Submit your bank statement from your
          {' '}
          <PayforceText.Body2 color={colors.palette.FMBlack[800]} style={styles.headingDescBold}>
            commercial bank
          </PayforceText.Body2>
          {' '}
          account to help you get a higher loan offer.
        </PayforceText.Body2>
      </View>

      {/* ------- form inputs -------- */}
      <View style={styles.formWrapper}>
        {formInputs.map((item, index) => (
          <Controller
            key={index}
            control={control}
            name={item.key}
            rules={{
              required: {
                value: true,
                message: `${item.name} is required`,
              },
              ...(item.key === 'AccountNumber' && {
                minLength: {
                  value: 10,
                  message: 'Account number must be at least 10 digits',
                },
                maxLength: {
                  value: 10,
                  message: 'Account number must be at most 10 digits',
                },
              }),
            }}
            // @ts-ignore
            render={({
              field: { onChange, onBlur, value },
              fieldState: { error },
            }) => {
              switch (item.key) {
                case 'BankCode':
                  return (
                    <View>
                      <PayforceText.Body1
                        color={colors.palette.FMBlack[800]}
                        style={styles.marginBottom}
                      >
                        {item.label}
                      </PayforceText.Body1>
                      <LoanSheetDropDown
                        openSelect={openbankNameModal}
                        placeholder={item.placeholder}
                        value={bankDetails}
                        hasDropdown={false}
                      />
                    </View>
                  );
                case 'AccountNumber':
                  return (
                    getValues('BankCode') !== '' && statementAvailability?.data.result.isAvailable
                      ? (
                        <View style={styles.Wrapper}>
                          <PayforceInput
                            keyboardType="numeric"
                            errorMsg={error?.message}
                            showError={Boolean(error?.message)}
                            inputContainerStyles={{
                              borderColor: errors.AccountNumber?.message
                                ? colors.palette.error[300]
                                : colors.palette.FMBlack[300],
                            }}
                            value={value}
                            onBlur={onBlur}
                            onChangeText={onChange}
                            placeholder={item.placeholder}
                            label={(
                              <PayforceText.Body2 color="label">
                                {item.label}
                              </PayforceText.Body2>
                        )}
                          />
                          <View style={styles.officerIDContainer}>
                            <PayforceText.Body2 color={colors.palette.FMBlack[500]}>
                              {fetchingBankInfo && 'Fetching bank info...'}
                              {bankInfo && bankInfo?.account_name}
                            </PayforceText.Body2>
                          </View>
                        </View>
                        )
                      : null
                  );
                case 'PhoneNumber':
                  return (
                    getValues('AccountNumber') !== ''
                      ? (
                        <View style={styles.Wrapper}>
                          <PayforceNumberInput
                            errorMsg={error?.message}
                            maxLength={11}
                            showError={Boolean(error?.message)}
                            inputContainerStyles={{
                              borderColor: errors.PhoneNumber?.message
                                ? colors.palette.error[300]
                                : colors.palette.FMBlack[300],
                            }}
                            value={value}
                            onBlur={onBlur}
                            onChangeText={onChange}
                            placeholder={item.placeholder}
                            label={(
                              <PayforceText.Body2 color="label">
                                {item.label}
                              </PayforceText.Body2>
                                                    )}
                          />
                        </View>
                        )
                      : null
                  );

                default:
                  return (
                    null
                  );
              }
            }}
          />
        ))}
      </View>

      <View>
        <PayforceButton
          type="big"
          onPress={() => {
            // navigation.navigate('LoanDocumentPrep');
            // openFailedBankStatementCheckRef();
            // openBankStatementRef();
          }}
          disabled={!statementAvailability?.data?.result.isAvailable}
        >
          <PayforceText.Body1Medium color={colors.white}>
            Next
          </PayforceText.Body1Medium>
        </PayforceButton>
      </View>

      <CustomBottomSheet
        ref={bankNameRef}
        index={1}
        snapPoints={snapPoints}
        enablePanDownToClose
        style={styles.marginTop}
      >
        <TouchableOpacity
          onPress={() => closeBankNameModal()}
          style={styles.CancelModalBtn}
        >
          <CancelModal />
        </TouchableOpacity>
        <View style={styles.modalContainerTitle}>
          <PayforceText.Body2 color={colors.palette.FMBlack[800]}>
            Submit bank statement
          </PayforceText.Body2>
          <PayforceText.Body1
            color={colors.palette.FMBlack[500]}
            style={styles.modalContainerDescription}
          >
            Select your bank from the list below to proceed with uploading your bank statement.
          </PayforceText.Body1>
        </View>

        {fetchingBanks && (
          <PayforceText.Body1 color={colors.palette.FMBlack[500]}>
            Loading Duration...
          </PayforceText.Body1>
        )}

        {bankList && (
          <TypedBottomSheetFlatlist
            data={bankList.length > 0 ? bankList : [{ name: 'Access Bank Nigeria Plc', bank_code: '044' }, { name: 'Gtbank Nigeria Plc', bank_code: '058' }]}
            renderItem={renderBankNameItem}
          />
        )}
      </CustomBottomSheet>

      <LoanBankStatementBottomSheet
        ref={bankStatementRef}
        onClose={closeBankStatementRef}
        uploadStatement={() => {
          closeBankStatementRef();
          openBankUploadRef();
        }}
        applicationId={applicationId}
      />
      <LoanBankUploadBottomSheet
        ref={bankUploadRef}
        onClose={() => {
          closeBankUploadRef();
          setFile(null);
        }}
        onUpload={handleUploadDocument}
        uploading={uploading}
        file={file}
        setFile={setFile}
        definition={document}
      />

      <LoanBankUploadListBottomSheet
        ref={bankStatementListRef}
        onClose={closeBankStatementListRef}
        upload={() => {
          closeBankStatementListRef();
          openBankUploadRef();
        }}
        proceed={() => {
          closeBankStatementListRef();

          if (isUpdate) {
            navigation.navigate('LoanApplicationDetails', {
              applicationId,
            });
            return;
          }

          navigation.navigate('LoanDocumentPrep', { applicationId });
        }}
        bankList={versionList?.data?.result?.documentVersions || []}
        isLoading={fetchingVersion}
        isError={versionError}
      />

      {/* ----- TODO: Uncomment when MBS process is ready ------ */}
      {/* <FailedLoanBankStatementBottomSheet ref={failedBankStatementRef} onClose={closeFailedBankStatementRef} /> */}
      {/* <FailedBankStatementCheckBottomSheet ref={failedBankStatementCheckRef} onClose={closeFailedBankStatementCheckRef} /> */}
    </GlobalPageWrapper>
  );
};

export default LoanBankStatement;
const styles = StyleSheet.create({
  CancelModalBtn: {
    alignItems: 'center',
    alignSelf: 'flex-end',
    backgroundColor: colors.palette.FMBlack[100],
    borderRadius: 50,
    height: heightPixel(45),
    justifyContent: 'center',
    marginRight: pixelSizeHorizontal(20),
    width: widthPixel(45),
  },
  ProgressBarFluid: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  Wrapper: {
    marginVertical: pixelSizeVertical(10),
  },
  activeItem: {
    backgroundColor: colors.palette.FMGreen[50],
  },
  bankNameText: {
    borderColor: colors.palette.FMBlack[100],
    height: heightPixel(48),
    marginHorizontal: pixelSizeHorizontal(14),
    marginVertical: pixelSizeVertical(4),
    padding: pixelSizeVertical(12),
  },
  formWrapper: {
    flex: 1,
    marginVertical: pixelSizeVertical(30),
  },
  headerContainer: {
    alignItems: 'flex-start',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  headingDesc: {
    fontSize: fontPixel(14),
    lineHeight: 20,
  },
  headingDescBold: {
    fontWeight: '600',
  },
  inactiveItem: {
    backgroundColor: 'transparent',
  },
  marginBottom: { marginBottom: 10 },
  marginTop: {
    marginVertical: 2,
  },
  modalContainerDescription: {
    color: colors.palette.FMBlack[500],
    fontSize: fontPixel(14),
    lineHeight: 20,
    paddingTop: pixelSizeVertical(4),
  },
  modalContainerTitle: {
    paddingHorizontal: pixelSizeHorizontal(21),
    paddingVertical: pixelSizeVertical(12),
  },
  officerIDContainer: {
    marginTop: pixelSizeVertical(5),
  },
  topNavStyle: {
    bottom: 0,
    top: -5,
    width: '10%',
  },

});
