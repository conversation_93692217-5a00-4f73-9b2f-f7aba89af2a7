import { useNavigation } from '@react-navigation/native';
import React, { useCallback, useMemo } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { StyleSheet, View } from 'react-native';
import { AnimatedCircularProgress } from 'react-native-circular-progress';
import { TouchableOpacity } from 'react-native-gesture-handler';
import Toast from 'react-native-toast-message';

import { CancelModal } from '@/asset/index';
import { ScreenHeader } from '@/common/components';
import { colors } from '@/common/styles/colors';
import {
  fontPixel,
  heightPixel,
  pixelSizeHorizontal,
  pixelSizeVertical,
  widthPixel,
} from '@/common/utilities/normalize';
import CustomBottomSheet from '@/components/BottomSheet/BottomSheet';
import { TypedBottomSheetFlatlist } from '@/components/BottomSheet/types';
import GlobalPageWrapper from '@/components/GlobalPageWrapper';
import { PayforceButton } from '@/components/PayforceButton';
import PayforceInput from '@/components/PayforceInput';
import { PayforceText } from '@/components/PayforceText/PayforceText';
import { useCreateBottomSheetModal } from '@/utils/helper';

import { LoanSheetDropDown } from '../components';
import CreditCheckPermissionBottomSheet from '../components/CreditCheckPermissionBottomSheet';
import { useCreateApplication } from '../hooks/mutations/loanApplication.mutation';
import { useLookupQuery } from '../hooks/queries/lookup.query';
import { FormData, formInputs } from './LoanApplicationFormField';

// export type ILoanApplicationProps = {
//   loanOfficerAvailable?: boolean;
// };

const LoanApplication = () => {
  const navigation = useNavigation();
  const [duration, setDuration] = React.useState('');
  const [purpose, setPurpose] = React.useState('');
  const [applicationId, setApplicationId] = React.useState('');

  const [loanDurationRef, openDurationModal, closeDurationModal]
    = useCreateBottomSheetModal();
  const [loanPurposeRef, openPurposeModal, closePurposeModal]
    = useCreateBottomSheetModal();
  const [
    loanCreditCheckRef,
    openLoanCreditCheckModal,
    closeLoanCreditCheckModal,
  ] = useCreateBottomSheetModal();
  const snapPoints = useMemo(() => ['40%', '50%', '80%'], []);

  const { data: loanTenure, isFetching: fetchingTenure } = useLookupQuery({ queryType: 'MerchantLendingLoanTenure' });

  const { data: loanPurpose, isFetching: fetchingPurpose } = useLookupQuery({ queryType: 'MerchantLendingLoanPurpose' });

  const { mutate, isLoading } = useCreateApplication();

  const defaultState = {
    loanDesiredAmount: '',
    loanDuration: '',
    purposeOfLoan: '',
    loanOfficerUserName: '',
  };

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    getValues,
  } = useForm<FormData>({
    defaultValues: defaultState,
    mode: 'all',
  });

  React.useLayoutEffect(() => {
    // openLoanCreditCheckModal();
  }, []);
  const handlePeriodChange = (val: string) => {
    setDuration(val);
    setValue('loanDuration', val);
    closeDurationModal();
  };
  const handlePurposeChange = (val: string) => {
    setPurpose(val);
    setValue('purposeOfLoan', val);
    closePurposeModal();
  };

  const handleApplicationCreation = (data: FormData) => {
    mutate({
      loanAmount: +data.loanDesiredAmount,
      loanTenure: data.loanDuration,
      loanPurpose: data.purposeOfLoan,
      loanOfficerUsername: (data.loanOfficerUserName).toLowerCase(),
      loanType: 0,
    }, {
      onSuccess: (resp) => {
        if (resp?.status === 400) {
          Toast.show({
            type: 'error',
            text1: resp.data.error_message,
          });
        }

        if (resp.status === 200) {
          setApplicationId(resp.data.result.loanApplicationId);
          openLoanCreditCheckModal();
        }
      },
    });
  };

  const renderDuration = useCallback(
    ({ item }: { item: any }) => (
      <TouchableOpacity
        onPress={() => {
          handlePeriodChange(item.name);
          setDuration(item.name);
        }}
        key={item?.name}
        style={[
          styles.loanDurationText,
          item?.name === duration ? styles.activeItem : styles.inactiveItem,
        ]}
      >
        <PayforceText.Body1 color={colors.palette.FMBlack[900]}>
          {item?.name}
        </PayforceText.Body1>
      </TouchableOpacity>
    ),
    [duration],
  );

  const renderPurpose = useCallback(
    ({ item }: { item: any }) => (
      <TouchableOpacity
        onPress={() => {
          setPurpose(item.name);
          handlePurposeChange(item.name);
        }}
        key={item?.name}
        style={[
          styles.loanDurationText,
          item?.name === purpose ? styles.activeItem : styles.inactiveItem,
        ]}
      >
        <PayforceText.Body1 color={colors.palette.FMBlack[900]}>
          {item?.name}
        </PayforceText.Body1>
      </TouchableOpacity>
    ),
    [purpose],
  );

  return (
    <GlobalPageWrapper>
      <View style={styles.headerContainer}>
        <ScreenHeader
          header="Apply for a loan"
          body="Pick the loan term that suits your needs"
          onPress={() => navigation.goBack()}
          topNavStyle={styles.topNavStyle}
        />
        <AnimatedCircularProgress
          size={50}
          width={7}
          fill={30}
          rotation={270}
          tintColor={colors.palette.FMGreen[500]}
          onAnimationComplete={() => {}}
          backgroundColor={colors.palette.FMGreen[50]}
        >
          {() => (
            <View style={styles.ProgressBarFluid}>
              <PayforceText.Body2Bold color={colors.palette.FMBlack[800]}>
                1/
              </PayforceText.Body2Bold>
              <PayforceText.Body2Bold color={colors.palette.FMBlack[500]}>
                3
              </PayforceText.Body2Bold>
            </View>
          )}
        </AnimatedCircularProgress>
      </View>

      <View style={styles.formWrapper}>
        {formInputs.map((item) => (
          <React.Fragment key={item.key}>
            <Controller
              control={control}
              key={item.key}
              rules={{
                ...(item.key !== 'loanOfficerUserName'
                  ? {
                      required: {
                        value: true,
                        message: `${item.name} is required`,
                      },
                      ...(item.key === 'loanDesiredAmount'
                        ? {
                            pattern: {
                              value: /^\d+$/,
                              message: 'Please enter a valid amount',
                            },
                            min: {
                              value: 1000000,
                              message: 'Minimum loan amount is ₦1,000,000',
                            },
                            max: {
                              value: 500000000,
                              message: 'Maximum loan amount is ₦500,000,000',
                            },
                          }
                        : {}
                      ),

                    }
                  : {}),
              }}
              name={item.key}
              render={({
                field: { onChange, onBlur, value },
                fieldState: { error },
              }) => {
                switch (item.key) {
                  case 'loanDesiredAmount':
                    return (
                      <PayforceInput
                        showError={Boolean(error?.message)}
                        onBlur={onBlur}
                        placeholder="Type amount here"
                        value={value || ''}
                        keyboardType="numeric"
                        returnKeyType="done"
                        errorMsg={errors.loanDesiredAmount?.message}
                        onChangeText={onChange}
                        inputContainerStyles={{
                          borderColor: errors.loanDesiredAmount?.message
                            ? colors.palette.error[300]
                            : colors.palette.FMBlack[300],
                          marginTop: pixelSizeVertical(5),
                        }}
                        makeIconComponent={() => (
                          <PayforceText.Body1
                            color={
                              errors.loanDesiredAmount?.message
                                ? 'error'
                                : colors.palette.FMBlack[800]
                            }
                            style={{
                              marginRight: pixelSizeVertical(10),
                              marginLeft: pixelSizeHorizontal(5),
                            }}
                          >
                            ₦
                          </PayforceText.Body1>
                        )}
                        iconContainerStyles={styles.iconContainerStyles}
                        label={(
                          <PayforceText.Body1
                            color={colors.palette.FMBlack[800]}
                          >
                            Desired Loan Amount
                          </PayforceText.Body1>
                        )}
                      />
                    );
                  case 'loanDuration':
                    return (
                      <View style={styles.Wrapper}>
                        <PayforceText.Body1 color={colors.palette.FMBlack[800]} style={styles.dropdownLabel}>
                          {item.label}
                        </PayforceText.Body1>
                        <LoanSheetDropDown
                          openSelect={openDurationModal}
                          placeholder={item.placeholder}
                          value={duration}
                          hasDropdown={false}
                        />
                        {error?.message && getValues('loanDuration') === ''
                          ? (
                            <PayforceText.Decorative color="error">
                              {error?.message}
                            </PayforceText.Decorative>
                            )
                          : null}
                      </View>
                    );
                  case 'purposeOfLoan':
                    return (
                      <View style={styles.Wrapper}>
                        <PayforceText.Body1 color={colors.palette.FMBlack[800]} style={styles.dropdownLabel}>
                          {`  ${item.label}`}
                        </PayforceText.Body1>
                        <LoanSheetDropDown
                          openSelect={openPurposeModal}
                          placeholder={item.placeholder}
                          value={purpose}
                          hasDropdown={false}
                        />
                        {error?.message && getValues('purposeOfLoan') === ''
                          ? (
                            <PayforceText.Decorative color="error">
                              {error?.message}
                            </PayforceText.Decorative>
                            )
                          : null}
                      </View>
                    );
                  default:
                    return (
                      <View style={styles.Wrapper}>
                        <PayforceInput
                          errorMsg={error?.message}
                          showError={Boolean(error?.message)}
                          inputContainerStyles={{
                            borderColor: errors.loanOfficerUserName?.message
                              ? colors.palette.error[300]
                              : colors.palette.FMBlack[300],
                          }}
                          value={value}
                          onBlur={onBlur}
                          onChangeText={onChange}
                          placeholder={item.placeholder}
                          label={(
                            <PayforceText.Body2 color="label">
                              {item.label}
                              <PayforceText.Body2 color={colors.palette.FMBlack[500]}>(optional)</PayforceText.Body2>
                            </PayforceText.Body2>
                          )}
                        />
                        {/* {loanOfficerAvailable && (
                          <View style={styles.officerIDContainer}>
                            <PayforceText.Body2
                              color={colors.palette.FMGreen[500]}
                            >
                              Ezekiel Ayikoe
                            </PayforceText.Body2>
                          </View>
                        )} */}
                      </View>
                    );
                }
              }}
            />
          </React.Fragment>
        ))}
      </View>

      <View>
        <PayforceButton
          type="big"
          isLoading={isLoading}
          onPress={handleSubmit(handleApplicationCreation)}
        >
          <PayforceText.Body1Medium color={colors.white}>
            Next
          </PayforceText.Body1Medium>
        </PayforceButton>
      </View>

      <CustomBottomSheet
        ref={loanDurationRef}
        index={1}
        snapPoints={snapPoints}
        enablePanDownToClose
      >
        <TouchableOpacity
          onPress={() => closeDurationModal()}
          style={styles.modalCloseIcon}
        >
          <CancelModal />
        </TouchableOpacity>

        {fetchingTenure && (
          <PayforceText.Body1 color={colors.palette.FMBlack[500]}>
            Loading Duration...
          </PayforceText.Body1>
        )}

        {loanTenure && (
          <>
            <View style={styles.modalContainerTitle}>
              <PayforceText.Body2 color={colors.palette.FMBlack[800]}>
                Loan Duration
              </PayforceText.Body2>
              <PayforceText.Subheader
                color={colors.palette.FMBlack[500]}
                style={styles.modalContainerDescription}
              >
                How long do you want to take this loan for?
              </PayforceText.Subheader>
            </View>

            <TypedBottomSheetFlatlist
              data={loanTenure?.data.result}
              renderItem={renderDuration}
            />
          </>
        )}
      </CustomBottomSheet>

      <CustomBottomSheet
        ref={loanPurposeRef}
        index={1}
        snapPoints={snapPoints}
        enablePanDownToClose
        style={styles.marginTop}
      >
        <TouchableOpacity
          onPress={() => closePurposeModal()}
          style={styles.CancelModalBtn}
        >
          <CancelModal />
        </TouchableOpacity>

        {fetchingPurpose && (
          <PayforceText.Body1 color={colors.palette.FMBlack[500]}>
            Loading Duration...
          </PayforceText.Body1>
        )}

        {loanPurpose && (
          <>
            <View style={styles.modalContainerTitle}>
              <PayforceText.Body2 color={colors.palette.FMBlack[800]}>
                Purpose of Loan
              </PayforceText.Body2>
              <PayforceText.Body1
                color={colors.palette.FMBlack[500]}
                style={styles.modalContainerDescription}
              >
                What do you want to use this loan for?
              </PayforceText.Body1>
            </View>

            <TypedBottomSheetFlatlist
              data={loanPurpose?.data.result}
              renderItem={renderPurpose}
              showsVerticalScrollIndicator
            />
          </>
        )}
      </CustomBottomSheet>

      <CreditCheckPermissionBottomSheet
        ref={loanCreditCheckRef}
        onClose={closeLoanCreditCheckModal}
        applicationId={applicationId}
      />
    </GlobalPageWrapper>
  );
};

export default LoanApplication;

const styles = StyleSheet.create({
  CancelModalBtn: {
    alignItems: 'center',
    alignSelf: 'flex-end',
    backgroundColor: colors.palette.FMBlack[100],
    borderRadius: 50,
    height: heightPixel(45),
    justifyContent: 'center',
    marginRight: pixelSizeHorizontal(20),
    width: widthPixel(45),
  },
  ProgressBarFluid: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  Wrapper: {
    marginVertical: pixelSizeVertical(10),
  },
  activeItem: {
    backgroundColor: colors.palette.FMGreen[50],
  },
  dropdownLabel: {
    marginBottom: pixelSizeVertical(4),
  },
  formWrapper: { flex: 1, marginVertical: pixelSizeVertical(24) },
  headerContainer: {
    alignItems: 'flex-start',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  iconContainerStyles: {
    flex: 0,
    paddingBottom: pixelSizeVertical(2),
    paddingLeft: pixelSizeHorizontal(12),
  },
  inactiveItem: {
    backgroundColor: 'transparent',
  },
  loanDurationText: {
    borderColor: colors.palette.FMBlack[100],
    height: heightPixel(48),
    marginHorizontal: pixelSizeHorizontal(14),
    marginVertical: pixelSizeVertical(4),
    padding: pixelSizeVertical(12),
  },
  marginTop: {
    marginVertical: 2,
  },
  modalCloseIcon: {
    alignItems: 'center',
    alignSelf: 'flex-end',
    backgroundColor: colors.palette.FMBlack[100],
    borderRadius: 50,
    height: heightPixel(45),
    justifyContent: 'center',
    marginRight: pixelSizeHorizontal(20),
    width: widthPixel(45),
  },
  modalContainerDescription: {
    color: colors.palette.FMBlack[500],
    fontSize: fontPixel(14),
    fontWeight: '700',
    lineHeight: 20,
    paddingTop: pixelSizeVertical(4),
  },
  modalContainerTitle: {
    paddingHorizontal: pixelSizeHorizontal(21),
    paddingVertical: pixelSizeVertical(12),
  },
  topNavStyle: {
    bottom: 0,
    top: -5,
  },
});
