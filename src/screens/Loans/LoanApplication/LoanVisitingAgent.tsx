import { useFocusEffect } from '@react-navigation/native';
import React, { useCallback, useEffect } from 'react';
import { StyleSheet, View } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import Toast from 'react-native-toast-message';

import { Assets } from '@/asset/index';
import { ScreenHeader } from '@/common/components';
import { colors } from '@/common/styles/colors';
import {
  fontPixel,
  heightPixel,
  pixelSizeHorizontal,
  pixelSizeVertical,
  widthPixel,
} from '@/common/utilities/normalize';
import GlobalPageWrapper from '@/components/GlobalPageWrapper';
import { ListSeparator } from '@/components/ListSeparator';
import { PayforceButton } from '@/components/PayforceButton';
import { PayforceImage } from '@/components/PayforceImage';
import { PayforceText } from '@/components/PayforceText/PayforceText';
import { useNavigation, useRoute } from '@/routes/auth/(stacks)';
import { clearLoadingAnimation, updateLoadingAnimation } from '@/store/global';
import { useAppDispatch } from '@/store/hooks';

import { useGetLoanVisitingAgent } from '../hooks/queries/lookup.query';

const isEmpty = (value: any, fallback = '-') => typeof value === 'string' && value.trim() !== '' ? value : fallback;

const LoanVisitingAgent = () => {
  const navigation = useNavigation();
  const { params } = useRoute<'LoanVisitingAgent'>();
  const applicationId = params?.applicationId;
  const dispatch = useAppDispatch();

  const {
    isFetching,
    data: agentData,
    isError,
    error,
    refetch,
  } = useGetLoanVisitingAgent(applicationId);

  useEffect(() => {
    if (!isFetching) {
      dispatch(clearLoadingAnimation());
      if (isError || error) {
        Toast.show({
          type: 'error',
          autoHide: true,
          text1: typeof agentData?.data.error_message === 'string'
            ? agentData?.data.error_message
            : 'Unable to get visiting agent data',
        });
        navigation.goBack();
      }
    } else {
      dispatch(updateLoadingAnimation({ status: true }));
    }
  }, [isFetching, isError, error]);

  useFocusEffect(
    useCallback(() => {
      refetch();
    }, [refetch]),
  );

  const visitingInformation = typeof agentData?.data?.result === 'object' ? agentData.data.result : null;
  const hasVisitationDate = typeof visitingInformation?.preferredVisitationDate === 'string' && visitingInformation?.preferredVisitationDate.trim() !== '';

  const visitationInfo = [
    {
      icon: <Assets.LoansIcons.CalendarFilled />,
      title: 'Preferred visitation date',
      value: isEmpty(visitingInformation?.preferredVisitationDate),
    },
    {
      icon: <Assets.LoansIcons.ClockFilled />,
      title: 'Preferred visitation time',
      value: isEmpty(visitingInformation?.preferredVisitationTime),
    },
    {
      icon: <Assets.LoansIcons.PhoneFilled />,
      title: 'Alternative phone number',
      value: visitingInformation?.alternatePhoneNumber
        ? `0${Number(visitingInformation?.alternatePhoneNumber)}`
        : '-',
    },
  ];

  const onPressVisitationButton = () => {
    navigation.navigate('LoanProposeNewVisitationDate', {
      applicationId,
      alternatePhoneNumber: visitingInformation?.alternatePhoneNumber,
      preferredVisitationDate: visitingInformation?.preferredVisitationDate,
      preferredVisitationTime: visitingInformation?.preferredVisitationTime,
    });
  };

  return (
    <GlobalPageWrapper>
      <ScreenHeader
        onPress={() => navigation.goBack()}
        topNavStyle={styles.topNavStyle}
        header="Visiting Agent"
        body="View your loan officer and see your visitation time"
      />
      <ListSeparator height={1} />
      <ScrollView showsVerticalScrollIndicator={false}>
        <ListSeparator height={24} />
        <View style={styles.agentDataWrapper}>
          {visitingInformation?.displayPicture
            ? (
              <PayforceImage
                source={{
                  uri: visitingInformation?.displayPicture,
                }}
                resizeMode="cover"
                style={styles.agentImage}
              />
              )
            : (
              <View style={styles.agentPlaceholder}>
                <Assets.LoansIcons.User />
              </View>
              )}
          <View style={styles.agentContent}>
            <View>
              <PayforceText.Body3
                color={colors.palette.FMBlack[800]}
                style={styles.agentName}
              >
                {isEmpty(visitingInformation?.fullName)}
              </PayforceText.Body3>
              <View style={styles.agentVerifiedBadge}>
                <Assets.LoansIcons.VerifiedBadge />
                <PayforceText.MicroText color={colors.palette.FMBlack[800]}>
                  Verified
                </PayforceText.MicroText>
              </View>
            </View>
            <View>
              <PayforceText.Body2
                color={colors.palette.FMBlack[500]}
                style={styles.agentOtherDetails}
              >
                Username:
                {' '}
                {isEmpty(visitingInformation?.username)}
              </PayforceText.Body2>
              <PayforceText.Body2
                color={colors.palette.FMBlack[500]}
                style={styles.agentOtherDetails}
              >
                Call: +
                {isEmpty(visitingInformation?.phoneNumber)}
              </PayforceText.Body2>
            </View>
          </View>
        </View>
        <View style={styles.contentContainerWrapper}>
          {visitationInfo.map((item) => (
            <View style={styles.infoCard} key={item.title}>
              <View style={styles.infoIcon}>{item.icon}</View>
              <View>
                <PayforceText.Decorative
                  color={colors.palette.FMBlack[500]}
                  style={styles.infoCardText}
                >
                  {item.title}
                </PayforceText.Decorative>
                <PayforceText.Body2
                  color={colors.palette.FMBlack[800]}
                  style={styles.infoCardText}
                >
                  {item.value}
                </PayforceText.Body2>
              </View>
            </View>
          ))}
        </View>
        <View style={styles.infoWrapper}>
          <PayforceText.MicroText
            color={colors.palette.warning[900]}
            style={styles.infoDesc}
          >
            <PayforceText.IconLabel
              color={colors.palette.warning[900]}
              style={styles.infoDesc}
            >
              Fraud Alert:
            </PayforceText.IconLabel>
            {' '}
            Please do not respond to any agent other than the one assigned to
            you. When your agent arrives, verify their phone number by calling
            them or kindly ask for their ID card to verify identity.
          </PayforceText.MicroText>
        </View>
      </ScrollView>

      <View>
        <PayforceButton
          type="big"
          onPress={onPressVisitationButton}
        >
          <PayforceText.Body1Medium color={colors.white}>
            {hasVisitationDate ? 'Propose a new visitation date' : 'Add preferred visitation date'}
          </PayforceText.Body1Medium>
        </PayforceButton>
      </View>
    </GlobalPageWrapper>
  );
};

export default LoanVisitingAgent;

const styles = StyleSheet.create({
  // ----- agent styles -----
  agentContent: {
    alignItems: 'flex-start',
    flex: 1,
    gap: heightPixel(12),
  },

  agentDataWrapper: {
    alignItems: 'center',
    backgroundColor: colors.palette.FMPrimary[50],
    borderRadius: 4,
    flexDirection: 'row',
    gap: 8,
    paddingHorizontal: pixelSizeHorizontal(8),
    paddingVertical: pixelSizeVertical(8),
  },
  agentImage: {
    borderRadius: widthPixel(2),
    height: widthPixel(104),
    width: heightPixel(116),
  },
  agentName: {
    lineHeight: heightPixel(24),
  },
  agentOtherDetails: {
    fontSize: fontPixel(12),
    lineHeight: 18,
  },
  agentPlaceholder: {
    alignItems: 'center',
    backgroundColor: colors.palette.FMGreen[100],
    borderRadius: widthPixel(2),
    height: widthPixel(104),
    justifyContent: 'center',
    width: heightPixel(116),
  },
  agentVerifiedBadge: {
    alignSelf: 'flex-start',
    backgroundColor: colors.palette.FMPrimary[200],
    borderRadius: 4,
    flexDirection: 'row',
    gap: 2,
    marginTop: heightPixel(4),
    paddingHorizontal: pixelSizeHorizontal(4),
    paddingVertical: pixelSizeVertical(2),
  },

  contentContainerWrapper: {
    gap: heightPixel(12),
    marginTop: heightPixel(20),
  },

  infoCard: {
    alignItems: 'center',
    backgroundColor: colors.palette.FMPrimary[50],
    borderRadius: 4,
    flexDirection: 'row',
    gap: 8,
    paddingHorizontal: pixelSizeHorizontal(10),
    paddingVertical: pixelSizeVertical(10),
    width: '100%',
  },

  infoCardText: {
    lineHeight: heightPixel(20),
  },

  infoDesc: {
    fontSize: fontPixel(12),
    lineHeight: heightPixel(18),
  },

  infoIcon: {
    alignContent: 'center',
    backgroundColor: colors.palette.FMGreen[100],
    borderRadius: 4,
    justifyContent: 'center',
    paddingHorizontal: pixelSizeHorizontal(10),
    paddingVertical: pixelSizeVertical(10),
  },

  infoWrapper: {
    backgroundColor: colors.palette.warning[50],
    borderRadius: 4,
    gap: 8,
    marginVertical: pixelSizeVertical(16),
    paddingHorizontal: pixelSizeHorizontal(12),
    paddingVertical: pixelSizeVertical(8),
    width: '100%',
  },

  topNavStyle: {
    alignItems: 'center',
    bottom: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    top: -5,
    width: '100%',
  },
});
