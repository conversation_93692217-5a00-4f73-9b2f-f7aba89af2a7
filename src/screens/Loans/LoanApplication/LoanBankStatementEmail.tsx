import Clipboard from '@react-native-clipboard/clipboard';
import { useNavigation, useRoute } from '@react-navigation/native';
import React, { useMemo } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import Toast from 'react-native-toast-message';

import { Assets, CopyIcon } from '@/asset/index';
import { ScreenHeader } from '@/common/components';
import { colors } from '@/common/styles/colors';
import {
  fontPixel,
  pixelSizeHorizontal,
  pixelSizeVertical,
} from '@/common/utilities/normalize';
import GlobalPageWrapper from '@/components/GlobalPageWrapper';
import { PayforceButton } from '@/components/PayforceButton';
import { PayforceText } from '@/components/PayforceText/PayforceText';

const LoanBankStatementEmail = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { applicationId } = route.params as { applicationId: string };

  const steps = useMemo(() => [
    `Log into your commercial bank app or visit your bank's office.`,
    `Generate a bank statement for at least 6 months (1 year preferred).`,
    `Choose the option to send it via email provided below, then send bank statement.`,
    `<EMAIL>`,
    `Once sent, click the button below to continue your loan application.`,
  ], []);

  return (
    <GlobalPageWrapper>
      <ScreenHeader
        onPress={() => navigation.goBack()}

        textAreaStyle={styles.topNavStyle}
      >
        <View style={styles.headingWrapper}>
          <View style={styles.headingDetails}>
            <PayforceText.Body2 color={colors.white} style={styles.headingTitle}>
              Send bank statement directly
            </PayforceText.Body2>
            <PayforceText.Body2 color={colors.white} style={styles.headingDesc}>
              Please follow the instructions below
            </PayforceText.Body2>
          </View>
          <Assets.LoansIcons.Send />
        </View>
      </ScreenHeader>

      <View style={styles.guideWrapper}>
        <PayforceText.Body2 color={colors.palette.FMBlack[800]}>
          How to send your bank statement as email.
        </PayforceText.Body2>

        <View>
          {steps.map((step, index) => (
            <View key={index} style={styles.stepWrapper}>
              <PayforceText.Body2 color={colors.palette.FMBlack[500]} style={styles.stepNum}>
                Step
                {' '}
                {index + 1}
              </PayforceText.Body2>
              {index === 3
                ? (
                  <View style={styles.stepEmail}>
                    <PayforceText.Body2 color={colors.palette.FMGreen[500]} style={styles.stepDesc}>
                      {step}
                    </PayforceText.Body2>
                    <TouchableOpacity onPress={() => {
                      Clipboard.setString(step);
                      Toast.show({
                        type: 'copied',
                        text1: `${step} Copied!`,
                      });
                    }}
                    >
                      <CopyIcon />
                    </TouchableOpacity>
                  </View>
                  )

                : (
                  <PayforceText.Body2 color={colors.palette.FMBlack[800]} style={styles.stepDesc}>
                    {step}
                  </PayforceText.Body2>
                  )}
            </View>
          ))}
        </View>
      </View>

      <View>
        <PayforceButton type="big" onPress={() => navigation.navigate('LoanDocumentPrep', { applicationId })}>
          <PayforceText.Body1Medium color={colors.white}>
            I have sent the email
          </PayforceText.Body1Medium>
        </PayforceButton>
      </View>

    </GlobalPageWrapper>
  );
};

export default LoanBankStatementEmail;

const styles = StyleSheet.create({
  guideWrapper: {
    backgroundColor: colors.palette.FMPrimary[50],
    borderRadius: 4,
    marginBottom: pixelSizeVertical(70),
    marginTop: pixelSizeVertical(16),
    paddingHorizontal: pixelSizeHorizontal(12),
    paddingVertical: pixelSizeVertical(12),
  },
  headingDesc: {
    fontSize: fontPixel(14),
    fontWeight: '400',
  },
  headingDetails: {
    width: '80%',
  },
  headingTitle: {
    fontSize: fontPixel(18),
    fontWeight: '500',
    lineHeight: fontPixel(24),
  },
  headingWrapper: {
    alignItems: 'center',
    backgroundColor: colors.palette.FMGreen[600],
    borderRadius: 6,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: pixelSizeHorizontal(9),
    paddingVertical: pixelSizeVertical(16),
  },
  stepDesc: {
    fontSize: fontPixel(14),
    fontWeight: '500',
    lineHeight: fontPixel(20),
  },
  stepEmail: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  stepNum: {
    fontSize: fontPixel(14),
  },
  stepWrapper: {
    marginTop: pixelSizeVertical(16),
  },
  topNavStyle: {
    marginTop: pixelSizeVertical(0),
  },
});
