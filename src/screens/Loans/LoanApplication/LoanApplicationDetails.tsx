import { useFocusEffect } from '@react-navigation/native';
import CleverTap from 'clevertap-react-native';
import React, { useEffect } from 'react';
import { Image, StyleSheet, View } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { useSelector } from 'react-redux';

import { Assets } from '@/asset/index';
import { ScreenHeader } from '@/common/components';
import { constants } from '@/common/constants';
import { colors } from '@/common/styles/colors';
import {
  fontPixel,
  heightPixel,
  pixelSizeHorizontal,
  pixelSizeVertical,
  widthPixel,
} from '@/common/utilities/normalize';
import GlobalPageWrapper from '@/components/GlobalPageWrapper';
import { ListSeparator } from '@/components/ListSeparator';
import { PayforceText } from '@/components/PayforceText/PayforceText';
import { convertCurrency } from '@/functions/functions';
import { useNavigation, useRoute } from '@/routes/auth/(stacks)';
import { selectAgent } from '@/store/user/selectors';
import { useCreateBottomSheetModal } from '@/utils/helper';

import LoanapplicationBottomSheet from '../components/LoanApplicationBottomSheet';
import OfflineApplicationRequestLoan from '../components/OfflineApplicationRequestLoan';
import OfflineApplicationRequestLoanFooter from '../components/OfflineApplicationRequestLoanFooter';
import OfflineApplicationReviewLoan from '../components/OfflineApplicationReviewLoan';
import OfflineApplicationReviewLoanFooter from '../components/OfflineApplicationReviewLoanFooter';
import { useGetApplicationState, useGetGuarantorList } from '../hooks/queries/lookup.query';
// import NoGuarantorBottomSheet from '../components/NoGuarantorBottomSheet';

const LoanApplicationDetails = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { applicationId } = route.params as { applicationId: string };

  const [loanApplicationRef, openApplicationModal, closeApplicationModal]
    = useCreateBottomSheetModal();
  // const [noGuarantorRef, openNoGuarantoRef, closeNoGuarantorRef] = useCreateBottomSheetModal();
  const agentDetials = useSelector(selectAgent);

  const { data: applicationState, refetch: refetchApplicationState } = useGetApplicationState(applicationId);
  const { data: guarantorList, refetch: refetchGuarantors } = useGetGuarantorList(applicationId);

  useEffect(() => {
    if (!applicationId) {
      openApplicationModal();
    }
    // openNoGuarantoRef();
  }, []);

  useFocusEffect(
    React.useCallback(() => {
      refetchApplicationState();
      refetchGuarantors();
    }, []),
  );

  const handleLoanBottomSheetClick = () => {
    closeApplicationModal();
    CleverTap.recordEvent('request_loan_modal_button_clicked', {
      agent_id: agentDetials.agent_id,
      agent_username: agentDetials.agent_username,
      device_type: constants.IS_ANDROID ? 'android' : 'ios',
      app_version: constants.app_version,
    });
    navigation.navigate('LoanApplication');
  };

  const onPressSeeVisitingAgent = () => {
    if (!applicationId) {
      throw new Error('applicationId missing cannot navigate to visiting agent screen');
      return;
    }
    navigation.navigate('LoanVisitingAgent', { applicationId });
  };

  return (
    <GlobalPageWrapper>
      <ScreenHeader
        header="Loans"
        onPress={() => navigation.goBack()}
        variant="row-with-icon"
        topNavStyle={styles.topNavStyle}
      />
      <ListSeparator height={5} />
      <ScrollView showsVerticalScrollIndicator={false}>
        <ListSeparator height={10} />
        <View style={styles.topWrapper}>
          <View style={styles.offerCardTitle}>
            <View style={styles.statusWrapper}>
              <PayforceText.IconLabel color={colors.white}>
                No active Loan
              </PayforceText.IconLabel>

              {applicationId && (
                <View style={styles.reviewWrapper}>
                  <PayforceText.Body2
                    color={colors.palette.warning[500]}
                    style={styles.reviewText}
                  >
                    Under Review
                  </PayforceText.Body2>
                </View>
              )}
            </View>
            <PayforceText.KycHeader
              color={colors.white}
              style={{ paddingTop: pixelSizeVertical(3) }}
            >
              {applicationId && applicationState?.data.result
                ? convertCurrency(applicationState.data.result?.loanApplicationAmount)
                : convertCurrency('000')}
            </PayforceText.KycHeader>
          </View>
          <Image
            style={styles.backdrop}
            source={Assets.GeneralIcons.FMBlackBackDrop}
          />
        </View>
        <ListSeparator height={24} />
        {applicationId && applicationState && applicationState.data.result
          ? (
            <OfflineApplicationReviewLoan
              applicationState={applicationState.data.result.loanApplicationStates}
              applicationId={applicationId}
              guarantorList={guarantorList?.data?.result?.data ?? []}
              requiredAction={applicationState?.data?.result?.requiredActions[0] ?? null}
            />
            )
          : <OfflineApplicationRequestLoan />}

      </ScrollView>

      {/* ------ no active loan ------ */}
      <View>
        {applicationId
          ? <OfflineApplicationReviewLoanFooter onPressSeeVisitingAgent={onPressSeeVisitingAgent} />
          : <OfflineApplicationRequestLoanFooter cb={() => navigation.navigate('LoanApplication')} />}
      </View>

      {/* ------ Bottom sheet apply for loan ------ */}
      <LoanapplicationBottomSheet
        ref={loanApplicationRef}
        onClose={closeApplicationModal}
        onHandleRequest={handleLoanBottomSheetClick}
      />

      {/* <NoGuarantorBottomSheet ref={noGuarantorRef} onClose={closeNoGuarantorRef} /> */}
    </GlobalPageWrapper>
  );
};

export default LoanApplicationDetails;

const styles = StyleSheet.create({
  backdrop: {
    height: heightPixel(90),
    position: 'absolute',
    right: 0,
    top: 0,
    width: widthPixel(143),
  },
  offerCardTitle: {
    flexDirection: 'column',
    height: heightPixel(65),
    justifyContent: 'center',
    width: widthPixel(195),
  },
  reviewText: {
    fontSize: fontPixel(12),
  },
  reviewWrapper: {
    backgroundColor: colors.palette.warning[24],
    borderRadius: 4,
    paddingHorizontal: pixelSizeHorizontal(8),
    paddingVertical: pixelSizeVertical(6),
  },
  statusWrapper: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: 8,
  },
  topNavStyle: { bottom: 0, top: -5 },
  topWrapper: {
    backgroundColor: colors.palette.FMBlack[900],
    borderRadius: 8,
    height: heightPixel(94),
    justifyContent: 'space-between',
    paddingHorizontal: pixelSizeHorizontal(15),
    paddingVertical: pixelSizeVertical(15),
  },
});
