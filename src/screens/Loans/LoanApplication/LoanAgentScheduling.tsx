import { useNavigation, useRoute } from '@react-navigation/native';
import moment from 'moment';
import React, { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { Image, StyleSheet, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Toast from 'react-native-toast-message';

// import { PhoneNumberVerification } from 'src/screens/Onboarding/components/forms';
import { Assets } from '@/asset/index';
import { colors } from '@/common/styles/colors';
import {
  heightPixel,
  pixelSizeHorizontal,
  pixelSizeVertical,
  widthPixel,
} from '@/common/utilities/normalize';
import { ListSeparator } from '@/components/ListSeparator';
import { PayforceButton } from '@/components/PayforceButton';
import PayforceDatepicker from '@/components/PayforceDatepicker/PayfoorceDatepicker';
import { PayforceNumberInput } from '@/components/PayforceNumberInputs/PayforceNumberInput';
import { PayforceText } from '@/components/PayforceText/PayforceText';
import { useAppSelector } from '@/store/hooks';
import { selectAgent } from '@/store/user/selectors';
import { useCreateBottomSheetModal } from '@/utils/helper';
import Timepicker from '@/utils/Timepicker';

import ScheduleAnytimeBottomSheet from '../components/ScheduleAnytimeBottomSheet';
import { TScheduleVisitRequest, useScheduleVisit } from '../hooks/mutations/loanApplication.mutation';

type FormData = {
  PreferredDate: string;
  PreferredTime: string;
  PhoneNumber: string;
};
const LoanAgentScheduling = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { applicationId } = route.params as { applicationId: string };
  const defaultState = {
    PreferredDate: '',
    PreferredTime: '',
    PhoneNumber: '',
  };
  const agentDetails = useAppSelector(selectAgent);
  const [anytimeScheduleRef, openAnytimeScheduleRef, closeAnytimeScheduleRef] = useCreateBottomSheetModal();
  const { mutate, isLoading } = useScheduleVisit();

  const {
    control,
    handleSubmit,
    getValues,
    formState: { errors },
  } = useForm<FormData>({
    defaultValues: defaultState,
    mode: 'all',
  });

  const formInputs: {
    label: string;
    key: keyof FormData;
    placeholder: string;
    name: string;
  }[] = [
    {
      label: 'Select  preferred visitation date',
      placeholder: 'DD/MM/YY',
      key: 'PreferredDate',
      name: 'visitation date',
    },
    {
      label: 'Select prefrred visitation time',
      placeholder: '08:00 AM',
      key: 'PreferredTime',
      name: 'visitation time',
    },
    {
      label: 'Enter alternative phone number',
      placeholder: 'Enter number',
      key: 'PhoneNumber',
      name: 'Phone  Number',
    },
  ];

  const getVistationDateTime = () => {
    const original = date;
    const originalDate = moment(original).format('YYYY-MM-DD');
    const newTime = time;
    const updatedMoment = moment(`${originalDate} ${newTime}`, 'YYYY-MM-DD hh:mm A');
    const updatedISO = updatedMoment.toISOString();

    return updatedISO;
  };

  const scheduleVisit = (data: FormData, isAnyTime: boolean = false) => {
    const preferredVisitationDate = isAnyTime ? '' : getVistationDateTime();
    const alternatePhoneNumber = isAnyTime ? '' : data.PhoneNumber;
    const req: TScheduleVisitRequest = { preferredVisitationDate, alternatePhoneNumber, loanApplicationId: applicationId, isAnyTime };
    mutate(req, {
      onSuccess: (res) => {
        if (res.status === 400) {
          Toast.show({
            type: 'error',
            text1: res.data.error_message,
          });
        }

        if (res.status === 200) {
          navigation.navigate('LoanApplicationDetails', {
            applicationId: Number(applicationId),
          });
        }
      },
    });
  };

  const handleSubmitSchedule = (data: FormData) => {
    scheduleVisit(data);
  };

  const [time, setTime] = useState('');
  const [date, setDate] = useState('');
  return (
    <SafeAreaView
      style={styles.componentWrapper}
    >
      <View
        style={styles.titleWrapper}
      >
        <View style={styles.headingWrapper}>
          <PayforceText.Body3Bold color={colors.white}>
            {'Application Submitted \nSuccessfully'}
          </PayforceText.Body3Bold>
          <ListSeparator height={heightPixel(12)} />
          <PayforceText.Subheader color={colors.white}>
            {
              `Congratulations ${agentDetails?.full_name?.split(' ')[0] || ''}, you have \nsuccessfully applied for a loan with us`
            }
          </PayforceText.Subheader>
        </View>
        <Image
          style={styles.backdrop}
          source={Assets.GeneralIcons.FMGreenBackDrop}
        />
      </View>
      <View
        style={styles.caveatWrapper}
      >
        <View>
          <PayforceText.Body1Bold color={colors.palette.FMBlack[900]}>
            Whats Next?
          </PayforceText.Body1Bold>
          <ListSeparator height={5} />
          <PayforceText.Subheader color={colors.palette.FMBlack[500]}>
            An Agent will visit your residential address within 1 week to
            confirm some details from you.
          </PayforceText.Subheader>
        </View>
        <View style={styles.formWrapper}>
          {formInputs.map((item, index) => (
            <Controller
              key={index}
              control={control}
              name={item.key}
              // rules={{
              //   required: {
              //     value: false,
              //     message: `${item.name} is required`,
              //   },
              // }}
              render={({
                field: { onChange, onBlur, value },
                fieldState: { error },
              }) => {
                switch (item.key) {
                  case 'PreferredDate':
                    return (
                      <View>
                        <PayforceDatepicker
                          onDateChange={(val) => {
                            const isToday = moment(val).isSame(moment(), 'day');

                            if (!isToday) {
                              setDate(val);
                            }
                          }}
                          minDate={new Date(moment().add(1, 'day').format('YYYY-MM-DD'))}
                          value={date}
                          label={<PayforceText.Body2 color={colors.palette.FMPrimary[400]}>{item.label}</PayforceText.Body2>}
                          maxDate={false}

                        />
                        {Boolean(error?.message) && getValues('PreferredDate') === ''
                          ? (
                            <PayforceText.Body2 color={colors.palette.error[300]}>
                              {error?.message}
                            </PayforceText.Body2>
                            )
                          : null}
                      </View>
                    );
                  case 'PreferredTime':
                    return (
                      <View style={styles.Wrapper}>
                        <Timepicker
                          value={time}
                          label="Select preferred visitation date"
                          pickerLabel="Select label"
                          onChange={(val) => {
                            setTime(val);
                          }}
                          placeholder="08:00AM"
                        />
                        {Boolean(error?.message) && getValues('PreferredTime') === ''
                          ? (
                            <PayforceText.Body2 color={colors.palette.error[300]}>
                              {error?.message}
                            </PayforceText.Body2>
                            )
                          : null}
                      </View>
                    );
                  default:
                    return (
                      <View style={styles.Wrapper}>
                        <PayforceNumberInput
                          errorMsg={error?.message}
                          maxLength={11}
                          showError={Boolean(error?.message)}
                          inputContainerStyles={{
                            borderColor: errors.PhoneNumber?.message
                              ? colors.palette.error[300]
                              : colors.palette.FMBlack[300],
                          }}
                          value={value}
                          onBlur={onBlur}
                          onChangeText={onChange}
                          placeholder={item.placeholder}
                          label={(
                            <PayforceText.Body2 color="label">
                              {item.label}
                            </PayforceText.Body2>
                          )}
                        />
                      </View>
                    );
                }
              }}
            />
          ))}
        </View>

        <View
          style={styles.btnContainer}
        >
          <PayforceButton
            type="big"
            disabled={!time || !date || isLoading}
            onPress={handleSubmit(handleSubmitSchedule)}
            style={styles.sumbitBtn}
          >
            <PayforceText.Body1Medium color={colors.white}>
              Submit Date
            </PayforceText.Body1Medium>
          </PayforceButton>
          <PayforceButton
            type="small"
            color={colors.white}
            onPress={() => {
              openAnytimeScheduleRef();
            }}
            style={styles.btnWrapper}
          >
            <PayforceText.Body1Medium color={colors.palette.FMBlack[900]}>
              Any time is fine
            </PayforceText.Body1Medium>
          </PayforceButton>
        </View>

        <ScheduleAnytimeBottomSheet
          ref={anytimeScheduleRef}
          onClose={closeAnytimeScheduleRef}
          proceed={() => {
            scheduleVisit(defaultState, true);
          }}
          loading={isLoading}
        />
      </View>
    </SafeAreaView>
  );
};

export default LoanAgentScheduling;

const styles = StyleSheet.create({
  Wrapper: {
    marginTop: pixelSizeVertical(24),
  },
  backdrop: {
    height: heightPixel(90),
    position: 'absolute',
    right: 0,
    top: 0,
    width: widthPixel(143),
  },
  btnContainer: {
    alignItems: 'flex-end',
    flex: 1,
    justifyContent: 'flex-end',
    marginVertical: pixelSizeVertical(20),
  },
  btnWrapper: {
    borderColor:
     colors.buttonBorder, borderWidth: 1,
  },

  caveatWrapper: {
    backgroundColor: colors.white,
    borderTopEndRadius: 20,
    borderTopStartRadius: 20,
    height: heightPixel(697),
    padding: pixelSizeHorizontal(20),
  },
  componentWrapper: {
    backgroundColor: colors.palette.FMGreen[700],
    zIndex: 1,
  },
  formWrapper: {
    flex: 2,
    marginVertical: pixelSizeVertical(30),
  },
  headingWrapper: {
    zIndex: 2,
  },
  sumbitBtn: { bottom: pixelSizeVertical(10) },
  titleWrapper: {
    justifyContent: 'center',
    padding: pixelSizeHorizontal(20),
    zIndex: 1,
  },
});
