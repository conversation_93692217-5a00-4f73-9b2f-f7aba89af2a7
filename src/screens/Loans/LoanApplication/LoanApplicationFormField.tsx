export type FormData = {
  loanDesiredAmount: string;
  loanDuration: string;
  purposeOfLoan: string;
  loanOfficerUserName: string;
};
export const formInputs: {
  label: string;
  key: keyof FormData;
  placeholder: string;
  name: string;
}[] = [
  {
    label: 'Desired Loan Amount',
    placeholder: 'Enter Amount here',
    key: 'loanDesiredAmount',
    name: 'loan amount',
  },
  {
    label: 'Loan Duration',
    placeholder: 'Select duration here',
    key: 'loanDuration',
    name: 'loan duration',
  },
  {
    label: 'Purpose of Loan',
    placeholder: 'Select purpose here',
    key: 'purposeOfLoan',
    name: 'purpose of loan',
  },
  {
    label: 'Loan Officer Username',
    placeholder: 'Eg: JohnDoe123',
    key: 'loanOfficerUserName',
    name: 'loan officer Userna<PERSON>',
  },
];
