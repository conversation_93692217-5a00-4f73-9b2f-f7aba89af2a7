import { useNavigation, useRoute } from '@react-navigation/native';
import React from 'react';
import { Controller, useForm } from 'react-hook-form';
import { StyleSheet, View } from 'react-native';
import Toast from 'react-native-toast-message';
import { PayforceNumberInput } from 'src/components/PayforceNumberInputs/PayforceNumberInput';

import InfoIcon from '@/assets/svg/info-icon.svg';
import { ScreenHeader } from '@/common/components';
import { colors } from '@/common/styles/colors';
import {
  fontPixel,
  pixelSizeHorizontal,
  pixelSizeVertical,
  widthPixel,
} from '@/common/utilities/normalize';
import GlobalPageWrapper from '@/components/GlobalPageWrapper';
import { PayforceButton } from '@/components/PayforceButton';
import PayforceInput from '@/components/PayforceInput';
import { PayforceText } from '@/components/PayforceText/PayforceText';
import { useAppSelector } from '@/store/hooks';
import { selectAgentLoginInformation } from '@/store/user/selectors';

import { useCreateGuarantor } from '../hooks/mutations/loanApplication.mutation';

type FormData = {
  firstName: string;
  lastName: string;
  phoneNumber: string;
  email: string;
};

const LoanGuarantorsInfo = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { username } = useAppSelector(selectAgentLoginInformation);

  const { applicationId, isUpdate } = route.params as { applicationId: string; isUpdate?: boolean };

  const { mutate: createQuarantor, isLoading: isCreating } = useCreateGuarantor();

  const defaultState = {
    firstName: '',
    lastName: '',
    phoneNumber: '',
    email: '',
  };

  const {
    control,
    handleSubmit,
    formState: { errors },
    // setValue,
  } = useForm<FormData>({
    defaultValues: defaultState,
    mode: 'all',
  });

  const formInputs: {
    label: string;
    key: keyof FormData;
    placeholder: string;
    name: string;
  }[] = [
    {
      label: 'Guarantor First name',
      placeholder: 'Eg: John',
      key: 'firstName',
      name: 'first name',
    },
    {
      label: 'Guarantor Last name',
      placeholder: 'Eg: Doe',
      key: 'lastName',
      name: 'last name',
    },
    {
      label: 'Guarantor Phone Number',
      placeholder: 'Enter Phone number herer',
      key: 'phoneNumber',
      name: 'Phone  Number',
    },
    {
      label: 'Guarantor Email Address',
      placeholder: 'Enter Email address here',
      key: 'email',
      name: 'Email  Address',
    },
  ];

  const submitQuarantor = (data: FormData) => {
    createQuarantor({
      loanApplicationId: +applicationId,
      firstName: data.firstName,
      lastName: data.lastName,
      phoneNumber: data.phoneNumber,
      email: data.email,
      loanApplicantUsername: username as string,
    }, {
      onSuccess: (resp) => {
        if (resp.status === 400) {
          Toast.show({
            type: 'error',
            text1: resp.data.error_message,
          });
        }

        if (resp.status === 200) {
          if (isUpdate) {
            navigation.navigate('LoanApplicationDetails', {
              applicationId,
            });
            return;
          }

          navigation.navigate('LoanAgentScheduling', {
            applicationId,
          });
        }
      },
    });
  };

  return (
    <GlobalPageWrapper>
      <View style={styles.headerContainer}>
        <ScreenHeader
          header="Guarantor's information"
          onPress={() => navigation.goBack()}
          topNavStyle={styles.topNavStyle}
        >
          <PayforceText.Body2 color={colors.palette.FMBlack[500]} style={styles.headingDesc}>
            Ensure that your guarantor is reachable within
            {' '}
            <PayforceText.Body2 color={colors.palette.FMBlack[800]} style={styles.headingDescBold}>
              48 hours.
            </PayforceText.Body2>
          </PayforceText.Body2>
        </ScreenHeader>
      </View>
      <View style={styles.fluidWrapper}>
        {formInputs.map((item, index) => (
          <Controller
            key={index}
            control={control}
            name={item.key}
            rules={{
              required: {
                value: true,
                message: `${item.name} is required`,
              },
              ...(item.key === 'email' && {
                pattern: {
                  value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                  message: 'Please enter a valid email address',
                },
              }),

            }}
            render={({
              field: { onChange, onBlur, value },
              fieldState: { error },
            }) => {
              switch (item.key) {
                case 'phoneNumber':
                  return (
                    <View style={styles.Wrapper}>
                      <PayforceNumberInput
                        errorMsg={error?.message}
                        maxLength={11}
                        showError={Boolean(error?.message)}
                        inputContainerStyles={{
                          borderColor: errors.phoneNumber?.message
                            ? colors.palette.error[300]
                            : colors.palette.FMBlack[300],
                        }}
                        value={value}
                        onBlur={onBlur}
                        onChangeText={onChange}
                        placeholder={item.placeholder}
                        label={(
                          <PayforceText.Body2 color="label">
                            {item.label}
                          </PayforceText.Body2>
                                                                         )}
                      />
                    </View>
                  );
                default:
                  return (
                    <View style={styles.Wrapper}>
                      <PayforceInput
                        errorMsg={error?.message}
                        showError={Boolean(error?.message)}
                        inputContainerStyles={{
                          borderColor: errors.root?.message
                            ? colors.palette.error[300]
                            : colors.palette.FMBlack[300],
                        }}
                        value={value}
                        onBlur={onBlur}
                        onChangeText={onChange}
                        placeholder={item.placeholder}
                        label={(
                          <PayforceText.Body2 color="label">
                            {item.label}
                          </PayforceText.Body2>
                    )}
                      />
                    </View>
                  );
              }
            }}
          />
        ))}
        <View
          style={styles.caveatContainer}
        >
          <View style={styles.indexContainer}>
            <InfoIcon />
          </View>
          <View style={styles.wrapperWidth}>
            <PayforceText.Body2 color={colors.palette.FMBlack[800]}>
              What do we need
            </PayforceText.Body2>
            <PayforceText.Decorative color={colors.palette.FMBlack[500]}>
              Your guarantor should have strong financial credibility and be
              readily available for visitation
            </PayforceText.Decorative>
          </View>
        </View>
      </View>
      <PayforceButton
        type="big"
        onPress={handleSubmit(submitQuarantor)}
        isLoading={isCreating}
        disabled={isCreating}
      >
        <PayforceText.Body1Medium color={colors.white}>
          Submit
        </PayforceText.Body1Medium>
      </PayforceButton>
      {/* <PayforceText.Body1Medium color={colors.palette.FMGreen[500]}>
        {` Do this later >> `}
      </PayforceText.Body1Medium> */}
    </GlobalPageWrapper>
  );
};

export default LoanGuarantorsInfo;

const styles = StyleSheet.create({
  // ProgressBarFluid: {
  //   alignItems: 'center',
  //   flexDirection: 'row',
  // },
  Wrapper: {
    marginVertical: pixelSizeVertical(10),
  },
  caveatContainer: {
    alignItems: 'flex-start',
    backgroundColor: colors.palette.FMBlack[100],
    borderRadius: 8,
    flexDirection: 'row',
    marginVertical: pixelSizeVertical(15),
    padding: 10,
  },
  fluidWrapper: {
    flex: 1,
    marginVertical: pixelSizeVertical(20),
  },
  headerContainer: {
    alignItems: 'flex-start',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  headingDesc: {
    fontSize: fontPixel(14),
    lineHeight: 20,
  },
  headingDescBold: {
    fontWeight: '600',
  },
  indexContainer: {
    alignItems: 'center',
    marginHorizontal: pixelSizeHorizontal(5),
  },
  topNavStyle: {
    bottom: 0,
    top: -5,
    width: '10%',
  },
  wrapperWidth: { width: widthPixel(303) },
});
