import { useNavigation, useRoute } from '@react-navigation/native';
import React from 'react';
import { FlatList, StyleSheet, TouchableOpacity, View } from 'react-native';
import { AnimatedCircularProgress } from 'react-native-circular-progress';

import GuarantorAvatar from '@/assets/svg/guarantor-avatar.svg';
import { ScreenHeader } from '@/common/components';
import { colors } from '@/common/styles/colors';
import {
  fontPixel,
  heightPixel,
  pixelSizeHorizontal,
  pixelSizeVertical,
  widthPixel,
} from '@/common/utilities/normalize';
import GlobalPageWrapper from '@/components/GlobalPageWrapper';
import { PayforceButton } from '@/components/PayforceButton';
import { PayforceText } from '@/components/PayforceText/PayforceText';
import { useCreateBottomSheetModal } from '@/utils/helper';

import LoanCollateralCollection from '../LoanCollateralCollection';

const LoanDocumentPrep = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { applicationId } = route.params as { applicationId: string };
  const [collateralRef, openCollateralModal, closeCollateralModal]
      = useCreateBottomSheetModal();

  const DocumentList = [
    {
      id: 1,
      title: 'Guarantor\'s details',
      Description:
        'Provide the details of a credible guarantor who is \n willing to stand and sign for you.',
      route: 'LoanGuarantorsInfo',
    },
    {
      id: 2,
      title: 'Collateral',
      Description:
        'Prepare at least one acceptable form of collateral for collection.',
      routeText: 'See collateral types here >',
      route: () => openCollateralModal(),
    },
  ];

  const renderDocumentList = (item: any) => (
    <TouchableOpacity
      onPress={() => item.id === 1 ? navigation.navigate(item.route, { applicationId }) : {}}
      style={[styles.renderContainer, {
        height: item.id === 1 ? heightPixel(74) : heightPixel(100),
      }]}
    >
      <View style={styles.indexContainer}>
        <PayforceText.Body2 color={colors.palette.FMBlack[500]}>
          {item.id}
        </PayforceText.Body2>
      </View>
      <View style={{ width: widthPixel(303) }}>
        <PayforceText.Body2 color={colors.palette.FMBlack[800]}>
          {item.title}
        </PayforceText.Body2>
        <PayforceText.Decorative color={colors.palette.FMBlack[500]}>
          {item.Description}
        </PayforceText.Decorative>

        <TouchableOpacity onPress={() => openCollateralModal()}>
          <PayforceText.Decorative style={styles.routeText} color={colors.palette.FMGreen[500]}>
            {item.routeText}
          </PayforceText.Decorative>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
  return (
    <GlobalPageWrapper>
      <View style={styles.headerContainer}>
        <ScreenHeader
          header="Document Preparation"
          onPress={() => navigation.goBack()}
          topNavStyle={styles.topNavStyle}
        />
        <AnimatedCircularProgress
          size={50}
          width={7}
          fill={100}
          rotation={36}
          tintColor={colors.palette.FMGreen[500]}
          onAnimationComplete={() => {}}
          backgroundColor={colors.palette.FMGreen[50]}
        >
          {() => (
            <View style={styles.ProgressBarFluid}>
              <PayforceText.Body2Bold color={colors.palette.FMBlack[800]}>
                3/
              </PayforceText.Body2Bold>
              <PayforceText.Body2Bold color={colors.palette.FMBlack[500]}>
                3
              </PayforceText.Body2Bold>
            </View>
          )}
        </AnimatedCircularProgress>
      </View>
      <View>
        <PayforceText.Body2 color={colors.palette.FMBlack[500]} style={styles.headingDesc}>
          Ensure you have the following documents available for your assigned Loan Officer:
        </PayforceText.Body2>
      </View>

      <View style={styles.flex}>
        <FlatList
          data={DocumentList}
          renderItem={({ item }) => renderDocumentList(item)}
          keyExtractor={(item) => item.id.toString()}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.contentContainerStyle}
        />
      </View>
      <View>

        <TouchableOpacity
          onPress={() => navigation.navigate('LoanGuarantorsInfo', { applicationId })}
          style={styles.footerGuarantorFluid}
        >
          <GuarantorAvatar />
          <View style={styles.marginHorizontal}>
            <PayforceText.Body2 color={colors.palette.FMBlack[800]}>
              I have my guarantor’s details now
            </PayforceText.Body2>
            <PayforceText.IconLabel color={colors.palette.FMBlack[500]}>
              You can submit them ahead here
            </PayforceText.IconLabel>
          </View>
          <PayforceText.IconLabel color={colors.palette.FMBlack[500]}>
            {'>'}
          </PayforceText.IconLabel>
        </TouchableOpacity>

        <PayforceButton
          type="big"
          onPress={() => navigation.navigate('LoanAgentScheduling', {
            applicationId,
          })}
        >
          <PayforceText.Body1Medium color={colors.white}>
            I understand
          </PayforceText.Body1Medium>
        </PayforceButton>
      </View>
      <LoanCollateralCollection
        ref={collateralRef}
        onClose={closeCollateralModal}
      />
    </GlobalPageWrapper>
  );
};

export default LoanDocumentPrep;
const styles = StyleSheet.create({
  ProgressBarFluid: {
    alignItems: 'center',
    flexDirection: 'row',
  },

  contentContainerStyle: { paddingBottom: 20 },
  flex: { flex: 1 },
  footerGuarantorFluid: {
    backgroundColor: colors.palette.FMGreen[50],
    borderColor: colors.palette.FMGreen[200],
    borderRadius: 4,
    borderStyle: 'dashed',
    borderWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: pixelSizeVertical(10),
    paddingHorizontal: pixelSizeHorizontal(14),
    paddingVertical: pixelSizeVertical(8),
  },
  headerContainer: {
    alignItems: 'flex-start',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  headingDesc: {
    fontSize: fontPixel(14),
  },
  indexContainer: {
    alignItems: 'center',
    backgroundColor: colors.palette.FMPrimary[200],
    borderRadius: 50,
    height: heightPixel(45),
    justifyContent: 'center',
    marginRight: pixelSizeHorizontal(20),
    width: widthPixel(45),
  },
  marginHorizontal: {
    flex: 1,
    marginHorizontal: pixelSizeHorizontal(10),
  },
  renderContainer: {
    alignItems: 'flex-start',
    backgroundColor: colors.palette.FMBlack[100],
    borderRadius: 8,
    flexDirection: 'row',
    marginVertical: pixelSizeVertical(15),
    padding: 10,
  },
  routeText: {
    fontSize: fontPixel(12),
    fontWeight: '600',
    lineHeight: 18,
    marginVertical: pixelSizeVertical(8),
    textDecorationLine: 'underline',
  },
  topNavStyle: {
    bottom: 0,
    top: -5,
    width: '10%',
  },
});
