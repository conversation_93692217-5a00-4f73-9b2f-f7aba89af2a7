import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';

import { Assets } from '@/asset/index';
import { colors } from '@/common/styles/colors';
import { fontPixel, heightPixel, pixelSizeHorizontal, pixelSizeVertical, widthPixel } from '@/common/utilities/normalize';
import { PayforceButton } from '@/components/PayforceButton';
import { PayforceText } from '@/components/PayforceText/PayforceText';
import { useNavigation } from '@/routes/auth/(stacks)';

import ApplicationStatusActionBanner from './ApplicationStatusActionBanner';

interface Props {
  applicationId: string;
  requiredAction?: {
    order: number;
    name: string;
    description: string;
  };
  applicationState?: {
    order: number;
    name: string;
    iconState: 'Done' | 'Undergoing' | 'Not Started';
    description: string;
  }[];
  guarantorList?: { [k: string]: unknown }[];
}

function OfflineApplicationReviewLoan({ applicationState, applicationId, requiredAction, guarantorList }: Props) {
  const navigation = useNavigation();

  const applicationStateIcon = {
    Done: <Assets.LoansIcons.Checkmark />,
    Undergoing: <Assets.LoansIcons.CheckmarkActiveBorder />,
    'Not Started': <Assets.LoansIcons.CheckmarkInActiveBorder />,
  };

  return (
    <View>

      {guarantorList && guarantorList.length > 0 && (
        <TouchableOpacity
          style={styles.progressBanner}
          onPress={() => {
            navigation.navigate('LoanGenerateGuarantorCode', { applicationId });
          }}
        >
          <View style={styles.progressContent}>
            <View style={styles.progressBannerIcon}>
              <Assets.LoansIcons.Guarantors />
            </View>
            <View>
              <PayforceText.Body2 color={colors.palette.FMBlack[800]} style={styles.progressContentHeading}>
                Generate Guarantor’s Code
              </PayforceText.Body2>
              <PayforceText.Body2 color={colors.palette.FMBlack[500]} style={styles.progressContentDesc}>
                This will allow them access to your information
              </PayforceText.Body2>
            </View>
          </View>
          <Assets.LoansIcons.CaretRight />
        </TouchableOpacity>
      )}

      <PayforceButton type="tiny" height={44} onPress={() => { navigation.navigate('LoanBankStatement', { applicationId }); }}>
        <PayforceText.Decorative color={colors.palette.FMBlack[500]}>
          Got to statement
        </PayforceText.Decorative>
      </PayforceButton>

      {requiredAction && <ApplicationStatusActionBanner {...requiredAction} applicationId={applicationId} />}

      <View style={styles.statusWrapper}>
        <PayforceText.Body2 color={colors.palette.FMBlack[800]} style={styles.statusHeading}>
          Loan Application Status
        </PayforceText.Body2>
        <View style={styles.statusContainer}>
          {applicationState?.map((step) => (
            <View style={styles.statusContent}>
              {applicationStateIcon[step.iconState]}
              <PayforceText.Body2 color={colors.palette.FMBlack[800]} style={styles.statusDesc}>
                {step.description}
              </PayforceText.Body2>
              {step.iconState === 'Undergoing' && (
                <PayforceText.Body2 color={colors.palette.warning[800]} style={styles.statusPendingPill}>
                  Under review
                </PayforceText.Body2>
              )}
            </View>
          ))}
        </View>
      </View>
    </View>
  );
}

export default OfflineApplicationReviewLoan;

const styles = StyleSheet.create({

  progressBanner: {
    alignItems: 'center',
    backgroundColor: colors.palette.FMGreen[50],
    borderRadius: 4,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: pixelSizeHorizontal(8),
    paddingVertical: pixelSizeVertical(8),
  },
  progressBannerIcon: {
    alignContent: 'center',
    backgroundColor: colors.palette.FMGreen[100],
    borderRadius: 4,
    height: heightPixel(32),
    justifyContent: 'center',
    paddingHorizontal: pixelSizeHorizontal(4),
    paddingVertical: pixelSizeVertical(4),
    width: widthPixel(32),

  },
  progressContent: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: 8,
  },
  progressContentDesc: {
    fontSize: fontPixel(12),
    lineHeight: 18,
  },
  progressContentHeading: {
    fontSize: fontPixel(14),
    fontWeight: '500',
    lineHeight: 20,
  },
  statusContainer: {
    borderColor: colors.palette.FMPrimary[200],
    borderRadius: 8,
    borderWidth: 1,
    marginTop: pixelSizeVertical(8),
    paddingHorizontal: pixelSizeHorizontal(12),
    paddingVertical: pixelSizeVertical(12),
  },
  statusContent: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: 8,
    marginBottom: pixelSizeVertical(12),
  },
  statusDesc: {
    fontSize: fontPixel(12),
    lineHeight: 18,
  },
  statusHeading: {
    fontSize: fontPixel(14),
    fontWeight: '500',
    lineHeight: 20,
  },
  statusPendingPill: {
    backgroundColor: colors.palette.warning[100],
    borderRadius: 4,
    paddingHorizontal: pixelSizeHorizontal(8),
    paddingVertical: pixelSizeVertical(4),
  },
  statusWrapper: {
    marginTop: pixelSizeVertical(24),
  },
});
