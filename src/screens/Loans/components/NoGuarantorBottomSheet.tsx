import { BottomSheetModalMethods } from '@gorhom/bottom-sheet/lib/typescript/types';
import React from 'react';
import { StyleSheet, View } from 'react-native';

import { Assets } from '@/asset/index';
import { colors } from '@/common/styles/colors';
import { pixelSizeHorizontal, pixelSizeVertical } from '@/common/utilities/normalize';
import CustomBottomSheet from '@/components/BottomSheet/BottomSheet';
import { PayforceButton } from '@/components/PayforceButton';
import { PayforceText } from '@/components/PayforceText/PayforceText';

interface Props {
  onClose?: () => void;
}

function NoQuarantorBottomSheet({ onClose }: Props, ref: React.Ref<BottomSheetModalMethods>) {
  return (
    <CustomBottomSheet ref={ref} snapBegin="45%" snapMid="45%" snapEnd="45%" enablePanDownToClose onClose={onClose}>
      <View style={styles.container}>
        <View>

          <View style={styles.headingWrapper}>
            <Assets.LoansIcons.Guarantee />
            <PayforceText.Body2
              color={colors.black}
              style={styles.heading1}
            >
              You do not have a guarantor yet.
            </PayforceText.Body2>
            <PayforceText.Body2
              color={colors.palette.FMBlack[500]}
              style={styles.uploadDesc}
            >
              Submit your guarantor’s details for approval and then we will generate a security code for them to view your loan details with.
            </PayforceText.Body2>

          </View>

          <PayforceButton
            type="big"
            onPress={onClose}
          >
            <PayforceText.Body1Medium color={colors.white}>
              Submit Guarantor’s Information
            </PayforceText.Body1Medium>
          </PayforceButton>
        </View>

      </View>

    </CustomBottomSheet>
  );
}

export default React.forwardRef(NoQuarantorBottomSheet);

const styles = StyleSheet.create({

  container: {
    flex: 1,
    justifyContent: 'space-between',
    paddingHorizontal: pixelSizeHorizontal(20),
  },
  heading1: {
    fontSize: 20,
    fontWeight: '500',
    marginTop: pixelSizeVertical(8),
    textAlign: 'center',
  },
  headingWrapper: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: pixelSizeVertical(32),
    marginTop: pixelSizeVertical(14),
  },
  uploadDesc: {
    fontSize: 14,
    fontWeight: '400',
    marginVertical: pixelSizeVertical(8),
    textAlign: 'center',
  },
});
