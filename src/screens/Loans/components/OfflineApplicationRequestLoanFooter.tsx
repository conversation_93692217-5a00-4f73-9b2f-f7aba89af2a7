import React from 'react';
import { StyleSheet, View } from 'react-native';

import CBNIcon from '@/assets/svg/pf_cbn_icon.svg';
import { colors } from '@/common/styles/colors';
import { pixelSizeVertical } from '@/common/utilities/normalize';
import { PayforceButton } from '@/components/PayforceButton';
import { PayforceText } from '@/components/PayforceText/PayforceText';

function OfflineApplicationRequestLoanFooter({ cb }: { cb: () => void }) {
  return (
    <View>
      <PayforceButton type="big" onPress={() => cb()}>
        <PayforceText.Body1Medium color={colors.white}>
          Request a Loan
        </PayforceText.Body1Medium>
      </PayforceButton>
      <View style={[styles.formFooter, styles.cbnTextView]}>
        <CBNIcon />
        <PayforceText.Decorative color={colors.palette.FMBlack[900]}>
          Licensed by Central bank of Nigeria (CBN)
        </PayforceText.Decorative>
      </View>
    </View>
  );
}

export default OfflineApplicationRequestLoanFooter;

const styles = StyleSheet.create({
  cbnTextView: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: 12,
    justifyContent: 'center',
  },
  formFooter: {
    backgroundColor: colors.palette.FMBlack[100],
    borderRadius: 4,
    marginTop: pixelSizeVertical(16),
    padding: pixelSizeVertical(8),
  },
});
