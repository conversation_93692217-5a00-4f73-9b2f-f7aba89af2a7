import { BottomSheetModalMethods } from '@gorhom/bottom-sheet/lib/typescript/types';
import React from 'react';
import { StyleSheet, View } from 'react-native';
import { TouchableOpacity } from 'react-native-gesture-handler';

import { CancelModal } from '@/asset/index';
import { colors } from '@/common/styles/colors';
import { pixelSizeHorizontal, pixelSizeVertical } from '@/common/utilities/normalize';
import CustomBottomSheet from '@/components/BottomSheet/BottomSheet';
import { PayforceButton } from '@/components/PayforceButton';
import { PayforceText } from '@/components/PayforceText/PayforceText';

interface Props {
  onClose?: () => void;
  proceed?: () => void;
  loading?: boolean;
}

function ScheduleCheckBottomSheet({ onClose, proceed, loading }: Props, ref: React.Ref<BottomSheetModalMethods>) {
  return (
    <CustomBottomSheet ref={ref} snapBegin="40%" snapMid="40%" snapEnd="40%" enablePanDownToClose onClose={onClose}>
      <View style={styles.container}>
        <View>
          <TouchableOpacity
            onPress={() => onClose?.()}
            style={styles.closeIcon}
          >
            <CancelModal />
          </TouchableOpacity>

          <View style={styles.headingWrapper}>
            <PayforceText.Body2
              color={colors.black}
              style={styles.heading1}
            >
              Are you sure!
            </PayforceText.Body2>
            <PayforceText.Body2
              color={colors.palette.FMBlack[600]}
              style={styles.uploadDesc}
            >
              An Agent will visit your residential address within 1 week to confirm some details from you at anytime.
            </PayforceText.Body2>
          </View>

          <PayforceButton
            type="big"
            onPress={() => {
              proceed?.();
            }}
            disabled={loading}
            style={styles.proceedBtn}
          >
            <PayforceText.Body1Medium color={colors.white}>
              Continue
            </PayforceText.Body1Medium>
          </PayforceButton>
        </View>

      </View>

    </CustomBottomSheet>
  );
}

export default React.forwardRef(ScheduleCheckBottomSheet);

const styles = StyleSheet.create({
  closeIcon: {
    alignItems: 'flex-end',
  },
  container: {
    flex: 1,
    justifyContent: 'space-between',
    paddingHorizontal: pixelSizeHorizontal(20),
  },
  heading1: {
    fontSize: 18,
    fontWeight: '500',
    lineHeight: 28,
    textAlign: 'center',
  },
  headingWrapper: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: pixelSizeVertical(14),
  },
  proceedBtn: {
    marginTop: pixelSizeVertical(20),
  },
  uploadDesc: {
    fontSize: 14,
    fontWeight: '400',
    marginVertical: pixelSizeVertical(8),
    textAlign: 'center',
  },
});
