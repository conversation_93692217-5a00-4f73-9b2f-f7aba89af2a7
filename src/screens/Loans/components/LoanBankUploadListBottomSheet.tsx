import { BottomSheetModalMethods } from '@gorhom/bottom-sheet/lib/typescript/types';
import React, { useCallback } from 'react';
import { StyleSheet, View } from 'react-native';
import { TouchableOpacity } from 'react-native-gesture-handler';

import { Assets, CancelModal } from '@/asset/index';
import { colors } from '@/common/styles/colors';
import { pixelSizeHorizontal, pixelSizeVertical } from '@/common/utilities/normalize';
import CustomBottomSheet from '@/components/BottomSheet/BottomSheet';
import { TypedBottomSheetFlatlist } from '@/components/BottomSheet/types';
import { PayforceButton } from '@/components/PayforceButton';
import { PayforceText } from '@/components/PayforceText/PayforceText';

interface Props {
  onClose?: () => void;
  upload?: () => void;
  proceed?: () => void;
  isLoading?: boolean;
  isError?: boolean;
  bankList?: { [k: string]: number | string }[];
}

function LoanBankUploadListBottomSheet({ onClose, upload, proceed, bankList, isLoading, isError }: Props, ref: React.Ref<BottomSheetModalMethods>) {
  const renderStatementItems = useCallback(({ item }: { item: { versionNumber?: number; id?: number } }) => (
    <View style={styles.bankWrapper} key={item.id}>
      <View>
        <View style={styles.bankHeading}>
          <PayforceText.Body2 color={colors.palette.FMBlack[800]}>
            Bank Statement
            {' '}
            {item?.versionNumber}
          </PayforceText.Body2>
          <Assets.LoansIcons.Checkmark height={16} width={16} />
        </View>
        <PayforceText.Body2 color={colors.palette.FMGray[500]}>
          100% uploaded
        </PayforceText.Body2>
      </View>
    </View>
  ), [bankList]);

  return (
    <CustomBottomSheet ref={ref} snapBegin="70%" snapMid="70%" snapEnd="70%" enablePanDownToClose onClose={onClose} style={styles.container}>
      <TouchableOpacity
        onPress={() => onClose?.()}
        style={styles.closeIcon}
      >
        <CancelModal />
      </TouchableOpacity>

      <View style={styles.headingWrapper}>
        <PayforceText.Body2
          color={colors.black}
          style={styles.heading1}
        >
          Upload Bank Statement
        </PayforceText.Body2>
        <PayforceText.Body2
          color={colors.palette.FMBlack[600]}
          style={styles.uploadDesc}
        >
          Please upload a bank statement from your primary account dated to no less than
          {' '}
          <PayforceText.Body2 color={colors.palette.FMBlack[800]} style={styles.uploadDescPeriod}>a year.</PayforceText.Body2>
        </PayforceText.Body2>
      </View>

      <TouchableOpacity style={styles.uploadBtn} onPress={() => upload?.()}>
        <Assets.LoansIcons.PlusIcon />
        <PayforceText.Body1Medium color={colors.palette.FMBlack[800]} style={styles.uploadBtnText}>
          Upload another document
        </PayforceText.Body1Medium>
      </TouchableOpacity>

      {isLoading && <PayforceText.Body2 color={colors.palette.FMBlack[600]}>Fetching Statements...</PayforceText.Body2>}

      {!isLoading && isError && <PayforceText.Body2 color={colors.palette.error[500]}>Error fetching Statement. Pls contact support</PayforceText.Body2>}

      {!isLoading && !isError && <TypedBottomSheetFlatlist data={bankList} renderItem={renderStatementItems} />}

      <PayforceButton
        type="big"
        onPress={() => proceed?.()}
      >
        <PayforceText.Body1Medium color={colors.white}>
          Continue
        </PayforceText.Body1Medium>
      </PayforceButton>

    </CustomBottomSheet>
  );
}

export default React.forwardRef(LoanBankUploadListBottomSheet);

const styles = StyleSheet.create({
  bankHeading: {
    flexDirection: 'row',
    gap: pixelSizeHorizontal(8),
    justifyContent: 'center',
  },
  bankWrapper: {
    backgroundColor: colors.palette.FMGreen[50],
    borderColor: colors.palette.FMGreen[300],
    borderRadius: 4,
    borderWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: pixelSizeVertical(16),
    paddingHorizontal: pixelSizeHorizontal(12),
    paddingVertical: pixelSizeVertical(12),
  },
  closeIcon: {
    alignItems: 'flex-end',
  },
  container: {
    flex: 1,
    justifyContent: 'space-between',
    paddingHorizontal: pixelSizeHorizontal(20),
  },
  heading1: {
    fontSize: 24,
    fontWeight: '600',

  },
  headingWrapper: {
    marginBottom: pixelSizeVertical(16),
    marginTop: pixelSizeVertical(14),
  },
  uploadBtn: {
    alignItems: 'center',
    backgroundColor: colors.palette.FMPrimary[50],
    borderColor: colors.palette.FMPrimary[300],
    borderRadius: 4,
    borderStyle: 'dashed',
    borderWidth: 1,
    display: 'flex',
    flexDirection: 'row',
    gap: 8,
    justifyContent: 'center',
    marginTop: pixelSizeVertical(16),
    paddingVertical: pixelSizeVertical(12),
    width: '100%',
  },
  uploadBtnText: {
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 20,
  },
  uploadDesc: {
    fontSize: 14,
    fontWeight: '400',
    marginVertical: pixelSizeVertical(8),
  },
  uploadDescPeriod: {
    fontWeight: '600',
  },
});
