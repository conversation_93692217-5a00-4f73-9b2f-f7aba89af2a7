import { BottomSheetModalMethods } from '@gorhom/bottom-sheet/lib/typescript/types';
import React from 'react';
import { StyleSheet, View } from 'react-native';
import { TouchableOpacity } from 'react-native-gesture-handler';

import { Assets, CancelModal } from '@/asset/index';
import { colors } from '@/common/styles/colors';
import { pixelSizeHorizontal, pixelSizeVertical } from '@/common/utilities/normalize';
import CustomBottomSheet from '@/components/BottomSheet/BottomSheet';
import { PayforceButton } from '@/components/PayforceButton';
import { PayforceText } from '@/components/PayforceText/PayforceText';

interface Props {
  onClose?: () => void;
}

function FailedBankStatementCheckBottomSheet({ onClose }: Props, ref: React.Ref<BottomSheetModalMethods>) {
  return (
    <CustomBottomSheet ref={ref} snapBegin="50%" snapMid="50%" snapEnd="50%" enablePanDownToClose onClose={onClose}>
      <View style={styles.container}>
        <View>
          <TouchableOpacity
            onPress={() => onClose?.()}
            style={styles.closeIcon}
          >
            <CancelModal />
          </TouchableOpacity>

          <View style={styles.headingWrapper}>
            <Assets.LoansIcons.loanFailedState />
            <PayforceText.Body2
              color={colors.black}
              style={styles.heading1}
            >
              Sorry, we found an issue!
            </PayforceText.Body2>
            <PayforceText.Body2
              color={colors.palette.FMBlack[600]}
              style={styles.uploadDesc}
            >
              You can not proceed due to an
              {' '}
              <PayforceText.Body2 color={colors.palette.error[500]} style={styles.highlight}>
                overdue loan
              </PayforceText.Body2>
              {' '}
              with [X Organization]. Clear your existing loan to get a loan with FMB.
            </PayforceText.Body2>

          </View>

          <PayforceButton
            type="big"
          >
            <PayforceText.Body1Medium color={colors.white}>
              Got it
            </PayforceText.Body1Medium>
          </PayforceButton>
        </View>

      </View>

    </CustomBottomSheet>
  );
}

export default React.forwardRef(FailedBankStatementCheckBottomSheet);

const styles = StyleSheet.create({
  closeIcon: {
    alignItems: 'flex-end',
  },
  container: {
    flex: 1,
    justifyContent: 'space-between',
    paddingHorizontal: pixelSizeHorizontal(20),
  },
  heading1: {
    fontSize: 18,
    fontWeight: '500',
    lineHeight: 28,
    textAlign: 'center',
  },
  headingWrapper: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: pixelSizeVertical(14),
  },
  highlight: {
    textDecorationColor: colors.palette.error[500],
    textDecorationLine: 'underline',
    textDecorationStyle: 'solid',
  },
  uploadDesc: {
    fontSize: 14,
    fontWeight: '400',
    marginVertical: pixelSizeVertical(8),
    textAlign: 'center',
  },
});
