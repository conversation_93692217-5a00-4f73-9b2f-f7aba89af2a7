import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';

import { Assets } from '@/asset/index';
import { colors } from '@/common/styles/colors';
import { fontPixel, pixelSizeHorizontal, pixelSizeVertical } from '@/common/utilities/normalize';
import { PayforceText } from '@/components/PayforceText/PayforceText';
import { useNavigation } from '@/routes/auth/(stacks)';

interface Props {
  order: number;
  name: string;
  applicationId: string;
  description: string;
}

enum ActionStatus {
  'Guarantor Required' = 'Guarantor Required',
  'Guarantor Unavailable' = 'Guarantor Unavailable',
  'Guarantor Declined' = 'Guarantor Declined',
  'Guarantor Rejected' = 'Guarantor Rejected',
  'Bank Statement Required' = 'Bank Statement Required',
  'Bank Statement Issues' = 'Bank Statement Issues',
}

function ApplicationStatusActionBanner({ name, description, applicationId }: Props) {
  const navigation = useNavigation();

  const handeleActionPress = () => {
    if (name.includes('Guarantor')) {
      navigation.navigate('LoanGuarantorsInfo', { applicationId, isUpdate: true });
      return;
    }

    if (name.includes('Bank Statement')) {
      navigation.navigate('LoanBankStatement', { applicationId, isUpdate: true });
    }
  };

  return (
    <View>
      {
        name === ActionStatus['Guarantor Declined']
          ? (
            <View style={styles.errorContainer}>
              <TouchableOpacity onPress={handeleActionPress}>
                <View style={styles.headingContainer}>
                  <View style={styles.headingTextWrapper}>
                    <Assets.LoansIcons.ActionErrorBell />
                    <PayforceText.Body2 color={colors.palette.error[800]} style={styles.headingTitle}>Action Required!</PayforceText.Body2>
                  </View>
                  <Assets.LoansIcons.CaretRight />
                </View>

                <View>
                  <PayforceText.Body2 color={colors.palette.error[800]} style={styles.actionTitle}>
                    {name}
                  </PayforceText.Body2>
                  <PayforceText.Body2 color={colors.palette.FMBlack[800]} style={styles.actionDesc}>
                    {description}
                  </PayforceText.Body2>
                </View>
              </TouchableOpacity>
            </View>
            )
          : (
            <View style={styles.container}>
              <TouchableOpacity onPress={handeleActionPress}>
                <View style={styles.headingContainer}>
                  <View style={styles.headingTextWrapper}>
                    <Assets.LoansIcons.ActionWarningBell />
                    <PayforceText.Body2 color={colors.palette.warning[800]} style={styles.headingTitle}>Action Required!</PayforceText.Body2>
                  </View>
                  <Assets.LoansIcons.CaretRight />
                </View>

                <View>
                  <PayforceText.Body2 color={colors.palette.warning[900]} style={styles.actionTitle}>
                    {name}
                  </PayforceText.Body2>
                  <PayforceText.Body2 color={colors.palette.FMBlack[800]} style={styles.actionDesc}>
                    {description}
                  </PayforceText.Body2>
                </View>
              </TouchableOpacity>
            </View>
            )
      }

    </View>
  );
}

export default ApplicationStatusActionBanner;

const styles = StyleSheet.create({
  actionDesc: {
    fontSize: fontPixel(10),
    lineHeight: 14,
  },
  actionTitle: {
    fontSize: fontPixel(14),
    fontWeight: '500',
    lineHeight: 20,
    marginBottom: pixelSizeVertical(4),
    marginTop: pixelSizeVertical(8),
  },
  container: {
    backgroundColor: colors.palette.warning[50],
    borderColor: colors.palette.warning[100],
    borderRadius: 4,
    borderWidth: 1,
    marginVertical: pixelSizeVertical(14),
    paddingHorizontal: pixelSizeHorizontal(12),
    paddingVertical: pixelSizeVertical(8),
  },
  errorContainer: {
    backgroundColor: colors.palette.error[50],
    borderColor: colors.palette.error[100],
    borderRadius: 4,
    borderWidth: 1,
    marginVertical: pixelSizeVertical(14),
    paddingHorizontal: pixelSizeHorizontal(12),
    paddingVertical: pixelSizeVertical(8),
  },
  headingContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  headingTextWrapper: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: 8,
  },
  headingTitle: {
    fontSize: fontPixel(12),
    fontWeight: '500',
    lineHeight: 18,
  },
});
