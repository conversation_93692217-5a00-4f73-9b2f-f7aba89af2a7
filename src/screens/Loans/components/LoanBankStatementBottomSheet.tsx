import { BottomSheetModalMethods } from '@gorhom/bottom-sheet/lib/typescript/types';
import { useNavigation } from '@react-navigation/native';
import React from 'react';
import { StyleSheet, View } from 'react-native';
import { TouchableOpacity } from 'react-native-gesture-handler';

import { Assets, CancelModal } from '@/asset/index';
import { colors } from '@/common/styles/colors';
import { pixelSizeHorizontal, pixelSizeVertical } from '@/common/utilities/normalize';
import CustomBottomSheet from '@/components/BottomSheet/BottomSheet';
import { PayforceButton } from '@/components/PayforceButton';
import { PayforceText } from '@/components/PayforceText/PayforceText';

interface Props {
  onClose?: () => void;
  uploadStatement?: () => void;
  applicationId?: string;
}

function LoanBankStatementBottomSheet({ onClose, uploadStatement, applicationId }: Props, ref: React.Ref<BottomSheetModalMethods>) {
  const navigation = useNavigation();

  const options = [
    // ------ TODO: Uncomment when email feature is implemented ------
    // {
    //   title: 'Send Directly From Your Bank',
    //   desc: 'Use your bank app or request your bank to send the statement to our email.',
    //   icon: <Assets.LoansIcons.OfflineMail />,
    //   action: () => {
    //     onClose?.();
    //     navigation.navigate('LoanBankStatementEmail', { applicationId });
    //   },
    // },
    {
      title: 'Upload Bank Statement',
      desc: 'Download a minimum of 1 year’s bank statement and upload it.',
      icon: <Assets.LoansIcons.OfflineUpload />,
      action: () => {
        uploadStatement?.();
      },
    },
  ];

  return (
    <CustomBottomSheet ref={ref} snapBegin="55%" snapMid="55%" snapEnd="55%" enablePanDownToClose onClose={onClose}>
      <View style={styles.container}>
        <View>
          <TouchableOpacity
            onPress={() => onClose?.()}
            style={styles.closeIcon}
          >
            <CancelModal />
          </TouchableOpacity>

          <View style={styles.headingWrapper}>
            <PayforceText.Body2
              color={colors.black}
              style={styles.heading1}
            >
              Submit bank Statement
            </PayforceText.Body2>
            <PayforceText.Body2
              color={colors.palette.FMBlack[600]}
              style={styles.uploadDesc}
            >
              Submit a bank statement of
              {' '}
              <PayforceText.Body2 color={colors.palette.FMBlack[800]} style={styles.uploadDescPeriod}>1 to 3 years</PayforceText.Body2>
              {' '}
              to continue
            </PayforceText.Body2>

          </View>

          {options.map((option, index) => (
            <TouchableOpacity style={styles.optionWrapper} key={index} onPress={option.action}>
              <View style={styles.optionDetails}>
                <View style={styles.optionIcon}>
                  {option.icon}

                </View>
                <View>
                  <PayforceText.Body2
                    color={colors.palette.FMBlack[800]}
                    style={styles.optionTitle}
                  >
                    {option.title}
                  </PayforceText.Body2>
                  <PayforceText.Body2
                    color={colors.palette.FMBlack[500]}
                    style={styles.optionDesc}
                  >
                    {option.desc}
                  </PayforceText.Body2>
                </View>
              </View>

              <View>
                <Assets.LoansIcons.CaretRight />
              </View>
            </TouchableOpacity>
          ))}
        </View>

        <PayforceButton
          type="big"
          // style={styles.GrantBtn}
          onPress={() => {
            onClose?.();
            navigation.navigate('LoanDocumentPrep', { applicationId });
          }}
        >
          <PayforceText.Body1Medium color={colors.white} style={styles.doLater}>
            {`Do this later >>`}
          </PayforceText.Body1Medium>
        </PayforceButton>
      </View>

    </CustomBottomSheet>
  );
}

export default React.forwardRef(LoanBankStatementBottomSheet);

const styles = StyleSheet.create({
  closeIcon: {
    alignItems: 'flex-end',
  },
  container: {
    flex: 1,
    justifyContent: 'space-between',
    paddingHorizontal: pixelSizeHorizontal(20),
  },
  doLater: {
    textAlign: 'center',
  },
  heading1: {
    fontSize: 24,
    fontWeight: '600',
    textAlign: 'center',
  },
  headingWrapper: {
    marginBottom: pixelSizeVertical(24),
    marginTop: pixelSizeVertical(14),
  },
  optionDesc: {
    fontSize: 12,
  },
  optionDetails: {
    flexDirection: 'row',
    gap: pixelSizeVertical(8),
    width: '80%',
  },
  optionIcon: {
    alignItems: 'center',
    backgroundColor: colors.palette.FMGreen[500],
    borderRadius: 50,
    height: 32,
    justifyContent: 'center',
    width: 32,
  },
  optionTitle: {
    fontSize: 14,
    fontWeight: '500',
  },
  optionWrapper: {
    backgroundColor: colors.palette.FMGreen[50],
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: pixelSizeVertical(12),
    paddingHorizontal: pixelSizeHorizontal(16),
    paddingVertical: pixelSizeVertical(16),
  },
  uploadDesc: {
    fontSize: 14,
    fontWeight: '400',
    marginVertical: pixelSizeVertical(8),
    textAlign: 'center',
  },
  uploadDescPeriod: {
    fontWeight: '600',
  },
});
