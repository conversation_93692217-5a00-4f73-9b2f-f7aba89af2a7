import React from 'react';
import { StyleSheet, View } from 'react-native';

import EmptyFolderIcon from '@/assets/svg/empty_folder_icon.svg';
import { colors } from '@/common/styles/colors';
import { heightPixel } from '@/common/utilities/normalize';
import { PayforceText } from '@/components/PayforceText/PayforceText';

function OfflineApplicationRequestLoan() {
  return (
    <View style={styles.contentContainer}>
      <EmptyFolderIcon />
      <PayforceText.Body2
        color={colors.palette.FMBlack[500]}
        style={styles.alignCenter}
      >
        You currently do not have an active loan. All loan information will
        be displayed here
      </PayforceText.Body2>
    </View>
  );
}

export default OfflineApplicationRequestLoan;

const styles = StyleSheet.create({
  alignCenter: { textAlign: 'center' },
  contentContainer: {
    alignItems: 'center',
    flex: 1,
    height: heightPixel(500),
    justifyContent: 'center',
  },

});
