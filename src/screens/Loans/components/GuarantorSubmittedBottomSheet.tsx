import { BottomSheetModalMethods } from '@gorhom/bottom-sheet/lib/typescript/types';
import React from 'react';
import { StyleSheet, View } from 'react-native';

import { Assets } from '@/asset/index';
import { colors } from '@/common/styles/colors';
import { pixelSizeHorizontal, pixelSizeVertical } from '@/common/utilities/normalize';
import CustomBottomSheet from '@/components/BottomSheet/BottomSheet';
import { PayforceButton } from '@/components/PayforceButton';
import { PayforceText } from '@/components/PayforceText/PayforceText';
import { useAppSelector } from '@/store/hooks';
import { selectAgent } from '@/store/user/selectors';

interface Props {
  onClose?: () => void;
}

function QuarantorSubmittedBottomSheet({ onClose }: Props, ref: React.Ref<BottomSheetModalMethods>) {
  const agentDetails = useAppSelector(selectAgent);

  return (
    <CustomBottomSheet ref={ref} snapBegin="50%" snapMid="50%" snapEnd="50%" enablePanDownToClose onClose={onClose}>
      <View style={styles.container}>
        <View>

          <View style={styles.headingWrapper}>
            <Assets.LoansIcons.loanSuccess />
            <PayforceText.Body2
              color={colors.black}
              style={styles.heading1}
            >
              Guarantor’s Information Submitted
            </PayforceText.Body2>
            <PayforceText.Body2
              color={colors.palette.FMBlack[500]}
              style={styles.uploadDesc}
            >
              You can now request for your guarantor’s code to be sent to your email
              {' '}
              {agentDetails?.email}
              .
            </PayforceText.Body2>

          </View>

          <PayforceButton
            type="big"
            onPress={onClose}
          >
            <PayforceText.Body1Medium color={colors.white}>
              Request Code
            </PayforceText.Body1Medium>
          </PayforceButton>
          <PayforceButton
            type="big"
            onPress={onClose}
            style={styles.closeBtn}
          >
            <PayforceText.Body1Medium color={colors.palette.FMBlack[800]}>
              Close
            </PayforceText.Body1Medium>
          </PayforceButton>
        </View>

      </View>

    </CustomBottomSheet>
  );
}

export default React.forwardRef(QuarantorSubmittedBottomSheet);

const styles = StyleSheet.create({

  closeBtn: {
    backgroundColor: 'transparent',
    borderColor: colors.palette.FMBlack[300],
    borderWidth: 1,
    marginTop: pixelSizeVertical(8),
  },
  container: {
    flex: 1,
    justifyContent: 'space-between',
    paddingHorizontal: pixelSizeHorizontal(20),
  },
  heading1: {
    fontSize: 20,
    fontWeight: '500',
    marginTop: pixelSizeVertical(8),
    textAlign: 'center',
  },
  headingWrapper: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: pixelSizeVertical(32),
    marginTop: pixelSizeVertical(32),
  },
  uploadDesc: {
    fontSize: 14,
    fontWeight: '400',
    marginTop: pixelSizeVertical(8),
    textAlign: 'center',
  },
});
