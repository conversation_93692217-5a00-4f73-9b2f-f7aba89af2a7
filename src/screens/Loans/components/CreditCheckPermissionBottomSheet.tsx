import AndroidAccountModule from '@credolab/react-android-account';
import AndroidContactModule from '@credolab/react-android-contact';
import AndroidSmsModule from '@credolab/react-android-sms';
import { BehavioralModule } from '@credolab/react-behavioral';
import CredoAppService from '@credolab/react-core';
import IosContactModule from '@credolab/react-ios-contact';
import { BottomSheetModalMethods } from '@gorhom/bottom-sheet/lib/typescript/types';
import { useNavigation } from '@react-navigation/native';
import React from 'react';
import { StyleSheet, View } from 'react-native';
import { getIpAddress } from 'react-native-device-info';
import { TouchableOpacity } from 'react-native-gesture-handler';
import Toast from 'react-native-toast-message';

import { CancelModal } from '@/asset/index';
import CheckInvolvement from '@/assets/svg/check-involvement.svg';
import CreditCheck from '@/assets/svg/loan-credit-check.svg';
import { colors } from '@/common/styles/colors';
import {
  heightPixel,
  pixelSizeHorizontal,
  pixelSizeVertical,
  widthPixel,
} from '@/common/utilities/normalize';
import CustomBottomSheet from '@/components/BottomSheet/BottomSheet';
import { PayforceButton } from '@/components/PayforceButton';
import { PayforceText } from '@/components/PayforceText/PayforceText';
import { FlexedView } from '@/components/PayForceView';
import { useCreateBottomSheetModal } from '@/utils/helper';

import { useApplicationCreditCheck } from '../hooks/mutations/loanApplication.mutation';
import CreditEvaluationBottomSheet from './CreditEvaluationBottomSheet';
import ProfileReviewBottomSheet from './ProfileReviewBottomSheet';

export type ICreditCheckPermissionBottomSheetProps = {
  onClose: () => void;
  onGrantAccess?: () => void;
  onEntailmentCheck?: () => void;
  applicationId?: string;
};

const CreditCheckPermissionBottomSheet = React.forwardRef(
  (
    { onClose, applicationId }: ICreditCheckPermissionBottomSheetProps,
    ref: React.Ref<BottomSheetModalMethods>,
  ) => {
    const navigation = useNavigation();

    // ------ intialize credolab modules ------
    const service = new CredoAppService();
    service.addModuleAsync(new AndroidSmsModule());
    service.addModuleAsync(new AndroidContactModule());
    service.addModuleAsync(new AndroidAccountModule());
    service.addModuleAsync(new IosContactModule());
    service.addModuleAsync(new BehavioralModule());

    const { mutate } = useApplicationCreditCheck();

    const [
      creditEvaluationRef,
      openCreditEvaluationModal,
      closeCreditEvaluationModal,
    ] = useCreateBottomSheetModal();

    const [
      profileVerificationRef,
      openProfileVerificationModal,
      closeProfileVerificationModal,
    ] = useCreateBottomSheetModal();

    const handleApplicationEvaluation = async () => {
      openProfileVerificationModal();
      const ipAddress = await getIpAddress();

      if (ipAddress) {
        const credoLabData = await service.collectAsync();

        if (credoLabData) {
          mutate({ userIp: ipAddress, credoLabData, applicationId: applicationId as string }, {
            onSuccess: (resp) => {
              profileVerificationRef.current?.close();

              if (resp?.status === 400) {
                Toast.show({
                  type: 'error',
                  text1: resp.data.error_message,
                });

                onClose();
              }

              if (resp?.status === 200) {
                navigation.navigate('LoanBankStatement', { applicationId });
                onClose();
              }
            },
          });
        }
      }
    };

    return (
      <>
        <CustomBottomSheet
          ref={ref}
          closeModal={onClose}
          snapBegin="85%"
          snapEnd="85%"
          snapMid="85%"
        >
          <TouchableOpacity
            onPress={() => onClose()}
            style={styles.CancelModalBtn}
          >
            <CancelModal />
          </TouchableOpacity>
          <FlexedView style={styles.horizontalContainer}>
            <View>
              <View style={styles.itemCtn}>
                <CreditCheck />
                <View
                  style={styles.wrapperContainer}
                >
                  <PayforceText.Body3Medium
                    style={styles.CentralizedText}
                    color={colors.palette.FMBlack[800]}
                  >
                    Credit Check Permission
                  </PayforceText.Body3Medium>
                  <PayforceText.Body2
                    style={styles.CentralizedText}
                    color={colors.palette.FMBlack[500]}
                  >
                    We need your consent to perform a background check on your
                    financial credibility. Don’t worry—your data is fully
                    protected with advanced security measures.
                  </PayforceText.Body2>
                </View>
              </View>
            </View>
            <PayforceButton
              type="big"
              style={styles.GrantBtn}
              onPress={() => {
                handleApplicationEvaluation();
              }}
            >
              <PayforceText.Body1Medium color={colors.white}>
                Grant access
              </PayforceText.Body1Medium>
            </PayforceButton>
          </FlexedView>

          <TouchableOpacity
            onPress={() => openCreditEvaluationModal()}
            style={styles.involvementContainer}
          >
            <CheckInvolvement />
            <View style={styles.marginHorizontal}>
              <PayforceText.Body2 color={colors.black}>
                What does this check involve?
              </PayforceText.Body2>
              <PayforceText.IconLabel color={colors.palette.FMBlack[500]}>
                Learn more about the credit bureau process here
              </PayforceText.IconLabel>
            </View>
          </TouchableOpacity>
        </CustomBottomSheet>

        <CreditEvaluationBottomSheet
          ref={creditEvaluationRef}
          onClose={closeCreditEvaluationModal}
          onOpen={openCreditEvaluationModal}
        />

        <ProfileReviewBottomSheet
          ref={profileVerificationRef}
          onClose={closeProfileVerificationModal}
          onPress={openProfileVerificationModal}
        />
      </>
    );
  },
);

export default CreditCheckPermissionBottomSheet;

const styles = StyleSheet.create({
  CancelModalBtn: {
    alignItems: 'center',
    alignSelf: 'flex-end',
    backgroundColor: colors.palette.FMBlack[100],
    borderRadius: 50,
    height: heightPixel(45),
    justifyContent: 'center',
    marginRight: pixelSizeHorizontal(20),
    width: widthPixel(45),
  },
  CentralizedText: {
    paddingVertical: pixelSizeVertical(8),
    textAlign: 'center',
  },
  GrantBtn: {
    borderRadius: 4,
    marginTop: pixelSizeVertical(16),
    width: widthPixel(206),
  },
  horizontalContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: pixelSizeHorizontal(15),
  },
  involvementContainer: {
    backgroundColor: colors.palette.FMGreen[50],
    borderColor: colors.palette.FMGreen[200],
    borderRadius: 4,
    borderStyle: 'dashed',
    borderWidth: 1,
    flexDirection: 'row',
    marginHorizontal: pixelSizeHorizontal(20),
    marginVertical: pixelSizeVertical(50),
    paddingHorizontal: pixelSizeHorizontal(10),
    paddingVertical: pixelSizeVertical(8),
  },
  itemCtn: { alignItems: 'center' },
  marginHorizontal: {
    marginHorizontal: pixelSizeHorizontal(5),
  },
  wrapperContainer: {
    alignItems: 'center',
    marginVertical: pixelSizeVertical(24),
  },
});
