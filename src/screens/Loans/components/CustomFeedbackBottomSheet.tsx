import { BottomSheetModalMethods } from '@gorhom/bottom-sheet/lib/typescript/types';
import React from 'react';
import { StyleSheet, View } from 'react-native';
import { TouchableOpacity } from 'react-native-gesture-handler';

import { CancelModal } from '@/asset/index';
import WarningIcon from '@/assets/svg/warning_icon.svg';
import { colors } from '@/common/styles/colors';
import {
  heightPixel,
  pixelSizeHorizontal,
  pixelSizeVertical,
  widthPixel,
} from '@/common/utilities/normalize';
import CustomBottomSheet from '@/components/BottomSheet/BottomSheet';
import { ListSeparator } from '@/components/ListSeparator';
import { PayforceButton } from '@/components/PayforceButton';
import { PayforceText } from '@/components/PayforceText/PayforceText';

export type ICustomBottomSheetProps = {
  title: string;
  description: string;
  onClose: () => void;
  onfirstBtnPress: () => void;
  onSecondBtnPress?: () => void;
  firstButtonLabel: string;
  secondButtonLabel?: string;
  secondButtonColor?: string;
};

const CustomFeedbackBottomSheet = React.forwardRef(
  (
    {
      title,
      description,
      firstButtonLabel,
      secondButtonLabel,
      onClose,
      onfirstBtnPress,
      onSecondBtnPress,
    }: ICustomBottomSheetProps,
    ref: React.Ref<BottomSheetModalMethods>,
  ) => (
    <>
      <CustomBottomSheet
        ref={ref}
        closeModal={onClose}
        snapBegin="50%"
        snapEnd="50%"
        snapMid="50%"
      >
        <TouchableOpacity onPress={onClose} style={styles.closeModalBtn}>
          <CancelModal />
        </TouchableOpacity>

        <View style={styles.horizontalContainer}>
          <WarningIcon />
          <View
            style={styles.titleCtn}
          >
            <PayforceText.ModalHeader
              style={styles.CentralizedText}
              color={colors.palette.FMBlack[800]}
            >
              {title}
            </PayforceText.ModalHeader>
            <PayforceText.Body2
              style={styles.CentralizedText}
              color={colors.palette.FMBlack[500]}
            >
              {description}
            </PayforceText.Body2>
          </View>
          <ListSeparator height={30} />

          <PayforceButton
            type="small"
            onPress={onfirstBtnPress}
            color={colors.palette.FMBlack[900]}
          >
            <PayforceText.Body1Medium color={colors.white}>
              {firstButtonLabel}
            </PayforceText.Body1Medium>
          </PayforceButton>
          <ListSeparator height={15} />

          {secondButtonLabel && (
            <PayforceButton
              type="small"
              color={colors.white}
              onPress={onSecondBtnPress || onClose}
              style={styles.btnSecondary}
            >
              <PayforceText.Body1Medium color="red">
                {secondButtonLabel}
              </PayforceText.Body1Medium>
            </PayforceButton>
          )}
        </View>
      </CustomBottomSheet>
    </>
  ),
);

export default CustomFeedbackBottomSheet;

const styles = StyleSheet.create({
  CentralizedText: {
    paddingHorizontal: pixelSizeHorizontal(40),
    paddingVertical: pixelSizeVertical(4),
    textAlign: 'center',
  },
  btnSecondary: { borderColor: colors.palette.error[500], borderWidth: 1 },
  closeModalBtn: {
    alignItems: 'center',
    alignSelf: 'flex-end',
    backgroundColor: colors.palette.FMBlack[100],
    borderRadius: 50,
    height: heightPixel(45),
    justifyContent: 'center',
    marginRight: pixelSizeHorizontal(20),
    width: widthPixel(45),
  },
  horizontalContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: pixelSizeHorizontal(15),
    marginVertical: pixelSizeVertical(24),
  },
  lottieView: {
    height: 50,
    width: 180,
  },
  processingContainer: {
    alignItems: 'center',
    backgroundColor: colors.palette.FMBlack[100],
    borderRadius: 4,
    height: heightPixel(42),
    justifyContent: 'center',
    width: widthPixel(197),
  },
  titleCtn: {
    alignItems: 'center',
  },
});
