import { BottomSheetModalMethods } from '@gorhom/bottom-sheet/lib/typescript/types';
import { useNavigation } from '@react-navigation/native';
import React from 'react';
import { StyleSheet, View } from 'react-native';
import { TouchableOpacity } from 'react-native-gesture-handler';

import { Assets, CancelModal } from '@/asset/index';
import { colors } from '@/common/styles/colors';
import { pixelSizeHorizontal, pixelSizeVertical } from '@/common/utilities/normalize';
import CustomBottomSheet from '@/components/BottomSheet/BottomSheet';
import { PayforceButton } from '@/components/PayforceButton';
import { PayforceText } from '@/components/PayforceText/PayforceText';

interface Props {
  onClose?: () => void;
  uploadStatement?: () => void;
  applicationId?: string;
}

function FailedLoanBankStatementBottomSheet({ onClose, uploadStatement, applicationId }: Props, ref: React.Ref<BottomSheetModalMethods>) {
  const navigation = useNavigation();

  const options = [
    {
      title: 'Send Directly from your bank',
      desc: 'You can send from your bank to our email',
      icon: <Assets.LoansIcons.OfflineFailedMail />,
      action: () => {
        onClose?.();
        navigation.navigate('LoanBankStatementEmail', { applicationId });
      },
    },
    {
      title: 'Upload bank statement manually',
      desc: 'Minimum of 1 year bank statement needed',
      icon: <Assets.LoansIcons.OfflineFailedUpload />,
      action: () => {
        uploadStatement?.();
      },
    },
  ];

  return (
    <CustomBottomSheet ref={ref} snapBegin="70%" snapMid="70%" snapEnd="70%" enablePanDownToClose onClose={onClose}>
      <View style={styles.container}>
        <View>
          <TouchableOpacity
            onPress={() => onClose?.()}
            style={styles.closeIcon}
          >
            <CancelModal />
          </TouchableOpacity>

          <View style={styles.headingWrapper}>
            <Assets.LoansIcons.loanFailedState />
            <PayforceText.Body2
              color={colors.black}
              style={styles.heading1}
            >
              Issue fetching your bank statement
            </PayforceText.Body2>
            <PayforceText.Body2
              color={colors.palette.FMBlack[600]}
              style={styles.uploadDesc}
            >
              We are unable to process your loan request at this time due to server issues. Please try again or try alternative methods.
            </PayforceText.Body2>

          </View>

          {options.map((option, index) => (
            <View style={styles.optionWrapper} key={index}>
              <View style={styles.optionDetails}>
                <View style={styles.optionIcon}>
                  {option.icon}

                </View>
                <View>
                  <PayforceText.Body2
                    color={colors.palette.FMBlack[800]}
                    style={styles.optionTitle}
                  >
                    {option.title}
                  </PayforceText.Body2>
                  <PayforceText.Body2
                    color={colors.palette.FMBlack[500]}
                    style={styles.optionDesc}
                  >
                    {option.desc}
                  </PayforceText.Body2>
                </View>
              </View>

              <TouchableOpacity onPress={option.action}>
                <Assets.LoansIcons.CaretRight />
              </TouchableOpacity>
            </View>
          ))}
        </View>

        <View>
          <PayforceButton
            type="big"
          >
            <PayforceText.Body1Medium color={colors.white}>
              Try again
            </PayforceText.Body1Medium>
          </PayforceButton>
          <TouchableOpacity>
            <PayforceText.Body2
              color={colors.palette.FMGreen[500]}
              style={styles.doLater}
            >
              {`Do this later >>`}
            </PayforceText.Body2>
          </TouchableOpacity>
        </View>
      </View>

    </CustomBottomSheet>
  );
}

export default React.forwardRef(FailedLoanBankStatementBottomSheet);

const styles = StyleSheet.create({
  closeIcon: {
    alignItems: 'flex-end',
  },
  container: {
    flex: 1,
    justifyContent: 'space-between',
    paddingHorizontal: pixelSizeHorizontal(20),
  },
  doLater: {
    fontSize: 14,
    fontWeight: '600',
    marginVertical: pixelSizeVertical(16),
    textAlign: 'center',
  },
  heading1: {
    fontSize: 18,
    fontWeight: '500',
    lineHeight: 28,
    textAlign: 'center',
  },
  headingWrapper: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: pixelSizeVertical(14),
  },
  optionDesc: {
    fontSize: 12,
  },
  optionDetails: {
    flexDirection: 'row',
    gap: pixelSizeVertical(8),
    width: '80%',
  },
  optionIcon: {
    alignItems: 'center',
    backgroundColor: colors.palette.FMPrimary[200],
    borderRadius: 4,
    height: 32,
    justifyContent: 'center',
    width: 32,
  },
  optionTitle: {
    fontSize: 14,
    fontWeight: '500',
  },
  optionWrapper: {
    backgroundColor: colors.palette.FMBlack[100],
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: pixelSizeVertical(24),
    paddingHorizontal: pixelSizeHorizontal(16),
    paddingVertical: pixelSizeVertical(16),
  },
  uploadDesc: {
    fontSize: 14,
    fontWeight: '400',
    marginVertical: pixelSizeVertical(8),
    textAlign: 'center',
  },
});
