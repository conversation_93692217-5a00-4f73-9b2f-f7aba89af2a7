import { BottomSheetModalMethods } from '@gorhom/bottom-sheet/lib/typescript/types';
import LottieView from 'lottie-react-native';
import React from 'react';
import { StyleSheet, View } from 'react-native';
import { TouchableOpacity } from 'react-native-gesture-handler';

import { CancelModal } from '@/asset/index';
import { colors } from '@/common/styles/colors';
import {
  heightPixel,
  pixelSizeHorizontal,
  pixelSizeVertical,
  widthPixel,
} from '@/common/utilities/normalize';
import CustomBottomSheet from '@/components/BottomSheet/BottomSheet';
import { PayforceText } from '@/components/PayforceText/PayforceText';
import { FlexedView } from '@/components/PayForceView';
import { useCreateBottomSheetModal } from '@/utils/helper';

import loadingAnimation from '../../../common/loader/loadingAnimation.json';
import CustomFeedbackBottomSheet from './CustomFeedbackBottomSheet';

export type IProfileReviewBottomSheetProps = {
  onClose: () => void;
  onGrantAccess?: () => void;
  onPress?: () => void;
};
const ProfileReviewBottomSheet = React.forwardRef(
  (
    { onClose }: IProfileReviewBottomSheetProps,
    ref: React.Ref<BottomSheetModalMethods>,
  ) => {
    const [
      CustomBottomSheetRef,
      openCustomBottomSheetModal,
      closeCustomBottomSheetModal,
    ] = useCreateBottomSheetModal();

    const cancelCheck = () => {
      onClose();
      closeCustomBottomSheetModal();
    };

    return (
      <>
        <CustomBottomSheet
          ref={ref}
          closeModal={onClose}
          snapBegin="80%"
          snapEnd="85%"
          snapMid="80%"
        >
          <TouchableOpacity
            onPress={() => openCustomBottomSheetModal()}
            style={styles.closeModalBtn}
          >
            <CancelModal />
          </TouchableOpacity>
          <FlexedView style={styles.horizontalContainer}>
            <View
              style={styles.wrapperContainer}
            >
              <LottieView
                autoPlay
                loop
                resizeMode="cover"
                source={loadingAnimation}
                style={styles.lottieView}
              />
              <PayforceText.ModalHeader
                style={styles.CentralizedText}
                color={colors.palette.FMBlack[800]}
              >
                Reviewing your profile
              </PayforceText.ModalHeader>
              <PayforceText.Body2
                style={styles.CentralizedText}
                color={colors.palette.FMBlack[500]}
              >
                We are verifying your identity and financial credibility. Please
                wait a moment.
              </PayforceText.Body2>
            </View>
            <View style={styles.processingContainer}>
              <PayforceText.Decorative color={colors.palette.FMBlack[500]}>
                Processing your secured data...
              </PayforceText.Decorative>
            </View>
          </FlexedView>
        </CustomBottomSheet>

        <CustomFeedbackBottomSheet
          ref={CustomBottomSheetRef}
          onClose={closeCustomBottomSheetModal}
          title="Are you sure you want to stop"
          description="You will have to start the process all over again if you cancel now."
          firstButtonLabel="Continue check"
          onfirstBtnPress={closeCustomBottomSheetModal}
          secondButtonLabel="Stop check"
          onSecondBtnPress={cancelCheck}
        />
      </>
    );
  },
);

export default ProfileReviewBottomSheet;

const styles = StyleSheet.create({
  CentralizedText: {
    paddingHorizontal: pixelSizeHorizontal(40),
    paddingVertical: pixelSizeVertical(4),
    textAlign: 'center',
  },
  closeModalBtn: {
    alignItems: 'center',
    alignSelf: 'flex-end',
    backgroundColor: colors.palette.FMBlack[100],
    borderRadius: 50,
    height: heightPixel(45),
    justifyContent: 'center',
    marginRight: pixelSizeHorizontal(20),
    width: widthPixel(45),
  },
  horizontalContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  lottieView: {
    height: 50,
    width: 180,
  },
  processingContainer: {
    alignItems: 'center',
    backgroundColor: colors.palette.FMBlack[100],
    borderRadius: 4,
    height: heightPixel(42),
    justifyContent: 'center',
    width: widthPixel(197),
  },
  wrapperContainer: {
    alignItems: 'center',
    marginVertical: pixelSizeVertical(24),
  },
});
