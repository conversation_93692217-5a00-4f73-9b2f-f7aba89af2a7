import React, { useEffect, useState } from 'react';
import {
  FlatList,
  ListRenderItem,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import ProgressBar from 'src/screens/Transactions/components/ProgressBar';

import { colors } from '@/common/styles/colors';
import { normalize } from '@/common/utilities/normalize';
import { ListSeparator } from '@/components/ListSeparator';
import { PayforceImage } from '@/components/PayforceImage';
import { TopNav } from '@/components/PayForceKYC';
import { PayforceText } from '@/components/PayforceText/PayforceText';
import SearchBar from '@/screens/SendMoney/components/SearchBar';

import { TLoansBank } from '../hooks/queries/loanMandate.query';

type Props = {
  onPress: () => void;
  label: string;
  backPress: () => void;
  bankList?: TLoansBank[];
  suggestList?: TLoansBank[];
  selectBillerItem: (biller: TLoansBank) => void;
};

const getNetworkStability = (transferSuccessRate: string) => {
  if (transferSuccessRate === null || transferSuccessRate === undefined) {
    return 'No data found';
  }

  const rate = Number(transferSuccessRate);

  if (rate >= 80) {
    return 'Stable network';
  }

  if (rate > 40) {
    return 'Fairly stable network';
  }

  return 'Unstable network';
};

const getNetworkColor = (transferSuccessRate: string) => {
  if (transferSuccessRate === null || transferSuccessRate === undefined) {
    return colors.palette.FMBlack[500];
  }
  const rate = Number(transferSuccessRate);

  if (rate >= 80) {
    return colors.palette.FMGreen[500];
  }
  if (rate > 40) {
    return colors.pending;
  }
  return colors.open;
};

const AllBanksList = ({
  onPress,
  label,
  backPress,
  bankList,
  suggestList,
  selectBillerItem,
}: Props) => {
  const renderItem: ListRenderItem<TLoansBank> = ({ item }) => {
    const { name, id, transferSucceessRate, logoUrl } = item;
    const color = transferSucceessRate ? getNetworkColor(transferSucceessRate) : colors.palette.FMBlack[500];

    return (
      <TouchableOpacity
        key={id}
        onPress={() => {
          selectBillerItem(item);
          onPress();
        }}
        style={styles.container}
      >
        {transferSucceessRate == null
          ? (
            <View>
              {logoUrl
                ? (
                  <PayforceImage
                    source={{ uri: logoUrl }}
                    style={styles.suggestLogo}
                    resizeMode="contain"
                  />
                  )
                : (
                  <View style={styles.logoBox}>
                    <PayforceText.Body2
                      numberOfLines={2}
                      color="label"
                      style={styles.selectedText}
                    >
                      {name}
                    </PayforceText.Body2>
                  </View>
                  )}
            </View>
            )
          : (
            <ProgressBar
              percentage={Number(transferSucceessRate)}
              color={color}
              textColor={color}
            />
            )}
        <ListSeparator width={8} />
        <View>
          <PayforceText.Body1 color={colors.palette.FMBlack[800]}>
            {name}
          </PayforceText.Body1>
          {transferSucceessRate != null && (
            <PayforceText.Body1 style={styles.subtext} color="form">
              {getNetworkStability(transferSucceessRate)}
            </PayforceText.Body1>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const [searchPhrase, setSearchPhrase] = useState('');
  const [data, setData] = useState<TLoansBank[] | []>();

  const handleSearch = (text: string) => {
    setSearchPhrase(text);

    const filteredData
      = text === ''
        ? bankList
        : bankList?.filter((item) =>
          item.name.toLowerCase().includes(text.toLowerCase()));

    setData(filteredData);
  };

  useEffect(() => {
    setData(bankList);
  }, []);
  // const createRenameSelectedBank = (bank: TLoansBank) => ({
  //   biller_code: 'ZCDT',
  //   id: bank.id,
  //   logoUrl: bank.logoUrl,
  //   payment_code: bank.bankCode,
  //   bankName: bank.name,
  //   transferSucceessRate: bank.transferSucceessRate,
  //   // ussd_code: bank.ussd_code,
  //   // ussd_transfer_string: bank.ussd_transfer_string,
  //   bank_code: bank.bankCode,
  //   name: bank.name,
  // });

  return (
    <View>
      <TopNav onPress={backPress} />

      <ListSeparator height={16} />
      <PayforceText.Body2 color="label">{`${label}`}</PayforceText.Body2>
      {bankList && bankList?.length > 0 && (
        <PayforceText.Body1 color="form" style={styles.subtitle}>
          Real time transfer success rate of each bank has been measured for you
          to avoid transfer delay or failure.
        </PayforceText.Body1>
      )}
      <ListSeparator height={16} />
      <SearchBar onChange={handleSearch} placeholder="Search bank name" />

      <ListSeparator height={16} />
      {suggestList && !searchPhrase && (
        <View style={styles.baneficiarybox}>
          <FlatList
            ListHeaderComponent={(
              <View>
                <PayforceText.Body2 color="label">
                  Suggested Banks
                </PayforceText.Body2>
                <ListSeparator height={8} />
              </View>
            )}
            bounces={false}
            data={suggestList}
            keyExtractor={(item, index) => `${item?.bankCode + index}`}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={() => (
              <PayforceText.Body1 color="label">
                Getting banks
              </PayforceText.Body1>
            )}
            renderItem={({ item: bank }) => (
              <TouchableOpacity
                onPress={() => {
                  // this is a quick fix on transaction and can be refactored
                  // const renameSelectedBank = createRenameSelectedBank(bank);
                  // selectBillerItem(renameSelectedBank);
                  selectBillerItem(bank);
                  // this is  removed and will be modified after review
                  // selectBillerItem(bank);
                  onPress();
                }}
                style={styles.container}
              >
                {bank?.transferSucceessRate == null
                  ? (
                    <View>
                      {bank?.logoUrl
                        ? (
                          <PayforceImage
                            source={{ uri: bank?.logoUrl }}
                            style={styles.suggestLogo}
                            resizeMode="contain"
                          />
                          )
                        : (
                          <View style={styles.logoBox}>
                            <PayforceText.Body2
                              numberOfLines={2}
                              color="label"
                              style={styles.selectedText}
                            >
                              {bank?.name[0] || ''}
                            </PayforceText.Body2>
                          </View>
                          )}
                    </View>
                    )
                  : (
                    <ProgressBar
                      percentage={Number(bank.transferSucceessRate)}
                      color={getNetworkColor(bank?.transferSucceessRate)}
                      textColor={getNetworkColor(bank?.transferSucceessRate)}
                    />
                    )}

                <ListSeparator width={8} />
                <View>
                  <PayforceText.Body1 color={colors.palette.FMBlack[800]}>
                    {bank?.name}
                  </PayforceText.Body1>
                  {bank?.transferSucceessRate != null && (
                    <PayforceText.Body1
                      style={styles.subtext}
                      color={getNetworkColor(bank?.transferSucceessRate)}
                    >
                      {getNetworkStability(bank?.transferSucceessRate)}
                    </PayforceText.Body1>
                  )}
                </View>
              </TouchableOpacity>
            )}
          />
          <ListSeparator height={20} />
        </View>
      )}

      <FlatList
        ListHeaderComponent={(
          <View>
            <PayforceText.Body2 color="label">All Banks</PayforceText.Body2>
            <ListSeparator height={8} />
          </View>
        )}
        bounces={false}
        keyExtractor={(item: TLoansBank, index) => item?.id?.toString() + index.toString()}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={() => (
          <PayforceText.Body1 color="label">Getting banks</PayforceText.Body1>
        )}
        renderItem={renderItem}
        data={data}
      />
    </View>
  );
};

export default AllBanksList;

const styles = StyleSheet.create({
  baneficiarybox: {
    height: normalize(300),
  },
  container: {
    alignItems: 'center',
    flexDirection: 'row',
    paddingVertical: normalize(8),
  },
  logoBox: {
    alignContent: 'center',
    backgroundColor: colors.palette.FMBlack[900],
    borderRadius: 50,
    height: 40,
    justifyContent: 'center',
    width: 40,
  },
  selectedText: {
    color: colors.white,
    textAlign: 'center',
  },
  subtext: {
    fontSize: normalize(10),
  },
  subtitle: {
    fontSize: normalize(14),
    marginVertical: normalize(5),
  },
  suggestLogo: {
    borderRadius: 32 / 2,
    height: 32,
    marginBottom: normalize(10),
    width: 32,
  },
});
