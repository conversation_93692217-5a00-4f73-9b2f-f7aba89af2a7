import { BottomSheetModalMethods } from '@gorhom/bottom-sheet/lib/typescript/types';
import React from 'react';
import { StyleSheet, View } from 'react-native';
import { TouchableOpacity } from 'react-native-gesture-handler';

import { CancelModal } from '@/asset/index';
import { colors } from '@/common/styles/colors';
import {
  heightPixel,
  pixelSizeHorizontal,
  pixelSizeVertical,
  widthPixel,
} from '@/common/utilities/normalize';
import CustomBottomSheet from '@/components/BottomSheet/BottomSheet';
import { PayforceButton } from '@/components/PayforceButton';
import { PayforceText } from '@/components/PayforceText/PayforceText';
import { FlexedView } from '@/components/PayForceView';

import { CreditRequirement } from './data';

export type ICreditEvaluationProps = {
  onClose: () => void;
  onOpen: () => void;
  onGrantAccess?: () => void;
  onEntailmentCheck?: () => void;
};

const CreditEvaluationBottomSheet = React.forwardRef(
  (
    { onClose, onOpen }: ICreditEvaluationProps,
    ref: React.Ref<BottomSheetModalMethods>,
  ) => {
    const handleDismiss = () => {
      onClose();
      onOpen();
    };
    return (
      <CustomBottomSheet
        ref={ref}
        closeModal={onClose}
        snapBegin="75%"
        snapEnd="75%"
        snapMid="75%"
      >
        <TouchableOpacity
          onPress={() => onClose()}
          style={styles.CancelModalBtn}
        >
          <CancelModal />
        </TouchableOpacity>
        <FlexedView style={styles.horizontalContainer}>
          <View>
            <View style={styles.modalWrapper}>
              <PayforceText.KycHeader color={colors.palette.FMBlack[800]}>
                Credit Bureau Check
              </PayforceText.KycHeader>
              <PayforceText.Body2
                style={styles.SubText}
                color={colors.palette.FMBlack[500]}
              >
                Your loan request is evaluated using these checks and
                understanding our loan terms to ensure secure approval.
              </PayforceText.Body2>
              <View>
                <View style={styles.containerWrapper}>
                  {CreditRequirement.map((_, index) => (
                    <View key={index} style={styles.listItem}>
                      {_.icon}
                      <View style={styles.contentColumn}>
                        <PayforceText.IconLabel
                          color={colors.palette.FMBlack[800]}
                        >
                          {_.title}
                        </PayforceText.IconLabel>
                        <PayforceText.Decorative
                          color={colors.palette.FMBlack[600]}
                        >
                          {_.description}
                        </PayforceText.Decorative>
                      </View>
                    </View>
                  ))}
                </View>
                <View style={styles.rangeFluidContainer}>
                  <PayforceText.IconLabel
                    color={colors.white}
                    style={styles.secondChild}
                  >
                    Loan amounts ranges between ₦1M to ₦500M
                  </PayforceText.IconLabel>
                </View>
              </View>
            </View>
          </View>
          <View style={styles.infoContainer}>
            <PayforceText.IconLabel color={colors.palette.FMBlack[500]}>
              Repayment periods range from 3 months to 18 months, with monthly
              interest rates between 2.5% and 30% (APRs from 30% to 260%).
            </PayforceText.IconLabel>
          </View>
          <PayforceButton type="big" onPress={() => handleDismiss()}>
            <PayforceText.Body1Medium color={colors.white}>
              Got it
            </PayforceText.Body1Medium>
          </PayforceButton>
        </FlexedView>
      </CustomBottomSheet>
    );
  },
);

export default CreditEvaluationBottomSheet;

const styles = StyleSheet.create({
  CancelModalBtn: {
    alignItems: 'center',
    alignSelf: 'flex-end',
    backgroundColor: colors.palette.FMBlack[100],
    borderRadius: 50,
    height: heightPixel(45),
    justifyContent: 'center',
    marginRight: pixelSizeHorizontal(20),
    width: widthPixel(45),
  },
  GrantBtn: {
    borderRadius: 4,
    marginTop: pixelSizeVertical(16),
    width: widthPixel(206),
  },
  SubText: {
    paddingVertical: pixelSizeVertical(2),
  },
  containerWrapper: {
    backgroundColor: colors.palette.FMGreen[50],
    borderRadius: 8,
    marginTop: pixelSizeVertical(20),
    transform: [
      {
        translateY: pixelSizeVertical(18),
      },
    ],
    zIndex: 1,
  },
  contentColumn: { marginHorizontal: pixelSizeHorizontal(10) },
  horizontalContainer: {
    paddingHorizontal: pixelSizeHorizontal(15),
    // justifyContent: "center",
    // alignItems: "center",
  },
  infoContainer: {
    bottom: pixelSizeVertical(24),
    marginBottom: pixelSizeVertical(20),
  },
  involvementContainer: {
    backgroundColor: colors.palette.FMGreen[50],
    borderColor: colors.palette.FMGreen[200],
    borderRadius: 4,
    borderStyle: 'dashed',
    borderWidth: 1,
    flexDirection: 'row',
    marginHorizontal: pixelSizeHorizontal(20),
    marginVertical: pixelSizeVertical(50),
    paddingHorizontal: pixelSizeHorizontal(10),
    paddingVertical: pixelSizeVertical(8),
  },
  listItem: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    padding: pixelSizeVertical(12),
  },
  marginHorizontal: {
    marginHorizontal: pixelSizeHorizontal(5),
  },
  modalWrapper: { marginVertical: pixelSizeVertical(18) },
  rangeFluidContainer: {
    alignItems: 'center',
    backgroundColor: colors.palette.FMGreen[500],
    borderRadius: 8,
    justifyContent: 'flex-end',
    marginBottom: pixelSizeVertical(20),
    paddingVertical: pixelSizeVertical(20),
  },
  secondChild: { top: pixelSizeVertical(10) },
});
