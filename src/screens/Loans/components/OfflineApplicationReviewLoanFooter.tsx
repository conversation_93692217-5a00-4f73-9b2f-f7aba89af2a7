import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';

import { Assets } from '@/asset/index';
import { colors } from '@/common/styles/colors';
import { fontPixel, heightPixel, pixelSizeHorizontal, pixelSizeVertical, widthPixel } from '@/common/utilities/normalize';
import { PayforceText } from '@/components/PayforceText/PayforceText';

function OfflineApplicationReviewLoanFooter({ onPressSeeVisitingAgent }: { onPressSeeVisitingAgent: () => void }) {
  return (
    <View>
      <View style={styles.nextWrapper}>
        <PayforceText.Body2 color={colors.black} style={styles.nextHeading}>
          What’s Next?
        </PayforceText.Body2>
        <PayforceText.Body2 color={colors.palette.FMBlack[500]} style={styles.nextDesc}>
          You will be visited by our agent, but it is a precautionary step to know who the agent is and be able to identify them.
        </PayforceText.Body2>
      </View>
      <TouchableOpacity onPress={onPressSeeVisitingAgent} style={styles.progressBanner}>
        <View style={styles.progressContent}>
          <View style={styles.progressBannerIcon}>
            <Assets.LoansIcons.User />
          </View>
          <View>
            <PayforceText.Body2 color={colors.palette.FMBlack[800]} style={styles.progressContentHeading}>
              See Visiting Agent
            </PayforceText.Body2>
            <PayforceText.Body2 color={colors.palette.FMBlack[500]} style={styles.progressContentDesc}>
              Tap to see who your assigned agent is
            </PayforceText.Body2>
          </View>
        </View>
        <TouchableOpacity onPress={() => {}}>
          <Assets.LoansIcons.CaretRight />
        </TouchableOpacity>
      </TouchableOpacity>
    </View>
  );
}

export default OfflineApplicationReviewLoanFooter;

const styles = StyleSheet.create({
  nextDesc: {
    fontSize: fontPixel(12),
    lineHeight: 18,
    marginTop: pixelSizeVertical(4),
  },
  nextHeading: {
    fontSize: fontPixel(14),
    fontWeight: '500',
    lineHeight: 20,
  },
  nextWrapper: {
    marginBottom: pixelSizeVertical(16),
    marginTop: pixelSizeVertical(24),
  },
  progressBanner: {
    alignItems: 'center',
    backgroundColor: colors.palette.FMGreen[50],
    borderRadius: 4,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: pixelSizeHorizontal(8),
    paddingVertical: pixelSizeVertical(8),
  },
  progressBannerIcon: {
    alignContent: 'center',
    backgroundColor: colors.palette.FMGreen[100],
    borderRadius: 4,
    height: heightPixel(32),
    justifyContent: 'center',
    paddingHorizontal: pixelSizeHorizontal(4),
    paddingVertical: pixelSizeVertical(4),
    width: widthPixel(32),
  },
  progressContent: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: 8,
  },
  progressContentDesc: {
    fontSize: fontPixel(12),
    lineHeight: 18,
  },
  progressContentHeading: {
    fontSize: fontPixel(14),
    fontWeight: '500',
    lineHeight: 20,
  },
});
