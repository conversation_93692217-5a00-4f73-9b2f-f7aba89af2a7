import { BottomSheetModalMethods } from '@gorhom/bottom-sheet/lib/typescript/types';
import React, { useEffect, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import DocumentPicker, {
  DocumentPickerResponse,
} from 'react-native-document-picker';
import { TouchableOpacity } from 'react-native-gesture-handler';
import Toast from 'react-native-toast-message';

import { Assets, CancelModal } from '@/asset/index';
import { colors } from '@/common/styles/colors';
import { heightPixel, pixelSizeHorizontal, pixelSizeVertical } from '@/common/utilities/normalize';
import CustomBottomSheet from '@/components/BottomSheet/BottomSheet';
import { PayforceButton } from '@/components/PayforceButton';
import { PayforceText } from '@/components/PayforceText/PayforceText';

interface Props {
  onClose?: () => void;
  onUpload?: (file: DocumentPickerResponse) => void;
  uploading?: boolean;
  file?: DocumentPickerResponse | null;
  setFile?: (file: DocumentPickerResponse | null) => void;
  definition?: { [key: string]: string | number };
}

function LoanBankUploadBottomSheet({ onClose, onUpload, uploading, setFile, file, definition }: Props, ref: React.Ref<BottomSheetModalMethods>) {
  const [docTypes, setDocTypes] = useState<string[]>([]);

  useEffect(() => {
    if (definition?.fileTypesAllowed) {
      const docTypesArr = (definition?.fileTypesAllowed as string)?.split(',').map((type) => type.replace('.', ''));
      const filteredDocTypes = docTypesArr.filter((type) => type !== 'jpg' && type !== 'jpeg' && type !== 'png');
      const uploadType = filteredDocTypes.map((type) => DocumentPicker.types[type as keyof typeof DocumentPicker.types]);
      setDocTypes(uploadType);
    }
  }, [definition]);

  const handleSelectDocument = () => {
    ref?.current?.dismiss();
    DocumentPicker.pick({
      type: [...docTypes, DocumentPicker.types.images],
    }).then((res: DocumentPickerResponse[]) => {
      setFile?.(res[0]);
      ref?.current?.present();
    }).catch(() => {
      Toast.show({
        type: 'info',
        text1: 'Please select a valid file',
      });
    });
  };

  return (
    <CustomBottomSheet ref={ref} snapBegin="60%" snapMid="60%" snapEnd="60%" enablePanDownToClose onClose={onClose}>
      <View style={styles.container}>
        <View>
          <TouchableOpacity
            onPress={() => onClose?.()}
            style={styles.closeIcon}
          >
            <CancelModal />
          </TouchableOpacity>

          <View style={styles.headingWrapper}>
            <PayforceText.Body2
              color={colors.black}
              style={styles.heading1}
            >
              Upload Bank Statement
            </PayforceText.Body2>
            <PayforceText.Body2
              color={colors.palette.FMBlack[600]}
              style={styles.uploadDesc}
            >
              Please upload a bank statement from your primary account dated to no less than
              {' '}
              <PayforceText.Body2 color={colors.palette.FMBlack[800]} style={styles.uploadDescPeriod}>a year.</PayforceText.Body2>
            </PayforceText.Body2>
          </View>

          <TouchableOpacity
            style={styles.fileWrapper}
            onPress={handleSelectDocument}
            disabled={uploading}
          >
            <Assets.LoansIcons.FileUpload />

            {file
              ? (
                <PayforceText.Body2
                  color={colors.palette.FMBlack[500]}
                  style={styles.fileDesc}
                >
                  {file.name}
                </PayforceText.Body2>
                )
              : (
                <>

                  <PayforceText.Body2
                    color={colors.palette.FMBlack[500]}
                    style={styles.fileDesc}
                  >
                    <PayforceText.Body2 color={colors.palette.FMGreen[600]}>Click to upload</PayforceText.Body2>
                    {' '}
                    or drag and drop
                  </PayforceText.Body2>
                  <PayforceText.Body2
                    color={colors.palette.FMBlack[400]}
                    style={styles.fileSize}
                  >
                    {definition?.fileTypesAllowed as string}
                    {' '}
                    {`(max, ${definition?.maxFileSizeInMb} MB)`}
                  </PayforceText.Body2>
                </>
                ) }

          </TouchableOpacity>

        </View>

        <PayforceButton
          type="big"
          disabled={!file || uploading}
          onPress={() => {
            onUpload?.(file!);
          }}
        >
          <PayforceText.Body1Medium color={colors.white}>
            Continue
          </PayforceText.Body1Medium>
        </PayforceButton>
      </View>

    </CustomBottomSheet>
  );
}

export default React.forwardRef(LoanBankUploadBottomSheet);

const styles = StyleSheet.create({
  closeIcon: {
    alignItems: 'flex-end',
  },
  container: {
    flex: 1,
    justifyContent: 'space-between',
    paddingHorizontal: pixelSizeHorizontal(20),
  },
  fileDesc: {
    fontSize: 14,
    fontWeight: '500',
    marginTop: pixelSizeVertical(8),
  },
  fileSize: {
    fontSize: 12,
    fontWeight: '400',
    marginTop: pixelSizeVertical(4),
  },
  fileWrapper: {
    alignItems: 'center',
    borderColor: colors.palette.FMGray[200],
    borderRadius: 4,
    borderWidth: 1,
    height: heightPixel(126),
    justifyContent: 'center',
  },
  heading1: {
    fontSize: 24,
    fontWeight: '600',

  },
  headingWrapper: {
    marginBottom: pixelSizeVertical(24),
    marginTop: pixelSizeVertical(14),
  },
  uploadDesc: {
    fontSize: 14,
    fontWeight: '400',
    marginVertical: pixelSizeVertical(8),

  },
  uploadDescPeriod: {
    fontWeight: '600',
  },
});
