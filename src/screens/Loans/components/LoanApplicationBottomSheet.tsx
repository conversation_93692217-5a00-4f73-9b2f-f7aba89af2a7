import { BottomSheetModalMethods } from '@gorhom/bottom-sheet/lib/typescript/types';
import React, { Fragment } from 'react';
import { StyleSheet, View } from 'react-native';

import FMBLoanIcon from '@/assets/svg/fair_money_loan.svg';
import { colors } from '@/common/styles/colors';
import {
  pixelSizeHorizontal,
  pixelSizeVertical,
  widthPixel,
} from '@/common/utilities/normalize';
import CustomBottomSheet from '@/components/BottomSheet/BottomSheet';
import { PayforceButton } from '@/components/PayforceButton';
import { PayforceText } from '@/components/PayforceText/PayforceText';
import { FlexedView } from '@/components/PayForceView';

type LoanApplicationBottomSheetProps = {
  onClose: () => void;
  onHandleRequest: () => void;
};

const steps = [
  {
    title: 'Request loan',
    subTitle: 'Fill in your application',
  },
  {
    title: 'Provide Bank Statement',
    subTitle: 'Submit your most recent bank statement',
  },
  {
    title: 'Collateral and Guarantor Check',
    subTitle: 'Ensure all necessary data are provided',
  },
  {
    title: 'Loan application successful!',
    subTitle: 'A loan officer will be immaediately assigned to you',
  },
];

const LoanApplicationBottomSheet = React.forwardRef(
  (
    { onClose, onHandleRequest }: LoanApplicationBottomSheetProps,
    ref: React.Ref<BottomSheetModalMethods>,
  ) => (

    <CustomBottomSheet
      ref={ref}
      closeModal={onClose}
      snapBegin="60%"
      snapEnd="60%"
      snapMid="60%"
    >
      <FlexedView style={styles.horizontalContainer}>
        <View style={styles.loanListTop}>
          <View style={styles.loanContentWrapper}>
            <PayforceText.Body3Medium
              color={colors.palette.FMBlack[800]}
              style={styles.loanListTopTitle}
            >
              Get Loan in 4 Easy Steps
            </PayforceText.Body3Medium>
            <PayforceText.Body2
              color={colors.palette.FMBlack[500]}
              style={styles.loanListBottomSheetSubheader}
            >
              Get the best business loan offers in 4 easy
              {' '}
              {'\n'}
              steps!
            </PayforceText.Body2>
          </View>
          <FMBLoanIcon />
        </View>
        <View style={styles.loanListMidContainer}>
          <View style={styles.stepNumberLineContainer}>
            {steps.map((_, stepIdx) => (
              <Fragment key={stepIdx}>
                <View style={styles.stepNumberContainer}>
                  <PayforceText.TinyText color={colors.white}>
                    {stepIdx + 1}
                  </PayforceText.TinyText>
                </View>
                {stepIdx !== 3 && <View style={styles.stepLine} />}
              </Fragment>
            ))}
          </View>

          <View>
            {steps.map((step) => (
              <View
                key={step.title}
                style={styles.loanListWrapperTextContainer}
              >
                <PayforceText.Body2 color={colors.palette.FMBlack[800]}>
                  {step.title}
                </PayforceText.Body2>
                <PayforceText.Decorative color={colors.palette.FMBlack[500]}>
                  {step.subTitle}
                </PayforceText.Decorative>
              </View>
            ))}
          </View>
        </View>
        <PayforceButton
          type="big"
          style={styles.loanListButton}
          onPress={() => {
            onHandleRequest();
          }}
        >
          <PayforceText.Body1Medium color={colors.white}>
            Request Loan
          </PayforceText.Body1Medium>
        </PayforceButton>
      </FlexedView>
    </CustomBottomSheet>
  ),
);

export default LoanApplicationBottomSheet;

const styles = StyleSheet.create({
  horizontalContainer: {
    paddingHorizontal: pixelSizeHorizontal(15),
  },
  loanContentWrapper: {
    alignItems: 'flex-start',
  },
  loanListBottomSheetSubheader: {
    marginTop: pixelSizeVertical(5),
    textAlign: 'left',
  },
  loanListButton: {
    flexDirection: 'row',
    marginTop: pixelSizeVertical(16),
  },
  loanListMidContainer: {
    borderRadius: 8,
    flexDirection: 'row',
    gap: pixelSizeHorizontal(12),
    marginTop: pixelSizeVertical(16),
    paddingHorizontal: pixelSizeHorizontal(26),
    paddingVertical: pixelSizeVertical(24),
  },
  loanListMidWrapper: {
    backgroundColor: colors.palette.FMBlack[50],
    borderRadius: 5,
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: pixelSizeVertical(15),
    paddingHorizontal: pixelSizeHorizontal(10),
    paddingVertical: pixelSizeVertical(20),
  },
  loanListTop: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 'auto',
    marginTop: pixelSizeVertical(15),
    maxWidth: widthPixel(358),
  },
  loanListTopTitle: {
    marginTop: pixelSizeVertical(10),
    textAlign: 'center',
  },
  loanListWrapperTextContainer: {
    flex: 1,
    gap: pixelSizeVertical(4),
    minHeight: 67,
  },
  stepLine: {
    borderColor: colors.palette.FMGreen[500],
    borderLeftWidth: 2,
    height: 49,
  },
  stepNumberContainer: {
    alignItems: 'center',
    backgroundColor: colors.palette.FMGreen[500],
    borderRadius: 10,
    height: 20,
    justifyContent: 'center',
    width: 20,
  },
  stepNumberLineContainer: {
    alignItems: 'center',
  },
});
