import { useState } from 'react';

import { useNavigation, useRoute } from '@/routes/auth/(stacks)';
import { useCreateBottomSheetModal } from '@/utils/helper';

import { useMLoanMandateFundsTransferred } from './mutations/loanMandate.mutation';
import { useQGetMandateStatus } from './queries/loanMandate.query';

const useVerifyLoanLinkedBank = () => {
  const [isGettingStatus, setIsGettingStatus] = useState(false);
  const [isFundsTransferred, setIsFundsTransferred] = useState(false);
  const navigation = useNavigation();
  const { params } = useRoute<'VerifyLoanLinkedBank'>();
  const [helpInfoRef, openHelpInfoSheet, closeHelpInfoSheet] = useCreateBottomSheetModal();
  const [successInfoRef, openSuccessInfoSheet, closeSuccessInfoSheet] = useCreateBottomSheetModal();
  const [failureInfoRef, openFailureInfoSheet, closeFailureInfoSheet] = useCreateBottomSheetModal();
  const { mutate, isLoading: isLoadingFundsTransferred } = useMLoanMandateFundsTransferred();
  const bankDetails = params?.bankDetails;

  const successCallBack = () => {
    setIsFundsTransferred(false);
    setIsGettingStatus(false);
    openSuccessInfoSheet();
  };

  const failureCallBack = () => {
    setIsFundsTransferred(false);
    setIsGettingStatus(false);
    openFailureInfoSheet();
  };

  useQGetMandateStatus({
    mandateReferenceId: bankDetails.mandateReferenceId,
    enabled: isGettingStatus,
    successCallBack,
    failureCallBack,
  });

  const getLoan = () => {
    closeSuccessInfoSheet();
    if (params?.isDDRepayment && params?.repaymentData) {
      // if direct debit repayment, go back to repayment screen
      navigation.navigate('MakeLoanPaymentScreen', params.repaymentData);
    } else if (params?.isOfflineLoan) {
      // if offline loan offer flow, go to offline loan finalised screen showing loan summary
      navigation.navigate('OfflineLoanFinalised');
    } else if (params?.summaryDetails) {
      navigation.navigate('LoanSummary', { data: params.summaryDetails });
    } else {
      // if unable to determine route, go home
      navigation.reset({
        index: 0,
        routes: [{ name: 'AuthenticatedAppStack' }],
      });
    }
  };

  const postFundsTransferred = () => {
    closeFailureInfoSheet();
    mutate(bankDetails.mandateReferenceId, {
      onSuccess: (data) => {
        if (data.status !== 200) {
          setIsFundsTransferred(true);
          setTimeout(() => {
            setIsGettingStatus(true);
          }, 60_000);
        }
      },
    });
  };

  return {
    isLoadingFundsTransferred,
    isFundsTransferred,
    bankDetails,
    navigation,
    postFundsTransferred,
    helpInfoRef,
    openHelpInfoSheet,
    closeHelpInfoSheet,
    successInfoRef,
    closeSuccessInfoSheet,
    failureInfoRef,
    closeFailureInfoSheet,
    getLoan,
  };
};

export default useVerifyLoanLinkedBank;
