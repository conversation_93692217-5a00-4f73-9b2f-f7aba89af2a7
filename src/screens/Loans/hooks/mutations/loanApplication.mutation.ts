import { useMutation } from '@tanstack/react-query';

import { api } from '@/functions/api';

type TApplicationRequest = {
  loanAmount: number;
  loanTenure: string;
  loanPurpose: string;
  loanOfficerUsername: string;
  loanType: number;
};

type TApplicationCheckRequest = {
  userIp: string;
  credoLabData: string;
  applicationId: string;
};

type TFileUploadRequest = FormData;

type TGuarantorRequest = {
  loanApplicationId: number;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  email: string;
  loanApplicantUsername: string;
};

export type TScheduleVisitRequest = {
  loanApplicationId: number | string;
  alternatePhoneNumber: string;
  preferredVisitationDate: string;
  isAnyTime: boolean;
};

export type TGenerateGuarantorCodeRequest = {
  guarantorId: number;
  loanApplicationId: number;
};

export const useCreateApplication = () => {
  const mutation = useMutation({
    mutationFn: (req: TApplicationRequest) => api.post('/gu/guardian/loan-application/create', req),
  });

  return mutation;
};

export const useApplicationCreditCheck = () => {
  const mutation = useMutation({
    mutationFn: (req: TApplicationCheckRequest) => api.post('/gu/guardian/loan-application/credit-check', req),
  });

  return mutation;
};

export const useUploadDocument = () => {
  const mutation = useMutation({
    mutationFn: (formData: TFileUploadRequest) =>
      api.post('/gu/guardian/document/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }),
  });

  return mutation;
};

export const useCreateGuarantor = () => {
  const mutation = useMutation({
    mutationFn: (req: TGuarantorRequest) =>
      api.post('/gu/guardian/guarantors/add', req),
  });

  return mutation;
};

export const useScheduleVisit = () => {
  const mutation = useMutation({
    mutationFn: (req: TScheduleVisitRequest) =>
      api.post('/gu/guardian/loan-application/schedule-visit', req),
  });

  return mutation;
};

export const useGenerateGuarantorCode = () => {
  const mutation = useMutation({
    mutationFn: (req: TGenerateGuarantorCodeRequest) => api.post('/gu/guardian/guarantors/get-access-token', req),
  });

  return mutation;
};
