/**
 * Custom hook to fetch lookup data for loans using `react-query`.
 *
 * @param {LookupQueryType} params - The parameters for the lookup query.
 * @param {queryType} params.queryType - The type of lookup query to perform.
 *                                        It can be one of the following:
 *                                        - 'MerchantLendingLoanTenure'
 *                                        - 'MerchantLendingLoanPurpose'
 *                                        - 'MerchantLendingLoanAmount'
 *
 * @returns {UseQueryResult} The result of the query, including data, status, and other properties provided by `react-query`.
 *
 * @remarks
 * - The query is enabled only if `queryType` is provided.
 * - The API endpoint used is `/l/loan/lookup/{queryType}`.
 *
 * @example
 * ```typescript
 * const { data, isLoading, error } = useLookupQuery({ queryType: 'MerchantLendingLoanPurpose' });
 * if (isLoading) return <div>Loading...</div>;
 * if (error) return <div>Error occurred</div>;
 * return <div>{JSON.stringify(data)}</div>;
 * ```
 */
import { useQuery } from '@tanstack/react-query';
import { fetchAccountInfo } from 'src/screens/Transactions/lib/useTransactionsActions';

import { api } from '@/functions/api';

import { TLoanVisitingAgentResponse } from '../../types';

export type queryType =
  | 'MerchantLendingLoanTenure'
  | 'MerchantLendingLoanPurpose'
  | 'MerchantLendingLoanAmount';

type LookupQueryType = {
  queryType?: queryType;
};

export const useLookupQuery = ({ queryType }: LookupQueryType) => {
  const query = useQuery(
    ['lookup_query', queryType],
    () => api.get(`/gu/guardian/lookup?specificCategory=${queryType}`),
    {
      enabled: !!queryType,
    },
  );

  return query;
};

export const useAccountInfo = (accountId: string, bankCode: string) => {
  const query = useQuery(
    ['user-bank-acount-info', accountId, bankCode],
    () => fetchAccountInfo(accountId, bankCode, 'ZCDT'),
    { enabled: !!accountId && !!bankCode },
  );

  return query;
};

export const useGetStatementAvailability = (bankcode: string, bankName: string) => {
  const query = useQuery(
    ['get-statement-availability', bankcode, bankName],
    () => api.get(`/gu/guardian/statement/availability?BankCode=${bankcode}&BankName=${bankName}`),
    {
      enabled: !!bankcode && !!bankName,
    },
  );

  return query;
};

export const useGetDocumentDefinitionList = (id: string) => {
  const query = useQuery(
    ['get-document-definition-list', id],
    () => api.get(`/gu/document-definition/list?LoanApplicationId=${id}`),
  );

  return query;
};

export const useGetDocumentVersionList = (appid: number, docId: number) => {
  const query = useQuery(
    ['get-document-version-list', appid, docId],
    () => api.get(`/gu/guardian/document/versions?loanApplicationId=${appid}&documentDefinitionId=${docId}`),
    { enabled: !!appid && !!docId },
  );

  return query;
};

export const useGetLoanVisitingAgent = (loanApplicationId?: string) => {
  const query = useQuery(
    ['get-loan-visiting-agent', loanApplicationId],
    () => api.get<TLoanVisitingAgentResponse>(`/gu/guardian/loan-officers/view?LoanApplicationId=${loanApplicationId}`),
    { enabled: Boolean(loanApplicationId) },
  );

  return query;
};

export const useGetApplicationState = (applicationId: string) => {
  const query = useQuery(
    ['lending-application-state', applicationId],
    () => api.get(`/gu/guardian/loan-application/merchant-lending-application-state?loanApplicationId=${applicationId}`),
    { enabled: !!applicationId },
  );

  return query;
};

export const useGetGuarantorList = (applicationId: string) => {
  const query = useQuery(
    ['guarantor-list', applicationId],
    () => api.get(`/gu/guardian/guarantors/list?loanApplicationId=${applicationId}&onlyPendingGuarantors=true`),
    {
      enabled: !!applicationId,
    },
  );

  return query;
};
