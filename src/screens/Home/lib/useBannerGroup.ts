import { CommonActions, useNavigation } from '@react-navigation/native';
import { useCallback, useMemo } from 'react';
import { Linking } from 'react-native';
import { SharedValue } from 'react-native-reanimated';

import { SwipperBanner } from '@/components/PayforceBanner/types';
import { isValidURL } from '@/functions/functions';
import { AuthenticatedStackNavProps } from '@/routes/auth/types';
import { log } from '@/utils/log';

import { routeMapping } from '../components/QuickIcons';
import { QuickLinkList } from '../components/QuickLinks';
import { parseInitialBannerFormat } from './functions';
import { useGeBannerItems } from './useBannerItems';

type Options = {
  activeIndex: SharedValue<number>;
};

export const useBannerGroup = ({ activeIndex }: Options) => {
  const { data } = useGeBannerItems();
  const navigation = useNavigation<AuthenticatedStackNavProps>();

  const gotoRoute = (link: string) => {
    if (link === 'fair_lock') {
      navigation.dispatch(
        CommonActions.navigate({
          name: 'SavingsBaseStack',
          params: {
            screen: 'FairLockBaseStack',
          },
        }),
      );
      return;
    }
    const routeInfo = QuickLinkList.find(
      ({ key: routeKey }) => routeKey === link,
    );

    if (routeInfo && Object.keys(routeInfo).length > 0) {
      navigation.dispatch(
        CommonActions.navigate({
          name: routeMapping[routeInfo.key],
          params: { screen: routeInfo.route },
        }),
      );
    } else {
      log({ name: `:::[${link} is not a valid app route key]:::` });
    }
  };

  const banners = useMemo<SwipperBanner[]>(() => parseInitialBannerFormat(data), [data]);

  const setActiveIndex = useCallback(
    (index: number) => {
      // eslint-disable-next-line no-param-reassign
      activeIndex.value = index;
    },
    [activeIndex],
  );

  const handleViewPage = (link: string) => {
    const prefixedURL = link.startsWith('http') ? link : `http://${link}`;

    if (isValidURL(prefixedURL)) {
      Linking.openURL(prefixedURL);
    } else {
      gotoRoute(link);
    }
  };

  return {
    banners,
    setActiveIndex,
    handleViewPage,
  };
};
