import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { FeatureFlagKeys } from 'src/lib/featureFlag';

import { FeatureFlag, FeatureFlagsState } from './type';

const initialState: FeatureFlagsState = {
  flags: [
    // add mock feature flags here
    {
      flagKey: FeatureFlagKeys.LOAN_APPLICATION,
      parsed: {
        showBeta: true,
        labels: ['beta'],
      },
      provider: 'local',
      fetchedAt: new Date(),
    },
  ],
};

const featureFlagsSlice = createSlice({
  name: 'featureFlags',
  initialState,
  reducers: {
    setFeatureFlags: (state, action: PayloadAction<FeatureFlag[]>) => ({
      ...state,
      flags: action.payload,
    }),
    updateSingleFeatureFlag: (state, action: PayloadAction<Partial<FeatureFlag>>) => ({
      ...state,
      flags: state.flags.map((flag) =>
        flag.flagKey === action.payload.flagKey ? { ...flag, ...action.payload } : flag),
    }),
    resetFeatureFlags: () => initialState,
  },
});

export const {
  setFeatureFlags,
  resetFeatureFlags,
  updateSingleFeatureFlag,
} = featureFlagsSlice.actions;

export default featureFlagsSlice.reducer;
