import * as React from 'react';
import Svg, { G, Path, SvgProps } from 'react-native-svg';

interface FemaleGenderIconProps extends SvgProps {
  strokeColor: string;
}

export const FemaleGenderIcon = ({
  strokeColor = '#FFF',
  ...props
}: FemaleGenderIconProps) => (
  <Svg width={16} height={17} fill="none" {...props}>
    <G stroke={strokeColor} strokeLinejoin="round" strokeWidth={1.75}>
      <Path d="M12.793 4.167a4.667 4.667 0 1 0-6.6 6.601 4.667 4.667 0 0 0 6.6-6.6Z" />
      <Path
        strokeLinecap="round"
        d="M6.155 10.729 1.912 14.97m4.478-.236-4.242-4.242"
      />
    </G>
  </Svg>
);
