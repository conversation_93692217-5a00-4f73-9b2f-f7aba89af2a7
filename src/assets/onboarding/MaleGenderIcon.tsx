import * as React from 'react';
import Svg, { G, Path, SvgProps } from 'react-native-svg';

interface MaleGenderIconProps extends SvgProps {
  strokeColor: string;
}

export const MaleGenderIcon = ({
  strokeColor = '#9CA3AF',
  ...props
}: MaleGenderIconProps) => (
  <Svg width={16} height={17} fill="none" {...props}>
    <G stroke={strokeColor} strokeLinejoin="round" strokeWidth={1.75}>
      <Path strokeLinecap="round" d="M13.984 5.9v-3h-3" />
      <Path d="M3.471 13.55a4.666 4.666 0 1 0 6.6-6.599 4.666 4.666 0 0 0-6.6 6.6Z" />
      <Path strokeLinecap="round" d="m10 6.883 3.317-3.317" />
    </G>
  </Svg>
);
