import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

interface TickIconProps extends SvgProps {
  strokeColor?: string;
}

export const TickOnboardIcon = ({
  strokeColor = '#111',
  ...props
}: TickIconProps) => (
  <Svg width={10} height={8} fill="currentColor" {...props}>
    <Path
      fill={strokeColor}
      fillRule="evenodd"
      stroke={strokeColor}
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={0.667}
      d="M9.253.724c.153.14.163.377.023.53l-5.5 6a.375.375 0 0 1-.541.011l-2.5-2.5a.375.375 0 1 1 .53-.53l2.223 2.223L8.724.747a.375.375 0 0 1 .53-.023Z"
      clipRule="evenodd"
    />
  </Svg>
);
