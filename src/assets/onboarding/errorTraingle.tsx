import * as React from 'react';
import { ColorValue } from 'react-native';
import Svg, { Path } from 'react-native-svg';

import { colors } from '@/common/styles/colors';

interface ErrorTriangleProps {
  size: number;
  color: string | ColorValue;
}

export const ErrorTriangleIcon = ({
  size = 0,
  color = colors.palette.warning[300],
}: ErrorTriangleProps): React.ReactElement => (
  <Svg width={size} height={(size / 18) * 15} fill="none">
    <Path
      fillRule="evenodd"
      fill={color}
      d="M20.5 26.666a1.666 1.666 0 1 0 0 3.333 1.666 1.666 0 0 0 0-3.332Zm17.784 2.45L24.867 5.784a5 5 0 0 0-8.733 0L2.801 29.116a5 5 0 0 0 4.266 7.55h26.867a5 5 0 0 0 4.35-7.55Zm-2.883 3.334a1.667 1.667 0 0 1-1.467.85H7.067a1.666 1.666 0 0 1-1.466-.85 1.667 1.667 0 0 1 0-1.667L18.934 7.45a1.666 1.666 0 0 1 2.967 0l13.416 23.333a1.667 1.667 0 0 1 .084 1.7v-.033ZM20.5 13.333A1.666 1.666 0 0 0 18.834 15v6.666a1.667 1.667 0 0 0 3.333 0V15a1.667 1.667 0 0 0-1.666-1.667Z"
    />
  </Svg>
);
