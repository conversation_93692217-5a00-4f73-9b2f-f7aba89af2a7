<svg width="390" height="844" viewBox="0 0 390 844" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_256_24073)">
<rect width="390" height="844" rx="24" fill="white"/>
<rect opacity="0.5" x="-63" y="607" width="440" height="663" rx="89" stroke="url(#paint0_linear_256_24073)" stroke-width="2"/>
<rect opacity="0.5" x="285" y="417" width="440" height="663" rx="89" stroke="url(#paint1_linear_256_24073)" stroke-width="2"/>
<g filter="url(#filter0_b_256_24073)">
<rect width="390" height="34" transform="translate(0 810)" fill="url(#paint2_linear_256_24073)" fill-opacity="0.01"/>

</g>
</g>
<defs>
<filter id="filter0_b_256_24073" x="-15" y="795" width="420" height="64" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="7.5"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_256_24073"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_256_24073" result="shape"/>
</filter>
<linearGradient id="paint0_linear_256_24073" x1="-56.2825" y1="850.815" x2="347.072" y2="1223.36" gradientUnits="userSpaceOnUse">
<stop stop-color="#2953A7"/>
<stop offset="1" stop-color="#EFECFC"/>
</linearGradient>
<linearGradient id="paint1_linear_256_24073" x1="291.717" y1="660.815" x2="695.072" y2="1033.36" gradientUnits="userSpaceOnUse">
<stop stop-color="#2953A7"/>
<stop offset="1" stop-color="#EFECFC"/>
</linearGradient>
<linearGradient id="paint2_linear_256_24073" x1="225.68" y1="34" x2="225.68" y2="0" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0_256_24073">
<rect width="390" height="844" rx="24" fill="white"/>
</clipPath>
</defs>
</svg>
