import { StyleSheet } from 'react-native';

import { colors } from '@/common/styles/colors';
import {
  heightPixel,
  pixelSizeHorizontal,
  widthPixel,
} from '@/common/utilities/normalize';

const Styles = StyleSheet.create({
  body: {
    marginTop: 4,
    width: 288,
  },
  buttonTag: {
    alignItems: 'center',
    borderRadius: 2,
    height: heightPixel(16),
    justifyContent: 'center',
    minWidth: widthPixel(36),
    paddingHorizontal: pixelSizeHorizontal(3),
  },
  container: {
    backgroundColor: colors.palette.FMBlack[50],
    borderColor: colors.palette.FMBlack[300],
    borderRadius: 4,
    borderWidth: 1,
    flexDirection: 'column',
    marginBottom: 8,
    minHeight: 76,
  },
  filler: {
    height: heightPixel(16),
  },
  headerSection: {
    justifyContent: 'space-between',
  },
  optionsInfo: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: pixelSizeHorizontal(12),
  },
  tagPending: {
    backgroundColor: colors.palette.warning[500],
  },
  tagSection: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  tagSuccess: {
    backgroundColor: colors.palette.FMGreen[500],
  },
  textContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 12,
    paddingTop: 0,
  },
});

export default Styles;
