import React from 'react';
import { View } from 'react-native';

import CBNIcon from '@/assets/svg/pf_cbn_icon.svg';
import { colors } from '@/common/styles/colors';
import { PayforceText } from '@/components/PayforceText/PayforceText';

import Styles from './styles';

export const CBNBadge = () => (
  <View style={Styles.container}>
    <CBNIcon style={Styles.iconStyle} />
    <PayforceText.Body1 color={colors.palette.FMBlue[400]}>
      Licensed by CBN and insured by
      {' '}

    </PayforceText.Body1>
    <PayforceText.Body1Bold color={colors.palette.FMBlue[800]}>
      NDIC
    </PayforceText.Body1Bold>
  </View>
);
