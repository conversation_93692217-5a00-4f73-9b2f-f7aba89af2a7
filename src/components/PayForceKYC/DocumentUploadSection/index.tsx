/* eslint-disable @typescript-eslint/no-unnecessary-condition */
import React, { Fragment } from 'react';
import { ActivityIndicator, StyleProp, View, ViewStyle } from 'react-native';

import { colors } from '@/common/styles/colors';
import { ListSeparator } from '@/components/ListSeparator';
import { DocumentUploadButton } from '@/components/PayForceKYC';
import { PayforceText } from '@/components/PayforceText/PayforceText';

import { FileAttachment, UploadDocs } from '../lib/types';
import { replaceUnderscoresAndCapitalize } from '../lib/utils';
import Styles from './styles';

interface DocumentUploadSectionProps {
  isLoading?: boolean;
  sectionTitle: string;
  documents?: UploadDocs[];
  selectedDoctype?: string;
  tierDocs?: number;
  header: string;
  attachments?: FileAttachment;
  styles?: StyleProp<ViewStyle>;

  uploadFn: (doctype: string) => void;
}

export const DocumentUploadSection = ({
  attachments,
  selectedDoctype = '',
  isLoading,
  documents,
  uploadFn,
  tierDocs = 0,
  styles,
}: DocumentUploadSectionProps) => (
  <View style={styles}>
    <View style={Styles.sectionTitle}>
      <PayforceText.Body1 color={colors.palette.FMBlack[500]}>
        UPLOAD ANY OF THE AVAILABLE DOCUMENT
      </PayforceText.Body1>
    </View>

    {isLoading && (
      <>
        <ActivityIndicator color={colors.p500} />
        <ListSeparator height={16} />
      </>
    )}

    {documents?.map(({ id, name, body, text }) => (
      <Fragment key={id}>
        {(tierDocs === 2
        && name !== 'UTILITY_BILL'
        && name !== 'CAC_DOCUMENT')
        || (tierDocs === 3 && name === 'UTILITY_BILL')
        || (tierDocs === 4 && name === 'CAC_DOCUMENT')
          ? (
            <View key={id} style={Styles.itemContainer}>
              <View style={Styles.textViewWidth}>
                <PayforceText.Body2 color={colors.palette.FMBlack[800]}>
                  {name !== 'NIN' ? replaceUnderscoresAndCapitalize(name) : name}
                </PayforceText.Body2>

                {body && (
                  <>
                    <ListSeparator height={4} />
                    <PayforceText.Decorative
                      color={colors.palette.FMBlack[500]}
                    >
                      {body}
                    </PayforceText.Decorative>
                  </>
                )}
              </View>

              {attachments
              && Object.keys(attachments).length > 1
              && selectedDoctype === name
                ? (
                  <DocumentUploadButton buttonState="uploaded" />
                  )
                : (
                  <DocumentUploadButton
                    pressFn={() => uploadFn(name)}
                    text={text}
                  />
                  )}
            </View>
            )
          : null}
      </Fragment>
    ))}
  </View>
);
