import React from 'react';
import { View } from 'react-native';

import { colors } from '@/common/styles/colors';
import { PayforceButton } from '@/components/PayforceButton';
import { PayforceText } from '@/components/PayforceText/PayforceText';

import Styles from './styles';

interface DocumentUploadButtonProps {
  text?: string;
  pressFn?: () => void;
  buttonState?: 'uploaded' | 'loading';
}

export const DocumentUploadButton = ({
  buttonState, text, pressFn,
}: DocumentUploadButtonProps) => {
  if (buttonState === 'loading') {
    return (
      <View style={Styles.loadingContainer}>
        <View style={Styles.loadingView} />
        <PayforceButton disabled type="tiny" height={34} style={Styles.loadingBtn}>
          <PayforceText.IconLabel color={colors.grey}>
            Loading...
          </PayforceText.IconLabel>
        </PayforceButton>
      </View>
    );
  }

  if (buttonState === 'uploaded') {
    return (
      <View style={Styles.normalView}>
        <PayforceButton type="tiny" height={34} style={Styles.uploadedBtn}>
          <PayforceText.IconLabel color={colors.palette.FMGreen[500]}>
            Selected ✓
          </PayforceText.IconLabel>
        </PayforceButton>
      </View>
    );
  }

  return (
    <View style={Styles.normalView}>
      <PayforceButton type="tiny" onPress={pressFn} height={34} disabled={text === 'Upload file'} style={Styles.defaultBtn}>
        <PayforceText.IconLabel color={colors.palette.FMLightBlue[500]}>
          {text || 'Upload doc'}
        </PayforceText.IconLabel>
      </PayforceButton>
    </View>
  );
};
