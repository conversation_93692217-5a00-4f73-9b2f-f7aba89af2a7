import { StyleSheet } from 'react-native';

import { colors } from '@/common/styles/colors';

const Styles = StyleSheet.create({
  defaultBtn: {
    backgroundColor: colors.palette.FMLightBlue[50],
  },
  loadingBtn: {
    backgroundColor: colors.palette.FMBlack[100],
  },
  loadingContainer: {
    position: 'relative',
    width: 82,
  },
  loadingView: {
    backgroundColor: colors.palette.FMBlack[500],
    height: '100%',
    position: 'absolute',
    width: '40%',
  },
  normalView: {
    width: 82,
  },
  uploadedBtn: {
    backgroundColor: colors.palette.FMGreen[50],
  },
});

export default Styles;
