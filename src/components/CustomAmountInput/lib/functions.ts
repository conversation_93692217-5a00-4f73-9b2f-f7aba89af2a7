/**
 * Handle currency formatting
 * @param inputValue
 * @returns
 */
export const formatCurrency = (inputValue: string) => {
  const [integerPart, decimalPart] = inputValue.split('.');

  // ------- format Interger ------
  const formattedInteger = integerPart
    ? Number(integerPart).toLocaleString()
    : '';

  const formattedValue
    = decimalPart !== undefined
      ? `${formattedInteger}.${decimalPart}`
      : formattedInteger;

  return formattedValue;
};

/**
 * Allow just digit and period for amount
 * @param inputString
 * @returns
 */
export const removeNonDigits = (inputString: string): string | null => {
  // ------- return if the passed string is empty -------
  if (inputString === '') {
    return '';
  }

  // -------- remove formating from parsed input strings ------
  const removeFormatting = inputString.replace(/,/g, '');

  const regexValue = /^\d+(\.{1})?(\d{1,2})?$/;
  return regexValue.test(removeFormatting) ? removeFormatting : null;
};
