import React, { ReactElement, useMemo, useState } from 'react';
import { FieldError, FieldErrorsImpl, Merge } from 'react-hook-form';
import { StyleSheet, TextInput, View, ViewStyle } from 'react-native';

import { colors } from '@/common/styles/colors';
import { FONT_DMSANS } from '@/common/styles/fonts';
import { fontPixel } from '@/common/utilities/normalize';
import { ListSeparator } from '@/components/ListSeparator';
import { FormValidationError } from '@/components/PayforceInput/components/FormValidationError';

import { formatCurrency, removeNonDigits } from './lib/functions';

const INPUT_HEIGHT = 58;
const BORDER_COLOR = colors.palette.FMBlack[300];

type Props = {
  setAmount: any;
  inputContainerStyle?: object;
  label?: ReactElement;
  labelStyle?: object;
  isError?: boolean;
  inputStyle?: object;
  errorMsg?: string | FieldError | Merge<FieldError, FieldErrorsImpl<any>>;
  inputContainerStyles?: ViewStyle;
  style?: ViewStyle;
};

const CustomAmountInput: React.FC<Props> = ({
  inputContainerStyles,
  label,
  style,
  isError,
  errorMsg,
  inputStyle,
  setAmount,
}) => {
  const [formatted, setFormatted] = useState<string | undefined>('');

  const inputContainer = useMemo(
    () => [styles.inputContainer, inputContainerStyles],
    [inputContainerStyles],
  );

  const handleInputChange = (inputValue: string) => {
    const refinedValue = removeNonDigits(inputValue);

    if (refinedValue !== null) {
      setAmount(refinedValue);
      const formattedValue = formatCurrency(refinedValue);
      setFormatted(formattedValue);
    }
  };

  return (
    <View style={StyleSheet.flatten([styles.container, style])}>
      {label || null}
      <ListSeparator height={5} />
      <View style={inputContainer}>
        <TextInput
          style={[styles.inputWidth,
            styles.input,
            inputStyle]}
          keyboardType="numeric"
          value={formatted}
          onChangeText={handleInputChange}
        />
      </View>
      <ListSeparator height={5} />
      {isError && <FormValidationError errorMsg={errorMsg as string} />}
    </View>
  );
};

export default CustomAmountInput;

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  input: {
    backgroundColor: 'transparent',
    color: colors.typography.primary,
    flex: 2.5,
    fontFamily: FONT_DMSANS,
    fontSize: fontPixel(14),
    height: '100%',
    marginLeft: 15,
  },
  inputContainer: {
    borderColor: BORDER_COLOR,
    borderRadius: 4,
    borderStyle: 'solid',
    borderWidth: 1,
    height: INPUT_HEIGHT,
  },
  inputWidth: {
    width: '100%',
  },
});
