import { StyleSheet } from 'react-native';

import { normalize } from '@/common/utilities/normalize';
import { COLORS, FONT } from '@/styles/Styles';

export const styles = StyleSheet.create({
  btnText: {
    alignItems: 'center',
    flexDirection: 'row',
    height: normalize(42),
    justifyContent: 'space-between',
    paddingHorizontal: normalize(25),
    position: 'absolute',
    top: 0,
  },
  cancel: {
    left: 0,
  },
  container: {
    marginTop: normalize(15),
  },
  containerRoot: {
    flex: 1,
  },
  done: {
    right: 0,
  },
  error: {
    color: COLORS.red,
    fontFamily: FONT.primary,
    fontSize: normalize(10),
    marginTop: normalize(3),
  },
  inputContainer: {
    alignItems: 'center',
    backgroundColor: COLORS.white,
    borderColor: COLORS.gray500,
    borderRadius: 8,
    borderWidth: 1,
    color: COLORS.black,
    flexDirection: 'row',
    fontFamily: FONT.primary,
    fontSize: normalize(14),
    height: normalize(55),
    justifyContent: 'center',
    overflow: 'hidden',
    width: '100%',
  },
  inputGroup: {
    alignItems: 'center',
    backgroundColor: COLORS.white,
    borderWidth: 1,
    flexDirection: 'row',
    overflow: 'hidden',
    paddingHorizontal: normalize(25),
    width: '100%',
  },
  iosContainer: {
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    height: normalize(256),
    overflow: 'hidden',
  },
  iosDropBorderRadius: {
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  iosDropdown: {
    backgroundColor: COLORS.white,
    borderColor: COLORS.gray400,
    borderRadius: 8,
    borderWidth: 1,
    color: COLORS.gray500,
    height: normalize(55),
    marginTop: normalize(5),
    overflow: 'hidden',
    width: '100%',
  },
  iosHolder: {
    alignItems: 'flex-end',
    backgroundColor: 'rgba(0,0,0,0.3)',
    flex: 1,
    flexDirection: 'row',
  },
  itemStyle: {
    backgroundColor: COLORS.white,
    color: COLORS.gray500,
    fontFamily: FONT.primary,
    fontSize: normalize(16),
    lineHeight: 24,
  },
  label: {
    color: COLORS.label,
    fontSize: normalize(14),
    marginBottom: normalize(5),
    textTransform: 'capitalize',
  },
  space: { marginTop: normalize(20) },
  textContainer: {
    backgroundColor: COLORS.white,
    height: '100%',
    justifyContent: 'center',
  },
  textStyle: {
    color: COLORS.black,
    fontFamily: FONT.primary,
    fontSize: normalize(14),
    overflow: 'hidden',
    paddingHorizontal: normalize(10),
  },
  underlay: {
    borderTopColor: COLORS.white,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderTopWidth: normalize(1),
    flex: 1,
  },
});
