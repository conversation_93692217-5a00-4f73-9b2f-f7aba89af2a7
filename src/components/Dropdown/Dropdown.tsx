import { Picker } from '@react-native-picker/picker';
import React, { forwardRef, useState } from 'react';
import { Modal, Platform, TouchableHighlight, View } from 'react-native';

import { COLORS } from '@/styles/Styles';
import { DisplayText } from '@/utils/DisplayText';

import { styles } from './styles';

type Props = {
  items: {
    label?: string;
    value?: string | number | null;
  }[];
  value: string | number;
  onStateChange?: (state: string | number) => void;
  errorText?: any;
  error?: any;
  firstRenderLabel?: string;
  label: string;
  placeholder?: string;
};

const Dropdown = forwardRef(
  (
    {
      items,
      value,
      onStateChange,
      error,
      errorText,
      firstRenderLabel = 'Select an item',
      label,
      placeholder,
    }: Props,
    ref: any,
  ) => {
    const [show, setShow] = useState(false);
    const [selectedItem, setSelectedItem] = useState(
      items.find((item) => item.value === value) || null,
    );

    const renderDropdown = () => (
      <Picker
        dropdownIconColor="#000"
        ref={ref}
        mode="dropdown"
        selectedValue={selectedItem?.value}
        placeholder={placeholder}
        onValueChange={(itemValue) => {
          const newItem = items.find((item) => item.value === itemValue);
          setSelectedItem(newItem || null);
          onStateChange?.(itemValue !== null ? itemValue : '');
        }}
        style={Platform.OS === 'ios' ? {} : [styles.inputGroup]}
      >
        {items.map((item) => (
          <Picker.Item
            key={item.value}
            label={item.label}
            value={item.value}
            style={styles.itemStyle}
          />
        ))}
      </Picker>
    );

    const onCancel = () => {
      setShow(false);
    };

    const onDone = () => {
      setShow(false);
    };

    return (
      <View style={styles.container}>
        {label && <DisplayText style={styles.label}>{label}</DisplayText>}
        {Platform.OS !== 'ios' && (
          <View
            style={[
              styles.inputContainer,
              error && { borderColor: COLORS.red },
            ]}
          >
            {renderDropdown()}
          </View>
        )}

        <TouchableHighlight activeOpacity={0} onPress={() => setShow(true)}>
          <>
            {Platform.OS === 'ios' && (
              <View style={styles.iosDropdown}>
                <View style={styles.textContainer}>
                  <DisplayText
                    textStyle={{
                      ...styles.textStyle,
                      borderColor: error ? COLORS.red : COLORS.gray500,
                    }}
                  >
                    {selectedItem ? selectedItem.label : firstRenderLabel}
                  </DisplayText>
                </View>
                <Modal
                  transparent
                  animationType="slide"
                  visible={show}
                  supportedOrientations={['portrait']}
                  onRequestClose={() => setShow(true)}
                >
                  <View style={styles.containerRoot}>
                    <TouchableHighlight
                      style={styles.iosHolder}
                      activeOpacity={1}
                      onPress={() => setShow(false)}
                    >
                      <TouchableHighlight
                        underlayColor={COLORS.white}
                        style={styles.underlay}
                      >
                        <View
                          style={[
                            styles.iosContainer,
                            styles.iosDropBorderRadius,
                          ]}
                        >
                          <View style={styles.space}>{renderDropdown()}</View>
                          <TouchableHighlight
                            underlayColor="transparent"
                            onPress={onCancel}
                            style={[styles.btnText, styles.cancel]}
                          >
                            <DisplayText>Cancel</DisplayText>
                          </TouchableHighlight>
                          <TouchableHighlight
                            underlayColor="transparent"
                            onPress={onDone}
                            style={[styles.btnText, styles.done]}
                          >
                            <DisplayText>Done</DisplayText>
                          </TouchableHighlight>
                        </View>
                      </TouchableHighlight>
                    </TouchableHighlight>
                  </View>
                </Modal>
              </View>
            )}
          </>
        </TouchableHighlight>
        {error && <DisplayText style={styles.error}>{errorText}</DisplayText>}
      </View>
    );
  },
);

export default Dropdown;
