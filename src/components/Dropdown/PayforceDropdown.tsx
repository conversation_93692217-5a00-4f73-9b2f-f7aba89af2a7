import React, { ReactElement, useMemo } from 'react';
import { StyleSheet, View } from 'react-native';
import RNPickerSelect, { PickerSelectProps } from 'react-native-picker-select';

import { CaretDown } from '@/asset/index';
import { colors } from '@/common/styles/colors';
import { globalStyles } from '@/common/styles/globalStyles';
import { ListSeparator } from '@/components/ListSeparator';

import { staticTypoConfig } from '../PayforceText/PayforceText';

const INPUT_HEIGHT = 58;
const BORDER_COLOR = colors.palette.FMBlack[300];

const Icon = () => <CaretDown />;

type Props = Omit<PickerSelectProps, 'placeholder'> & {
  placeholder: string;
  label?: ReactElement;
  leftIcon?: ReactElement;
  useLeftIcon?: boolean;
  height?: number;
};

const COLOR_PLACEHOLDER = colors.additionalUIColors.selectPlaceholder;

export const DropDownList: React.FC<Props> = ({
  placeholder,
  label,
  leftIcon,
  useLeftIcon = false,
  height,
  ...props
}) => {
  const memoizedPlaceholder = useMemo<PickerSelectProps['placeholder']>(
    () => ({
      label: placeholder,
      value: '',
      color: COLOR_PLACEHOLDER,
    }),
    [placeholder],
  );

  if (useLeftIcon) {
    return (
      <View>
        {label || null}
        <View style={styles.leftIconContainer}>
          {leftIcon}
          <RNPickerSelect
            Icon={Icon}
            style={useLeftPicker}
            placeholder={memoizedPlaceholder}
            fixAndroidTouchableBug
            useNativeAndroidPickerStyle={false}
            touchableWrapperProps={useLeftTouchableWrapperProps}
            {...props}
          />
        </View>
      </View>
    );
  }

  return (
    <View>
      {label
        ? (
          <>
            {label}
            <ListSeparator height={5} />
          </>
          )
        : null}
      <View
        style={{
          height: height || INPUT_HEIGHT,
        }}
      >
        <RNPickerSelect
          Icon={Icon}
          style={pickerStyles}
          placeholder={memoizedPlaceholder}
          fixAndroidTouchableBug
          useNativeAndroidPickerStyle={false}
          touchableWrapperProps={touchableWrapperProps}
          {...props}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  inputContainer: {
    alignItems: 'center',
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    ...globalStyles.px(15),
  },
  leftIconContainer: {
    alignItems: 'center',
    borderColor: BORDER_COLOR,
    borderRadius: 3,
    borderWidth: 0.5,
    flexDirection: 'row',
  },
  text: {
    flex: 1,
    ...staticTypoConfig.Body2,
    color: colors.typography.label,
  },
});

const pickerStyles: PickerSelectProps['style'] = {
  viewContainer: {
    height: '100%',
    borderColor: BORDER_COLOR,
    borderWidth: 0.5,
    borderStyle: 'solid',
    borderRadius: 3,
    shadowOpacity: 0,
  },
  iconContainer: {
    ...globalStyles.mr(8),
  },
  placeholder: { ...styles.text, color: COLOR_PLACEHOLDER },
  inputIOS: styles.text,
  inputAndroid: styles.text,
  headlessAndroidContainer: {
    flex: 1,
  },
  inputAndroidContainer: styles.inputContainer,
  inputIOSContainer: styles.inputContainer,
};

const useLeftPicker: PickerSelectProps['style'] = {
  ...pickerStyles,
  viewContainer: {
    height: '100%',
    borderStyle: 'solid',
    borderRadius: 3,
    shadowOpacity: 0,
    flex: 1,
  },
};

const useLeftTouchableWrapperProps: PickerSelectProps['touchableWrapperProps']
  = {
    style: useLeftPicker.viewContainer,
  };

const touchableWrapperProps: PickerSelectProps['touchableWrapperProps'] = {
  style: pickerStyles.viewContainer,
};
