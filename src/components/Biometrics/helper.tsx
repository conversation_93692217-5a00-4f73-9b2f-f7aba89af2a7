// ------------ import external dependencies -----------
// ------------ import internal dependencies ------------
import { useMutation } from '@tanstack/react-query';
import { useState } from 'react';
import TouchID from 'react-native-touch-id';

import { storage } from '@/common/storage';
import { api } from '@/functions/api';
import { log } from '@/utils/log';

type validatePassword = {
  password: string;
};

interface useBioMetricsProps {
  callBackStatus?: (status: boolean) => void;
  closeAction: () => void;
  openResp: () => void;
}

/**
 * Validate user password
 * @returns
 */
export const useValidateUserPassword = () =>
  useMutation(async (data: validatePassword) => {
    const { password } = data;

    const response = await api.post(`/au/mobile/agent/validate-password`, {
      password,
    });

    return response.data;
  });

export const useBioMetrics = ({
  callBackStatus,
  closeAction,
  openResp,
}: useBioMetricsProps) => {
  // ------ component state managers -------
  const [isSuccess, setIsSuccess] = useState(false);
  const [isFailed, setIsFailed] = useState(false);
  const [title, setTitle] = useState('');

  // ------ intialize custom hooks -------
  const { mutateAsync: valPassword, isLoading } = useValidateUserPassword();

  const isPasswordMatch = async (password: string) => {
    try {
      const passwordResp = await valPassword({ password });

      if (passwordResp.error_message === null) {
        const response = await TouchID.authenticate('Check for validity');
        storage.set('password', password);
        const biometricType = await TouchID.isSupported();
        storage.set(
          'bioType',
          typeof response !== 'undefined' ? 'TouchID' : biometricType,
        );

        setIsSuccess(true);
        setIsFailed(false);
        closeAction();
        openResp();
        callBackStatus?.(true);
      } else {
        setIsSuccess(false);
        setTitle('Please enable bio metrics');
        setIsFailed(true);
        closeAction();
        openResp();
        callBackStatus?.(false);
      }
    } catch (error) {
      setIsSuccess(false);
      setTitle('Please enable bio metrics');
      setIsFailed(true);
      closeAction();
      openResp();
      callBackStatus?.(false);
      log({ name: '[USE BIOMETRICS::]', error });
    }
  };

  return {
    isPasswordMatch,
    isSuccess,
    isFailed,
    title,
    isLoading,
  };
};
