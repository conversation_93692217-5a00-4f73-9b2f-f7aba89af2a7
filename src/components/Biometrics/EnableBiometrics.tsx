import { BottomSheetModal } from '@gorhom/bottom-sheet';
import React, { useEffect, useState } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import TouchID from 'react-native-touch-id';

import { FaceId, FingerPrint } from '@/asset/index';
import { storage } from '@/common/storage';
import { colors } from '@/common/styles/colors';
import {
  pixelSizeHorizontal,
  pixelSizeVertical,
} from '@/common/utilities/normalize';
import CustomBottomSheet from '@/components/BottomSheet/BottomSheet';
import { PayforceButton } from '@/components/PayforceButton';
import { PayforceText } from '@/components/PayforceText/PayforceText';
import { log } from '@/utils/log';

type Props = {
  buttonAction: () => void;
  sheetRef: React.MutableRefObject<BottomSheetModal | null>;
  callBack?: () => void;
};

const EnableBiometrics: React.FC<Props> = ({
  sheetRef,
  buttonAction,
  callBack,
}) => {
  // ------ component state managers ------
  const [bioType, setBioType] = useState('');

  const checkBioType = async () => {
    try {
      const response = await TouchID.isSupported();
      setBioType(typeof response !== 'undefined' ? 'TouchID' : response);
    } catch (error) {
      log({ error });
    }
  };

  useEffect(() => {
    checkBioType();
  }, []);

  return (
    <CustomBottomSheet
      ref={sheetRef}
      closeModal={buttonAction}
      snapBegin="65%"
      snapMid="65%"
      snapEnd="65%"
    >
      <View style={styles.wrapper}>
        <View>
          <PayforceText.Body3Bold
            color={colors.palette.FMBlack[800]}
            style={styles.heading}
          >
            Enable Biometrics Log In
          </PayforceText.Body3Bold>
          <PayforceText.Body1
            color={colors.palette.FMBlack[500]}
            style={styles.desc}
          >
            Use biometrics as a quick and secure method of logging in to your
            FairMoney Business app
          </PayforceText.Body1>
        </View>
        <View style={styles.img}>
          {bioType === 'FaceID' ? <FaceId /> : null}
          {bioType === 'TouchID' ? <FingerPrint /> : null}
        </View>
        <PayforceButton
          //   disabled={!isValid || loading || error}
          type="small"
          title="Enable Biometrics"
          onPress={() => {
            callBack?.();
            storage.set('isFirstLogIn', true);
            buttonAction();
          }}
          //   isLoading={isLoading || loading}
          style={styles.btn}
        />
        <TouchableOpacity
          onPress={() => {
            buttonAction();
            storage.set('isFirstLogIn', true);
          }}
        >
          <PayforceText.Body1Bold
            color={colors.palette.FMBlack[800]}
            style={styles.later}
          >
            Do it later
          </PayforceText.Body1Bold>
        </TouchableOpacity>
      </View>
    </CustomBottomSheet>
  );
};

export default EnableBiometrics;

// ------ component styles ------
const styles = StyleSheet.create({
  btn: {
    marginBottom: pixelSizeVertical(15),
  },
  desc: {
    marginTop: pixelSizeVertical(12),
    textAlign: 'center',
  },
  heading: {
    textAlign: 'center',
  },
  img: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: pixelSizeVertical(18),
  },
  later: {
    textAlign: 'center',
  },
  wrapper: {
    paddingHorizontal: pixelSizeHorizontal(16),
    paddingVertical: pixelSizeVertical(20),
  },
});
