// ------------ import external dependencies ------------
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import React, { useCallback, useState } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';

import { Assets, TimesIcon } from '@/asset/index';
import { colors } from '@/common/styles/colors';
import {
  pixelSizeHorizontal,
  pixelSizeVertical,
} from '@/common/utilities/normalize';
import BioMetricsResponse from '@/components/Biometrics/BiometricsResponse';
//   --------- import internal dependencies ------------
import CustomBottomSheet from '@/components/BottomSheet/BottomSheet';
import { useCreateBottomSheetModal } from '@/components/BottomSheet/helper';
import { PasswordInput } from '@/components/form/PasswordInput';
import { ListSeparator } from '@/components/ListSeparator';
import { PayforceButton } from '@/components/PayforceButton';
import { PayforceText } from '@/components/PayforceText/PayforceText';

import { useBioMetrics } from './helper';

type Props = {
  buttonAction: () => void;
  openAction?: () => void;
  sheetRef: React.MutableRefObject<BottomSheetModal | null>;
  navigation?: any;
  callBackStatus?: any;
};

const ConfirmBiometricsSheet: React.FC<Props> = ({
  sheetRef,
  buttonAction,
  openAction,
  navigation,
  callBackStatus,
}) => {
  // ------ component state managers ------
  const [password, setPassword] = useState('');

  // ------ intialize custom hooks -------
  const [enableBioRespRef, openEnableBioResp, closeEnableBioRespModal]
    = useCreateBottomSheetModal('enableBioResp');

  const { isPasswordMatch, isSuccess, isFailed, title, isLoading }
    = useBioMetrics({
      callBackStatus,
      closeAction: buttonAction,
      openResp: openEnableBioResp,
    });

  const onChangePasswordText = useCallback((text: string) => {
    setPassword(text);
  }, []);

  return (
    <>
      <CustomBottomSheet
        ref={sheetRef}
        snapBegin="100%"
        snapMid="100%"
        snapEnd="100%"
        closeModal={buttonAction}
      >
        <View style={styles.wrapper}>
          <View>
            <TouchableOpacity onPress={buttonAction}>
              <TimesIcon />
            </TouchableOpacity>
            <PayforceText.Body3Medium
              color={colors.palette.FMBlack[800]}
              style={styles.heading}
            >
              Enter your account password to confirm Biometric Login
            </PayforceText.Body3Medium>
            <View>
              <ListSeparator height={15} />
              <PasswordInput
                label={(
                  <PayforceText.Body2
                    style={styles.passwordInput}
                    color="label"
                  >
                    Password
                  </PayforceText.Body2>
                )}
                onChangeText={onChangePasswordText}
                returnKeyType="done"
              />
              <TouchableOpacity
                onPress={() =>
                  navigation?.navigate('ForgotPasswordUserVerification')}
              >
                <PayforceText.IconLabel
                  color="form"
                  style={styles.forgotPassword}
                >
                  Forgot Password?
                </PayforceText.IconLabel>
              </TouchableOpacity>
            </View>
          </View>

          {/* ------ submit section ------ */}
          <View style={styles.submitContainer}>
            <PayforceButton
              disabled={isLoading}
              type="small"
              title="Done"
              onPress={() => isPasswordMatch(password)}
              isLoading={isLoading}
              style={styles.btn}
            />
            <View style={styles.cautionContainer}>
              <Assets.SettingsIcons.Caution />
              <PayforceText.Decorative color={colors.palette.warning[600]}>
                Please make sure that you do not have fingerprints of any other
                individual registered on this device
              </PayforceText.Decorative>
            </View>
          </View>
        </View>
      </CustomBottomSheet>

      {/* ------ enable transaction feature response------  */}
      <BioMetricsResponse
        sheetRef={enableBioRespRef}
        buttonAction={closeEnableBioRespModal}
        isSuccess={isSuccess}
        isError={isFailed}
        retry={() => {
          closeEnableBioRespModal();
          openAction?.();
        }}
        title={title}
      />
    </>
  );
};

export default ConfirmBiometricsSheet;

// ----- component styles -------
const styles = StyleSheet.create({
  btn: {
    marginBottom: pixelSizeVertical(15),
  },
  cautionContainer: {
    backgroundColor: colors.palette.warning[50],
    borderRadius: 4,
    flexDirection: 'row',
    gap: 8,
    paddingHorizontal: pixelSizeHorizontal(12),
    paddingVertical: pixelSizeVertical(8),
  },
  forgotPassword: {
    color: colors.palette.FMGreen[500],
    marginTop: pixelSizeVertical(10),
  },
  heading: {
    marginTop: pixelSizeVertical(24),
  },
  passwordInput: {
    marginBottom: pixelSizeVertical(5),
  },
  submitContainer: {
    bottom: 130,
    left: pixelSizeHorizontal(16),
    position: 'absolute',
    width: '100%',
  },
  wrapper: {
    height: '100%',
    paddingHorizontal: pixelSizeHorizontal(16),
    paddingVertical: pixelSizeVertical(36),
  },
});
