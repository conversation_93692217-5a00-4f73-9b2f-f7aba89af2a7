// ------------ import external dependencies -------------
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';

import { BioError, BioSuccess } from '@/asset/index';
import { colors } from '@/common/styles/colors';
import {
  pixelSizeHorizontal,
  pixelSizeVertical,
} from '@/common/utilities/normalize';
//   --------- import internal dependencies ------------
import CustomBottomSheet from '@/components/BottomSheet/BottomSheet';
import { PayforceButton } from '@/components/PayforceButton';
import { PayforceText } from '@/components/PayforceText/PayforceText';

type Props = {
  buttonAction: () => void;
  sheetRef: React.MutableRefObject<BottomSheetModal | null>;
  isError?: boolean;
  isSuccess?: boolean;
  retry?: any;
  title?: string;
};

const BioMetricsResponse: React.FC<Props> = ({
  sheetRef,
  buttonAction,
  isSuccess,
  isError,
  retry,
  title = 'Biometrics Enrollment Failed',
}) => (
  <CustomBottomSheet
    ref={sheetRef}
    closeModal={buttonAction}
    snapBegin="75%"
    snapMid="75%"
    snapEnd="75%"
  >
    <View style={styles.wrapper}>
      <View style={styles.img}>
        {/* ------- biometrics failed image ------ */}
        {isError ? <BioError /> : null}

        {/* ------ biometrics success image ------ */}
        {isSuccess ? <BioSuccess /> : null}
      </View>

      {/* ------ biometrics failed description ------ */}
      {isError
        ? (
          <View style={styles.descContainer}>
            <PayforceText.Body3Bold
              color={colors.palette.FMBlack[800]}
              style={styles.heading}
            >
              {title ?? 'Biometrics Enrollment Failed'}
            </PayforceText.Body3Bold>
            <PayforceText.Body1
              color={colors.palette.FMBlack[500]}
              style={styles.desc}
            >
              Biometrics setup failed. Please retry for seamless login.
            </PayforceText.Body1>
          </View>
          )
        : null}

      {/* ------ biometrics successful description ------ */}
      {isSuccess
        ? (
          <View style={styles.descContainer}>
            <PayforceText.Body3Bold
              color={colors.palette.FMBlack[800]}
              style={styles.heading}
            >
              Biometrics enabled successfully
            </PayforceText.Body3Bold>
            <PayforceText.Body1
              color={colors.palette.FMBlack[500]}
              style={styles.desc}
            >
              You can now Log in to your FairMoney Business app using your
              biometrics only.
            </PayforceText.Body1>
          </View>
          )
        : null}

      {/* ------ biometrics failed action ------ */}
      {isError
        ? (
          <View>
            <PayforceButton
              type="small"
              title="Try again"
              onPress={() => {
                buttonAction();
                retry();
              }}
              style={styles.btn}
            />
            <TouchableOpacity
              onPress={() => {
                buttonAction();
              }}
            >
              <PayforceText.Body1Bold
                color={colors.palette.FMBlack[800]}
                style={styles.later}
              >
                Do it later
              </PayforceText.Body1Bold>
            </TouchableOpacity>
          </View>
          )
        : null}

      {/* ------ biometrics success action ------ */}
      {isSuccess
        ? (
          <View>
            <PayforceButton
              type="small"
              title="Got it"
              onPress={buttonAction}
              style={styles.btn}
            />
          </View>
          )
        : null}
    </View>
  </CustomBottomSheet>
);

export default BioMetricsResponse;

// ------ component styles ------
const styles = StyleSheet.create({
  btn: {
    marginBottom: pixelSizeVertical(15),
  },
  desc: {
    marginTop: pixelSizeVertical(12),
    textAlign: 'center',
  },
  descContainer: {
    marginBottom: pixelSizeVertical(24),
  },
  heading: {
    textAlign: 'center',
  },
  img: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: pixelSizeVertical(18),
  },
  later: {
    textAlign: 'center',
  },
  wrapper: {
    paddingHorizontal: pixelSizeHorizontal(16),
    paddingVertical: pixelSizeVertical(18),
  },
});
