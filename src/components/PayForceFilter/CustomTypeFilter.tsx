import React, { useState } from 'react';
import { StyleSheet, View } from 'react-native';

import { colors } from '@/common/styles/colors';
import { ListSeparator } from '@/components/ListSeparator';
import { PayforceText } from '@/components/PayforceText/PayforceText';

import CustomFilterItem from './CustomFilterItem';

type Props = {
  types: string[];
  spacing?: { top?: number; head?: number; item?: number };
  heading?: string | null;
  onChangeActiveItem?: (item: string) => void;
  value?: string;
};

const CustomTypeFilter = ({
  types,
  heading,
  spacing = { top: 0, head: 0, item: 0 },
  onChangeActiveItem = () => {},
  value = '',
}: Props) => {
  const [activeItem, setIsActiveItem] = useState(value);

  return (
    <View>
      <ListSeparator height={spacing.top || 25} />
      <PayforceText.Body3 color="label" style={styles.header}>
        {heading || 'By type'}
      </PayforceText.Body3>
      <ListSeparator height={spacing.head || 15} />
      {types.map((item, index) => (
        <View key={`${item}-${index}`}>
          <CustomFilterItem
            label={item}
            onItemPress={() => {
              setIsActiveItem(item);
              onChangeActiveItem(item);
            }}
            isActive={activeItem === item}
          />
          <ListSeparator height={spacing.item || 15} />
        </View>
      ))}
    </View>
  );
};

export default CustomTypeFilter;

const styles = StyleSheet.create({
  header: {
    color: colors.palette.FMBlue[800],
  },
});
