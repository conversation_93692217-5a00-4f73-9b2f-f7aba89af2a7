import React from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';

import PayforceRadio from '@/components/PayforceRadioButton';
import { PayforceText } from '@/components/PayforceText/PayforceText';

type Props = {
  label: string;
  onItemPress: () => void;
  isActive: boolean;
};

const CustomFilterItem = ({ onItemPress, label, isActive }: Props) => (
  <TouchableOpacity
    activeOpacity={0.8}
    onPress={onItemPress}
    style={styles.container}
  >
    <PayforceText.Body1 color="form">{label}</PayforceText.Body1>
    <PayforceRadio onPress={onItemPress} active={isActive} />
  </TouchableOpacity>
);

export default CustomFilterItem;

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
});
