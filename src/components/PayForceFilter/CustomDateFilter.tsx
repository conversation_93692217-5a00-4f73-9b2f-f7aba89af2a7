import React, { useState } from 'react';
import { StyleSheet, View } from 'react-native';

import { colors } from '@/common/styles/colors';
import { ListSeparator } from '@/components/ListSeparator';
import { PayforceText } from '@/components/PayforceText/PayforceText';

import CustomFilterItem from './CustomFilterItem';

type Props = {
  dates: string[];
  spacing?: { top?: number; head?: number; item?: number };
  onSelect: (date: string) => void;
  selectedDate: string;
};

const CustomDateFilter = ({
  dates,
  spacing,
  onSelect,
  selectedDate,
}: Props) => {
  const [activeItem, setIsActiveItem] = useState(selectedDate);

  const handleSelection = (item: string) => {
    setIsActiveItem(item);
    onSelect(item);
  };

  return (
    <View>
      <ListSeparator height={spacing?.top || 25} />
      <PayforceText.Body3 color="label" style={styles.header}>
        By date
      </PayforceText.Body3>
      <ListSeparator height={spacing?.head || 15} />
      {dates.map((date) => (
        <View key={date}>
          <CustomFilterItem
            label={date}
            onItemPress={() => handleSelection(date)}
            isActive={activeItem === date}
          />
          <ListSeparator height={spacing?.item || 15} />
        </View>
      ))}
    </View>
  );
};

export default CustomDateFilter;

const styles = StyleSheet.create({
  header: {
    color: colors.palette.FMBlue[800],
  },
});
