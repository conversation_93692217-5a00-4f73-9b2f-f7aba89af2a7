import React from 'react';
import { StyleSheet, View } from 'react-native';

import { TickIcon } from '@/asset/index';
import { colors } from '@/common/styles/colors';
import { normalize, pixelSizeVertical } from '@/common/utilities/normalize';
import { PayforceText } from '@/components/PayforceText/PayforceText';
import Spacer from '@/screens/Onboarding/components/Spacer';

type Props = {
  password: string;
};

const Regex = /[ `!@#$%£^&*()_+\-={};':"\\|,.<>/?~]/;

const PasswordStrength = ({ password }: Props) => (
  <View>
    <View style={styles.container}>
      <PayforceText.IconLabel style={styles.headerText} color="label">
        The password needs to contain:
      </PayforceText.IconLabel>
      <View style={styles.row}>
        <View style={[styles.circle, password.length > 7 && styles.valid]}>
          <TickIcon />
        </View>
        <PayforceText.Decorative style={styles.textStyle} color="label">
          A minimum of 8 characters
        </PayforceText.Decorative>
      </View>
      <Spacer height={10} />
      <View style={styles.row}>
        <View style={[styles.circle, password.match(/[a-z]/) && styles.valid]}>
          <TickIcon />
        </View>
        <PayforceText.Decorative style={styles.textStyle} color="label">
          A lowercase letter (a-z)
        </PayforceText.Decorative>
      </View>
      <Spacer height={10} />
      <View style={styles.row}>
        <View style={[styles.circle, password.match(/[A-Z]/) && styles.valid]}>
          <TickIcon />
        </View>
        <PayforceText.Decorative style={styles.textStyle} color="label">
          A uppercase letter (A-Z)
        </PayforceText.Decorative>
      </View>
      <Spacer height={10} />
      <View style={styles.row}>
        <View
          style={[
            styles.circle,
            password.match(Regex)
            && styles.valid,
          ]}
        >
          <TickIcon />
        </View>
        <PayforceText.Decorative style={styles.textStyle} color="label">
          A special character (e.g. !@#$)
        </PayforceText.Decorative>
      </View>
      <Spacer height={10} />
      <View style={styles.row}>
        <View style={[styles.circle, password.match(/[0-9]/) && styles.valid]}>
          <TickIcon />
        </View>
        <PayforceText.Decorative style={styles.textStyle} color="label">
          A number (1-9)
        </PayforceText.Decorative>
      </View>
    </View>
  </View>
);

export default PasswordStrength;

const styles = StyleSheet.create({
  circle: {
    alignItems: 'center',
    borderColor: colors.palette.FMBlack[300],
    borderRadius: 8,
    borderWidth: 1,
    height: normalize(16),
    justifyContent: 'center',
    width: normalize(16),
  },
  container: {
    backgroundColor: colors.palette.FMBlack[50],
    height: normalize(186),
    marginTop: normalize(10),
    paddingHorizontal: normalize(15),
    paddingVertical: normalize(15),
  },
  headerText: {
    marginBottom: pixelSizeVertical(15),
    marginTop: pixelSizeVertical(5),
  },
  row: {
    flexDirection: 'row',
  },
  textStyle: {
    marginLeft: pixelSizeVertical(5),
  },
  valid: {
    backgroundColor: colors.palette.FMBlack[900],
  },
});
