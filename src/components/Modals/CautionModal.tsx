import React from 'react';
import { StyleSheet, View } from 'react-native';

import { Assets } from '@/asset/index';
import { colors } from '@/common/styles/colors';
import { pixelSizeVertical } from '@/common/utilities/normalize';
import { PayforceButton } from '@/components/PayforceButton';
import PayforceModal from '@/components/PayforceModals/Modals/PayforceModal';
import { PayforceText } from '@/components/PayforceText/PayforceText';

interface Props {
  innerContainerStyles?: any;
  isVisible: boolean;
  onProceed?: () => void;
  onClose?: () => void;
}

const CautionModal = ({ isVisible, onProceed, onClose }: Props) => (
  <PayforceModal duration={500} visible={isVisible} options={{ from: 'top' }}>
    <View style={cautionStyles.container}>
      <Assets.LoansIcons.LoanCaution />
      <PayforceText.ModalHeader
        color={colors.palette.FMBlack[900]}
        style={cautionStyles.heading}
      >
        Don&apos;t go yet
      </PayforceText.ModalHeader>
      <PayforceText.Subheader
        color={colors.palette.FMBlack[500]}
        style={cautionStyles.subHeading}
      >
        We need this access to be able to accurately lend to you. If you
        disagree, you will lose access to this loan offer.
      </PayforceText.Subheader>
      <PayforceButton
        onPress={onProceed}
        style={cautionStyles.btnStyle}
        type="small"
      >
        <PayforceText.Body1Medium color={colors.white}>
          Okay, continue
        </PayforceText.Body1Medium>
      </PayforceButton>
      <PayforceButton
        onPress={onClose}
        style={{ ...cautionStyles.btnStyle, marginTop: pixelSizeVertical(12) }}
        type="small"
        color={colors.white}
      >
        <PayforceText.Body1Medium color={colors.palette.FMBlack[900]}>
          Take me to home
        </PayforceText.Body1Medium>
      </PayforceButton>
    </View>
  </PayforceModal>
);

export default CautionModal;

// ------- component styles ------
const cautionStyles = StyleSheet.create({
  btnStyle: {
    marginTop: pixelSizeVertical(20),
    width: '100%',
    // marginBottom: pixelSizeVertical(12)
  },
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: pixelSizeVertical(24),
  },
  heading: {
    marginBottom: pixelSizeVertical(8),
    marginTop: pixelSizeVertical(20),
    textAlign: 'center',
  },
  subHeading: {
    textAlign: 'center',
  },
});
