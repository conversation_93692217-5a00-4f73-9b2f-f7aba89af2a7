/**
 * A custom modal component that animates its appearance from the top or bottom of the screen
 * based on the 'from' prop.
 */

import React, { ReactNode } from 'react';
import { Animated, StyleProp, StyleSheet, View, ViewStyle } from 'react-native';

import { normalize } from '@/common/utilities/normalize';

import { useCustomModalAnimation } from './hooks/useCustomModalAnimation';
import { styles } from './style';

type Props = {
  children: ReactNode;
  visible: boolean;
  duration: number;
  options?: {
    easing?: any;
    useNativeDriver?: boolean;
  };
  height?: number;
  from?: 'top' | 'bottom';
  innerContainerStyles?: StyleProp<ViewStyle>;
};

const CustomModal = ({
  children,
  visible,
  duration,
  options = {},
  height = 250,
  from = 'top',
  innerContainerStyles,
}: Props) => {
  const { transY, generateBackgroundOpacity } = useCustomModalAnimation(
    visible,
    duration,
    options,
    from,
  );

  if (!visible) {
    return null; // Return null if the modal is not visible
  }

  return (
    <>
      {/* A transparent view that serves as the background overlay for the modal */}
      <Animated.View
        pointerEvents="none" // the view should not block any touch events
        style={[
          StyleSheet.absoluteFill,
          styles.outerContainer,
          { opacity: generateBackgroundOpacity() },
        ]}
      />

      {/* The actual modal view that contains the content */}
      <Animated.View
        testID="custom-modal"
        style={[
          StyleSheet.absoluteFill,
          styles.container,
          { transform: [{ translateY: transY }] },
        ]}
      >
        <View
          style={[
            styles.innerContainer,
            { height: normalize(height) },
            innerContainerStyles,
          ]}
        >
          <View style={styles.childContainer}>{children}</View>
        </View>
      </Animated.View>
    </>
  );
};

export default CustomModal;
