import React from 'react';
import { ActivityIndicator, StyleSheet, View } from 'react-native';

import { Assets } from '@/asset/index';
import { colors } from '@/common/styles/colors';
import { ListSeparator } from '@/components/ListSeparator';
import { PayforceButton } from '@/components/PayforceButton';
import { PayforceText } from '@/components/PayforceText/PayforceText';

import PayforceModal from '../PayforceModals/Modals/PayforceModal';

type BaseProps = {
  onClose?: () => void;
  title: string | React.ReactNode;
  description: string | React.ReactNode;
  isVisible: boolean;
  firstButtonLabel?: string;
  firstButtonColor?: string;
  firstButtonPress?: () => void;
  secondButtonLabel?: string;
  secondButtonColor?: string;
  secondButtonPress?: () => void;
  isLoading?: boolean;
  icon?: React.JSX.Element;
  isSuccess?: boolean;
};

type IconProp<T> = {
  icon?: T;
};

export type TCustomStatusModalProps<T = undefined> = T extends undefined
  ? BaseProps & IconProp<undefined>
  : BaseProps & IconProp<T>;

const styles = StyleSheet.create({
  button: {
    backgroundColor: colors.white,
    borderColor: colors.palette.FMBlack[300],
    borderWidth: 1,
  },
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  modalTitleVariation: {
    justifyContent: 'center',
    textAlign: 'center',
  },
  textAlign: {
    textAlign: 'center',
  },
});

const LoanModal = <T = React.JSX.Element>({
  icon,
  ...rest
}: TCustomStatusModalProps<T>): React.JSX.Element => {
  const {
    isSuccess,
    isVisible,
    title,
    onClose = () => {},
    isLoading,
    description,
    firstButtonLabel = 'Proceed',
    firstButtonColor,
    firstButtonPress,
    secondButtonLabel,
    secondButtonPress,
    secondButtonColor,
  } = rest;

  const getDefaultIcon = () => {
    switch (isSuccess) {
      case true:
        return <Assets.SavingsIcons.SuccessTick />;
      default:
        return <Assets.SavingsIcons.Danger />;
    }
  };

  const getIcon = () => {
    switch (typeof icon) {
      case 'undefined':
        return getDefaultIcon();
      default:
        return <>{icon}</>;
    }
  };

  return (
    <PayforceModal
      visible={isVisible}
      duration={500}
      options={{ from: 'top' }}
    >
      <View style={styles.container}>
        {getIcon()}
        <ListSeparator height={15} />
        <PayforceText.Button
          color={colors.palette.FMBlack[900]}
          style={styles.modalTitleVariation}
        >
          {title}
        </PayforceText.Button>
        <ListSeparator height={15} />
        <PayforceText.Subheader style={styles.textAlign} color="form">
          {description}
        </PayforceText.Subheader>

        <ListSeparator height={25} />
        {isLoading
          ? (
            <>
              <ActivityIndicator color={colors.palette.FMBlack[500]} />
            </>
            )
          : (
            <>
              <PayforceButton
                type="small"
                onPress={firstButtonPress}
                color={firstButtonColor || colors.palette.FMBlack[900]}
              >
                <PayforceText.Body1Medium color={colors.white}>
                  {firstButtonLabel}
                </PayforceText.Body1Medium>
              </PayforceButton>
              <ListSeparator height={15} />
              {secondButtonLabel
                ? (
                  <>
                    <PayforceButton
                      type="small"
                      color={secondButtonColor || colors.white}
                      style={styles.button}
                      onPress={secondButtonPress || onClose}
                    >
                      <PayforceText.Body1 color="form">
                        {secondButtonLabel}
                      </PayforceText.Body1>
                    </PayforceButton>
                    <ListSeparator height={5} />
                  </>
                  )
                : undefined}

            </>
            )}
      </View>
    </PayforceModal>
  );
};

export default LoanModal;
