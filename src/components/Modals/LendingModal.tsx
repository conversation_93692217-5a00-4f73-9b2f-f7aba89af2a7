import React from 'react';
import { StyleSheet, View } from 'react-native';

import { Assets } from '@/asset/index';
import { colors } from '@/common/styles/colors';
import { pixelSizeVertical } from '@/common/utilities/normalize';
import PayforceModal from '@/components/PayforceModals/Modals/PayforceModal';
import { PayforceText } from '@/components/PayforceText/PayforceText';
import useLoanOfferAwait from '@/screens/Loans/hooks/useLoanOfferAwait';

interface Props {
  innerContainerStyles?: any;
  isVisible: boolean;
  onRejected: () => void | undefined;
  onDelay: () => void | undefined;
  onSuccess: () => void | undefined;
  onFailed: () => void | undefined;
}

const LendingModal = ({ isVisible, onRejected, onDelay, onSuccess }: Props) => {
  useLoanOfferAwait({ onRejected, onDelay, onSuccess });

  return (
    <PayforceModal duration={500} visible={isVisible} options={{ from: 'top' }}>
      <View style={lendingStyles.container}>
        <Assets.LoansIcons.loanSuccess />
        <PayforceText.ModalHeader
          color={colors.palette.FMBlack[900]}
          style={lendingStyles.heading}
        >
          Preparing your loan offer...
        </PayforceText.ModalHeader>
        <PayforceText.Subheader
          color={colors.palette.FMBlack[500]}
          style={lendingStyles.subHeading}
        >
          We have successfully confirmed your identity and details. Hold on a
          bit more, we are preparing the right offers for you to choose from
        </PayforceText.Subheader>
      </View>
    </PayforceModal>
  );
};

export default LendingModal;

// ------- component styles ------
const lendingStyles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: pixelSizeVertical(0),
  },
  heading: {
    marginBottom: pixelSizeVertical(8),
    marginTop: pixelSizeVertical(20),
    textAlign: 'center',
  },
  subHeading: {
    textAlign: 'center',
  },
});
