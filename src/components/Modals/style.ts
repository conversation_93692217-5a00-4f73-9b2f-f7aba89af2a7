import { StyleSheet } from 'react-native';

import { normalize } from '@/common/utilities/normalize';
import { COLORS } from '@/styles/Styles';

export const styles = StyleSheet.create({
  button: {
    width: '45%',
  },
  cancel: {
    backgroundColor: 'transparent',
    borderColor: COLORS.gray,
    borderWidth: 1,
  },
  childContainer: {
    paddingHorizontal: normalize(15),
  },
  children: {
    alignItems: 'center',
  },

  confirm: {
    backgroundColor: COLORS.primary,
  },

  confirmBtn: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: normalize(10),
  },
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  error: {
    backgroundColor: COLORS.red,
  },
  errorHeading: {
    color: COLORS.darkGray,
    marginVertical: normalize(10),
  },
  heading: {
    color: COLORS.primary,
    fontSize: normalize(20),
    fontWeight: '600',
    letterSpacing: -0.02,
    marginLeft: normalize(10),
  },
  innerContainer: {
    alignItems: 'center',
    backgroundColor: COLORS.white,
    borderRadius: 20,
    justifyContent: 'center',
    overflow: 'hidden',
    position: 'relative',
    width: normalize(341),
  },
  outerContainer: {
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
  },
  subHeading: {
    color: COLORS.primary,
    fontSize: normalize(14),
    fontWeight: '400',
    marginBottom: normalize(20),
    marginVertical: normalize(10),
    textAlign: 'center',
  },
  top: {
    alignItems: 'center',
    flexDirection: 'row',
  },
});
