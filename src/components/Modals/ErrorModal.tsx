import React from 'react';
import { View } from 'react-native';

import ErrorIcon from '@/assets/svg/error.svg';
import Button from '@/utils/Button/Button';
import { DisplayText } from '@/utils/DisplayText';

import CustomModal from './Modal';
import { styles } from './style';

type Props = {
  visible: boolean;
  setVisible: (val: boolean) => void;
  message: string;
};

const ErrorModal = ({ visible, setVisible, message }: Props) => (
  <CustomModal visible={visible} duration={500}>
    <View>
      <DisplayText textStyle={styles.heading}>
        <ErrorIcon />
        {' '}
        Error!
      </DisplayText>
      <DisplayText textStyle={styles.errorHeading}>{message}</DisplayText>
      <Button
        title="Close"
        onPress={() => setVisible(false)}
        btnContainer={[styles.error]}
      />
    </View>
  </CustomModal>
);

export default ErrorModal;
