import React from 'react';
import { ActivityIndicator, StyleSheet, View } from 'react-native';

import { Assets } from '@/asset/index';
import { colors } from '@/common/styles/colors';
import { pixelSizeHorizontal, widthPixel } from '@/common/utilities/normalize';

import { ListSeparator } from '../ListSeparator';
import { PayforceButton } from '../PayforceButton';
import PayforceModal from '../PayforceModals/Modals/PayforceModal';
import { PayforceText } from '../PayforceText/PayforceText';

type BaseProps = {
  onClose?: () => void;
  title: string | React.ReactNode;
  description: string | React.ReactNode;
  isVisible: boolean;
  firstButtonLabel?: string;
  firstButtonColor?: string;
  firstButtonPress?: () => void;
  secondButtonLabel?: string;
  secondButtonColor?: string;
  secondButtonPress?: () => void;
  isLoading?: boolean;
  icon?: React.JSX.Element;
  isSuccess?: boolean;
};

const styles = StyleSheet.create({
  button: {
    backgroundColor: colors.white,
    borderColor: colors.palette.FMBlack[300],
    borderWidth: 1,
  },
  container: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
    width: '100%',
  },
  contentContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: pixelSizeHorizontal(20),
  },
  modalTitleVariation: {
    justifyContent: 'center',
    textAlign: 'center',
  },
  spacing: {
    bottom: 2,
    position: 'absolute',
  },
  textAlign: {
    textAlign: 'center',
  },
  width: {
    width: widthPixel(358),
  },

});

const NewModal = ({
  icon,
  ...rest
}: BaseProps): React.JSX.Element => {
  const {
    isSuccess,
    isVisible,
    title,
    onClose = () => { },
    isLoading,
    description,
    firstButtonLabel = 'Proceed',
    firstButtonColor,
    firstButtonPress,
    secondButtonLabel,
    secondButtonPress,
    secondButtonColor,
  } = rest;

  const getDefaultIcon = () => {
    switch (isSuccess) {
      case true:
        return <Assets.SavingsIcons.SuccessTick />;
      default:
        return <Assets.SavingsIcons.Danger />;
    }
  };

  const getIcon = () => {
    switch (typeof icon) {
      case 'undefined':
        return getDefaultIcon();
      default:
        return <>{icon}</>;
    }
  };

  return (
    <PayforceModal
      visible={isVisible}
      duration={500}
      options={{ from: 'bottom' }}
      loanModal
    >

      <View style={styles.container}>
        <View style={styles.contentContainer}>
          {getIcon()}
          <ListSeparator height={15} />
          <PayforceText.Button
            color={colors.palette.FMBlack[900]}
            style={styles.modalTitleVariation}
          >
            {title}
          </PayforceText.Button>
          <ListSeparator height={15} />
          <PayforceText.Subheader style={styles.textAlign} color="form">
            {description}
          </PayforceText.Subheader>
          <ListSeparator height={25} />
          {isLoading
            ? (
              <ActivityIndicator color={colors.palette.FMBlack[500]} />
              )
            : null}
        </View>
        {!isLoading && (
          <View style={styles.spacing}>
            <PayforceButton
              type="small"
              onPress={firstButtonPress}
              color={firstButtonColor || colors.palette.FMBlack[900]}
              style={styles.width}
            >
              <PayforceText.Body1Medium color={colors.white}>
                {firstButtonLabel}
              </PayforceText.Body1Medium>
            </PayforceButton>
            <ListSeparator height={15} />
            {secondButtonLabel && (
              <>
                <PayforceButton
                  type="small"
                  color={secondButtonColor || colors.white}
                  style={[styles.button, styles.width]}
                  onPress={secondButtonPress || onClose}
                >
                  <PayforceText.Body1 color="form">
                    {secondButtonLabel}
                  </PayforceText.Body1>
                </PayforceButton>
                <ListSeparator height={5} />
              </>
            )}
          </View>
        )}
      </View>
    </PayforceModal>
  );
};

export default NewModal;
