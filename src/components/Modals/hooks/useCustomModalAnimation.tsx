import { useEffect, useRef } from 'react';
import { Animated, Easing } from 'react-native';

import { dimensions } from '@/styles/Styles';

type Options = {
  easing?: any;
  useNativeDriver?: boolean;
};

export const useCustomModalAnimation = (
  visible: boolean,
  duration: number,
  options: Options = {},
  from: 'top' | 'bottom' = 'top',
) => {
  const startPointY
    = from === 'top' ? -dimensions.screenHeight : dimensions.screenHeight;
  const transY = useRef(new Animated.Value(startPointY)).current;

  useEffect(() => {
    const animation = Animated.timing(transY, {
      toValue: visible ? 0 : startPointY,
      duration,
      easing: options.easing || Easing.inOut(Easing.ease),
      useNativeDriver: options.useNativeDriver || true,
    });

    animation.start();

    return () => {
      animation.stop();
    };
  }, [visible, duration, options.easing, options.useNativeDriver]);

  const generateBackgroundOpacity = () => {
    const startPointOpacityY
      = from === 'top' ? -dimensions.screenHeight : dimensions.screenHeight;
    const inputRange = startPointOpacityY >= 0 ? [0, startPointOpacityY] : [startPointOpacityY, 0];
    const outputRange = startPointOpacityY >= 0 ? [0.8, 0] : [0, 0.8];

    return transY.interpolate({
      inputRange,
      outputRange,
      extrapolate: 'clamp',
    });
  };

  return { transY, generateBackgroundOpacity };
};
