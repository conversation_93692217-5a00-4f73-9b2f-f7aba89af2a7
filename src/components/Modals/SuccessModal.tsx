import React from 'react';
import { View } from 'react-native';

import SuccessIcon from '@/assets/svg/SuccessIcon.svg';
import Button from '@/utils/Button/Button';
import { DisplayText } from '@/utils/DisplayText';

import CustomModal from './Modal';
import { styles } from './style';

type Props = {
  setVisible: () => void;
  message: string;
  visible: boolean;
};

const SuccessModal = ({ message, visible, setVisible }: Props) => (
  <CustomModal visible={visible} duration={500}>
    <View>
      <View style={styles.top}>
        <SuccessIcon />
        <DisplayText textStyle={styles.heading}>Request sent!</DisplayText>
      </View>
      <DisplayText textStyle={styles.subHeading}>{message}</DisplayText>
      <Button title="Close" onPress={setVisible} />
    </View>
  </CustomModal>
);

export default SuccessModal;
