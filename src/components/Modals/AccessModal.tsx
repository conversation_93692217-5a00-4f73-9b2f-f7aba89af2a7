import React from 'react';
import { ActivityIndicator, StyleSheet, View } from 'react-native';

import { Assets } from '@/asset/index';
import { constants } from '@/common/constants';
import { colors } from '@/common/styles/colors';
import { normalize } from '@/common/utilities/normalize';
import { ListSeparator } from '@/components/ListSeparator';
import { PayforceButton } from '@/components/PayforceButton';
import PayforceModal from '@/components/PayforceModals/Modals/PayforceModal';
import { PayforceText } from '@/components/PayforceText/PayforceText';

type CustomAccessModalProps<T> = {
  onClose?: () => void;
  onProceed: () => void;
  title: string | React.ReactNode;
  description: string | React.ReactNode;
  isVisible?: boolean;
  firstButtonLabel?: string | React.ReactNode;
  secondButtonLabel?: string | React.ReactNode;
  onProceedButtonColor?: string;
  isLoading?: boolean;
  secondButton?: React.ReactNode;
  icon?: React.JSX.Element | T;
  isCancel?: boolean;
  isFeedback?: boolean;
  width?: number;
  height?: number;
  type?: string;
  isSuccess?: boolean;
};

const styles = StyleSheet.create({
  accessContainer: {
    alignItems: 'flex-start',
    justifyContent: 'center',
    marginTop: normalize(40),
  },
  accessInfo: {
    alignContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.palette.FMGreen[50],
    borderRadius: 4,
    marginVertical: normalize(20),
    padding: normalize(12),
  },
  button: {
    backgroundColor: colors.white,
    borderColor: colors.palette.FMBlack[300],
  },
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  secondButtonLabelWidth: {
    borderWidth: 0,
  },
  textAlign: {
    textAlign: 'center',
  },
});

const CustomAccessModal = <T extends React.JSX.Element>({
  icon,
  ...rest
}: CustomAccessModalProps<T>): React.JSX.Element => {
  const {
    isSuccess,
    isVisible,
    title,
    onClose = () => {},
    isLoading,
    description,
    onProceed,
    onProceedButtonColor,
    firstButtonLabel = '',
    secondButtonLabel,
    secondButton,
    type = '',
  } = rest;

  const getIcon = () => {
    if (!icon) {
      return (
        <>
          {isSuccess
            ? (
              <Assets.SavingsIcons.SuccessTick />
              )
            : (
              <Assets.SavingsIcons.Danger />
              )}
        </>
      );
    }
    return <>{icon}</>;
  };

  const renderAccessItem = (accessType: string) => {
    switch (accessType) {
      case 'access':
        return (
          <View style={styles.accessContainer}>
            <PayforceText.Button color={colors.palette.FMBlack[900]}>
              {title}
            </PayforceText.Button>
            <ListSeparator height={15} />
            <PayforceText.Subheader color="form">
              {description}
            </PayforceText.Subheader>
            <ListSeparator height={25} />
            <PayforceText.Button color={colors.palette.FMBlack[900]}>
              These information will help us make right loan offer to you.
            </PayforceText.Button>
          </View>
        );
      case 'stepper':
        return (
          <View style={styles.container}>
            {getIcon()}
            <ListSeparator height={16} />
            <PayforceText.Subheader style={styles.textAlign} color="form">
              {description}
            </PayforceText.Subheader>
          </View>
        );
      default:
        return '';
    }
  };
  return (
    <PayforceModal
      visible={Boolean(isVisible)}
      duration={500}
      options={{ from: 'top' }}
    >
      {renderAccessItem(type)}
      {constants.IS_IOS && <ListSeparator height={24} />}
      <View style={styles.accessInfo}>
        <Assets.LoansIcons.loanFeaturedIcon />
        <PayforceText.Decorative
          color={colors.palette.FMBlack[800]}
          style={[styles.textAlign, { paddingVertical: normalize(5) }]}
        >
          Your data will be collected and stored securely, using modern
          cryptography (over HTTPS), by FairMoney. Your data will not be shared
          or sold to anyone.
        </PayforceText.Decorative>
      </View>
      {isLoading
        ? (
          <>
            <ActivityIndicator color={colors.palette.FMBlack[500]} />
          </>
          )
        : (
          <>
            {firstButtonLabel && (
              <PayforceButton
                type="small"
                onPress={onProceed}
                color={onProceedButtonColor}
                title={firstButtonLabel as string}
              />
            )}
            <ListSeparator height={15} />
            {secondButtonLabel
              ? (
                <>
                  <PayforceButton
                    type="small"
                    style={[styles.button, styles.secondButtonLabelWidth]}
                    onPress={onClose}
                  >
                    <PayforceText.Body1 color="form">
                      {secondButtonLabel}
                    </PayforceText.Body1>
                  </PayforceButton>
                  <ListSeparator height={5} />
                </>
                )
              : null}
          </>
          )}
      <ListSeparator />
      {secondButton && <>{secondButton}</>}
    </PayforceModal>
  );
};
export default CustomAccessModal;
