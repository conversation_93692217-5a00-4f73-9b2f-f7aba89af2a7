import { BottomSheetModal } from '@gorhom/bottom-sheet';
import React, { useEffect, useRef, useState } from 'react';
import { ActivityIndicator, StyleSheet, TouchableOpacity, View } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { useGetBanks } from 'src/screens/DispenseError/lib/useGetBanks';
import { BankListType } from 'src/screens/Onboarding/@types';

import { colors } from '@/common/styles/colors';
import { heightPixel, pixelSizeHorizontal, pixelSizeVertical } from '@/common/utilities/normalize';
import { PayforceText } from '@/components/PayforceText/PayforceText';

import CustomBottomSheet from '../BottomSheet/BottomSheet';
import PayforceInput from '../PayforceInput';

const INPUT_HEIGHT = 58;
const BORDER_COLOR = colors.palette.FMBlack[100];
const INPUT_BORDER_COLOR = colors.palette.FMBlack[300];

interface Props {
  innerContainerStyles?: any;
  onSelect: (bank: any) => void;
  onClose?: () => void;
  placeholder?: string;
  height?: number;
  label?: string;
  selectedBank?: BankListType;
  borderColor?: string;
}

const BankListModal = ({
  label,
  onSelect,
  placeholder,
  selectedBank,
  height = INPUT_HEIGHT,
  borderColor = INPUT_BORDER_COLOR,
}: Props) => {
  const { allBanks, isGettingAllBanks } = useGetBanks({});
  const [searchText, setSearchText] = useState('');
  const [banks, setBanks] = useState(allBanks);
  const bottomSheetRef = useRef<BottomSheetModal>(null);

  useEffect(() => {
    setBanks(allBanks);
  }, [allBanks]);

  const handleSelection = (bank: any) => {
    bottomSheetRef.current?.close();
    onSelect(bank);
    setSearchText('');
    setBanks(allBanks);
  };

  const filterBanks = (text: string) => {
    setSearchText(text);
    if (text.length > 0) {
      const filteredBanks = allBanks?.filter((bank) =>
        bank.bankName?.toLowerCase().includes(text.toLowerCase()));
      setBanks(filteredBanks);
    } else {
      setBanks(allBanks);
    }
  };

  return (
    <>
      {label && (
        <PayforceText.Body2
          color={colors.palette.FMBlack[400]}
          style={bankStyles.label}
        >
          {label}
        </PayforceText.Body2>
      )}
      <TouchableOpacity
        activeOpacity={0.8}
        style={[bankStyles.input, { height, borderColor }]}
        onPress={() => bottomSheetRef.current?.present()}
      >
        {selectedBank
          ? (
            <PayforceText.Subheader color={colors.palette.FMBlack[800]}>
              {selectedBank.bankName}
            </PayforceText.Subheader>
            )
          : (
            <PayforceText.Subheader color={colors.typography.editTextPlaceholder}>
              {placeholder || 'Select Bank'}
            </PayforceText.Subheader>
            )}
      </TouchableOpacity>
      <CustomBottomSheet
        ref={bottomSheetRef}
        closeModal={() => bottomSheetRef.current?.close()}
        snapBegin="75%"
        snapEnd="75%"
        snapMid="75%"
      >
        <View style={bankStyles.content}>
          <PayforceInput
            placeholder="Search bank..."
            onChangeText={filterBanks}
            value={searchText}
            style={bankStyles.search}
            inputContainerStyles={bankStyles.searchWrapper}
          />
          <ScrollView
            showsVerticalScrollIndicator={false}
            style={bankStyles.container}
          >
            {isGettingAllBanks
              ? (
                <ActivityIndicator
                  size="large"
                  color={colors.palette.FMBlack[500]}
                  style={bankStyles.loading}
                />
                )
              : (
                <>
                  {banks?.map((bank) => (
                    <TouchableOpacity
                      key={bank.id}
                      style={bankStyles.bankItem}
                      onPress={() => handleSelection(bank)}
                    >
                      <PayforceText.Body2 color="label">
                        {bank.bankName}
                      </PayforceText.Body2>
                    </TouchableOpacity>
                  ))}
                </>
                )}
          </ScrollView>
        </View>
      </CustomBottomSheet>
    </>
  );
};

export default BankListModal;

const bankStyles = StyleSheet.create({
  bankItem: {
    borderBottomColor: BORDER_COLOR,
    borderBottomWidth: 1,
    paddingLeft: pixelSizeHorizontal(20),
    paddingVertical: pixelSizeVertical(18),
  },
  container: {
    flex: 1,
    paddingLeft: pixelSizeHorizontal(20),
    paddingTop: pixelSizeVertical(24),
    width: '100%',
  },
  content: {
    flex: 1,
  },
  input: {
    borderColor: BORDER_COLOR,
    borderRadius: 4,
    borderWidth: 1,
    height: heightPixel(50),
    justifyContent: 'center',
    paddingLeft: pixelSizeHorizontal(19),
    width: '100%',
  },
  label: {
    color: colors.palette.FMBlack[800],
    fontWeight: '500',
    marginBottom: pixelSizeVertical(5),
  },
  loading: {
    marginTop: pixelSizeVertical(20),
  },
  search: {
    alignSelf: 'center',
    width: '90%',
  },
  searchWrapper: {
    height: heightPixel(46),
  },
});
