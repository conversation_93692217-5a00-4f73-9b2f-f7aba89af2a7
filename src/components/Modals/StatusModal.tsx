import React, { useMemo } from 'react';
import { ActivityIndicator, StyleSheet, View, ViewStyle } from 'react-native';

import { Assets } from '@/asset/index';
import { constants } from '@/common/constants';
import { colors } from '@/common/styles/colors';
import { ListSeparator } from '@/components/ListSeparator';
import { PayforceButton } from '@/components/PayforceButton';
import { PayforceText } from '@/components/PayforceText/PayforceText';

import CustomModal from './Modal';

type BaseProps = {
  onClose?: () => void;
  onProceed: () => void;
  title: string | React.ReactNode;
  description: string | React.ReactNode;
  isVisible: boolean;
  firstButtonLabel: string | React.ReactNode;
  secondButtonLabel?: string | React.ReactNode;
  onProceedButtonColor?: string;
  isLoading?: boolean;
  secondButton?: React.ReactNode;
  icon?: React.JSX.Element;
  width?: number;
  isSuccess?: boolean;
};

type IconProp<T> = {
  icon?: T;
};

export type TCustomStatusModalProps<T = undefined> = T extends undefined
  ? BaseProps & IconProp<undefined>
  : BaseProps & IconProp<T>;

const styles = StyleSheet.create({
  button: {
    backgroundColor: colors.white,
    borderColor: colors.palette.FMBlack[300],
    borderWidth: 1,
  },
  container: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  innerContainer: {
    borderRadius: 8,
  },
  modalTitleVariation: {
    justifyContent: 'center',
    textAlign: 'center',
  },
  textAlign: {
    textAlign: 'center',
  },
});

const CustomStatusModal = <T = React.JSX.Element>({
  icon,
  ...rest
}: TCustomStatusModalProps<T>): React.JSX.Element => {
  const {
    isSuccess,
    isVisible,
    title,
    onClose = () => {},
    isLoading,
    description,
    onProceed,
    onProceedButtonColor,
    firstButtonLabel,
    secondButtonLabel,
    secondButton,
    width,
  } = rest;

  const getDefaultIcon = () => {
    switch (isSuccess) {
      case true:
        return <Assets.SavingsIcons.SuccessTick />;
      default:
        return <Assets.SavingsIcons.Danger />;
    }
  };

  const getIcon = () => {
    switch (typeof icon) {
      case 'undefined':
        return getDefaultIcon();
      default:
        return <>{icon}</>;
    }
  };

  const getWidth = () => {
    switch (typeof width) {
      case 'undefined':
        return constants.IS_ANDROID ? 370 : 361;
      default:
        return width;
    }
  };

  const defaultWidth = useMemo(getWidth, [width]);

  const innerContainerStyles: ViewStyle[] = [
    styles.innerContainer,
    { width: defaultWidth },
  ];

  return (
    <CustomModal
      innerContainerStyles={innerContainerStyles}
      visible={isVisible}
      height={488}
      duration={500}
    >
      <View style={styles.container}>
        {getIcon()}
        <ListSeparator height={15} />
        <PayforceText.Button
          color={colors.palette.FMBlack[900]}
          style={styles.modalTitleVariation}
        >
          {title}
        </PayforceText.Button>
        <ListSeparator height={15} />
        <PayforceText.Subheader style={styles.textAlign} color="form">
          {description}
        </PayforceText.Subheader>
        <ListSeparator height={25} />
        {isLoading
          ? (
            <>
              <ActivityIndicator color={colors.palette.FMBlack[500]} />
            </>
            )
          : (
            <>
              <PayforceButton
                type="small"
                onPress={onProceed}
                color={onProceedButtonColor}
              >
                <PayforceText.Body1Medium color={colors.white}>
                  {firstButtonLabel}
                </PayforceText.Body1Medium>
              </PayforceButton>
              <ListSeparator height={15} />
              {secondButtonLabel
                ? (
                  <>
                    <PayforceButton
                      type="small"
                      style={styles.button}
                      onPress={onClose}
                    >
                      <PayforceText.Body1 color="form">
                        {secondButtonLabel}
                      </PayforceText.Body1>
                    </PayforceButton>
                    <ListSeparator height={5} />
                  </>
                  )
                : undefined}

              {secondButton && <>{secondButton}</>}
            </>
            )}
      </View>
    </CustomModal>
  );
};

export default CustomStatusModal;
