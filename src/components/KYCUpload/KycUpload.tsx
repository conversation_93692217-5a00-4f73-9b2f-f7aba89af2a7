import React, { useState } from 'react';
import {
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  View,
} from 'react-native';
import { launchImageLibrary } from 'react-native-image-picker';

import Cancel from '@/assets/svg/cancel.svg';
import Upload from '@/assets/svg/cloud-upload.svg';
import { useGeneralState } from '@/contexts/general';
import { api } from '@/functions/api';
import { allowOnlyNumberAndAlphabets } from '@/functions/functions';
import IdentityDocument from '@/routes/authenticatedRoutes/PayForceKYC/IdentityDocument';
import Button from '@/utils/Button/Button';
import { DisplayText } from '@/utils/DisplayText';
import Input from '@/utils/Input/Input';
import StatusDisplay from '@/utils/StatusDisplay';

import { styles } from './styles';

type Props = {
  setOpenKyc: (open: boolean) => void;
  kycType: string;
  setUploadText: string;
  navigation: any;
  setmessage: (val: string) => void;
  message: string;
};

const defaultState = {
  uri: '',
  fileSize: 0,
  height: 0,
  fileName: '',
};

const KycUpload = (props: Props) => {
  const [loading, setLoading] = React.useState(false);
  const { setOpenKyc, kycType, navigation, message, setmessage } = props;
  const { user } = useGeneralState();
  const [ninValue, setNinValue] = useState<string>('');
  const [ninError] = useState<boolean>(false);
  const [selectedType, setSelectedType] = useState<string>('');
  const [attachments, setAttachments] = React.useState<any>(defaultState);

  const [error, setError] = useState<boolean>(false);
  const [success, setSuccess] = useState<boolean>(false);
  const [idNumberType, setIdNumberType] = useState<string>('');

  const submitKyc = async () => {
    setLoading(true);
    const payload = new FormData();

    let fieldName;
    if (kycType === 'id') {
      fieldName = 'Identity';
    } else if (kycType === 'passport') {
      fieldName = 'Passport';
    } else if (kycType === 'nin') {
      fieldName = 'PrimaryIdDoc';
    } else {
      fieldName = 'EvidenceOfAddress';
    }

    const uri = Platform.OS === 'android' ? attachments.uri : attachments.uri.replace('file://', '');

    payload.append(fieldName, {
      name: attachments.fileName,
      type: attachments.type,
      uri,
    });
    payload.append('AgentId', user.agent_id);

    switch (kycType) {
      case 'id':
        payload.append('IdentityType', selectedType);
        payload.append('IdentityNumber', ninValue);
        break;
      case 'nin':
        payload.append('PrimaryIdentityType', selectedType);
        payload.append('PrimaryIdentityNumber', ninValue);
        break;
      default:
        break;
    }

    await api
      .post(`/on/mobile/agent/UploadKyc`, payload, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        transformRequest: () => payload,
      })
      .then((res) => {
        setLoading(false);
        if (res.data.status_code === 200) {
          setSuccess(true);
          setmessage(
            `${res.data.result.response_message
            }\n Your document will be verified within 24 hours. `,
          );
        } else if (
          res.data.status_code !== 200
          || res.data.status_code === 404
        ) {
          setError(true);
          setmessage(res.data.error_message);
        }
      })
      .catch((err) => {
        setLoading(false);
        setError(true);
        setmessage(err.message);
      });
  };

  const closeStatusDisplay = () => {
    setOpenKyc(false);
    setAttachments(defaultState);
  };

  const imagePickerCallback = (response: any) => {
    if (response.errorMessage) {
      //   displayToast(`An error occur ${response.errorMessage}`);
    } else if (response.didCancel) {
      //   setAttachments(response.assets[0].base64);
    } else {
      //   setAttachment(response.fileName);
      // setAttachments(response.assets[0].uri);
      setAttachments(response.assets[0]);
      // setFilename(response.fileName);
    }
  };

  const imagePickerOptions = {
    quality: 1,
    maxWidth: 650,
    maxHeight: 650,
    mediaType: 'photo',
    // includeBase64: true
  } as any;

  const getImageFromGallery = () => {
    launchImageLibrary(imagePickerOptions, imagePickerCallback);
  };

  const pickdocument = () => {
    getImageFromGallery();
  };

  const renderInput = () => (
    <>
      {['nin', 'id'].includes(kycType) && idNumberType.length > 1 && (
        <Input
          label={`${idNumberType} NUMBER`}
          error={ninError}
          onChangeText={(text) =>
            setNinValue(allowOnlyNumberAndAlphabets(text))}
          value={ninValue}
        />
      )}
    </>
  );

  return (
    <View style={styles.container}>
      <View style={styles.top}>
        <DisplayText textStyle={styles.textStyle}>Upload ID</DisplayText>
        <TouchableOpacity
          onPress={() => {
            setOpenKyc(false);
            setAttachments(defaultState);
          }}
        >
          <Cancel width={25} />
        </TouchableOpacity>
      </View>
      <DisplayText textStyle={styles.text}>
        {kycType === 'id'
        && `Please upload an image of any of your Government IDs - NIN Slip, International Passport, Drivers' License or Voters Card.`}
        {kycType === 'nin'
        && `Please upload an image of any of your Government IDs - NIN Slip, International Passport or Drivers' License.`}
        {kycType === 'passport'
        && `Kindly upload a clear passport photograph of you.`}
        {kycType === 'address'
        && `Kindly upload a clear picture of your Electricity bill, Waste bill or a tenancy receipt`}
      </DisplayText>
      <View style={styles.dashed}>
        {attachments.uri.length > 1
          ? (
            <>
              <View style={[styles.uploadBtn, styles.uploadedFile]}>
                <View style={styles.asterisk}>
                  <DisplayText textStyle={styles.fileText}>{kycType}</DisplayText>
                </View>
                <DisplayText textStyle={styles.filename}>
                  {Platform.OS === 'ios'
                    ? attachments.uri.slice(178)
                    : attachments.uri.slice(67)}
                </DisplayText>
              </View>
              <Button
                title="Remove"
                color="#FF2D55"
                backgroundColor="rgba(255, 45, 85, 0.3)"
                onPress={() => {
                  setAttachments(defaultState);
                }}
              />
            </>
            )
          : (
            <TouchableOpacity onPress={pickdocument} style={styles.uploadBtn}>
              <Upload width={25} />
              <View style={styles.asterisk}>
                <DisplayText textStyle={styles.uploadText}>
                  Upload Image
                </DisplayText>
              </View>
            </TouchableOpacity>
            )}
      </View>
      {kycType === 'id' && (
        <IdentityDocument
          setIdNumberType={setIdNumberType}
          setSelectedType={setSelectedType}
          isNotNin
        />
      )}
      {['nin'].includes(kycType) && (
        <>
          <IdentityDocument
            isNotNin={false}
            setIdNumberType={setIdNumberType}
            setSelectedType={setSelectedType}
          />
        </>
      )}

      {Platform.OS === 'android'
        ? (
            renderInput()
          )
        : (
          <KeyboardAvoidingView
            style={styles.keyboardView}
            behavior="padding"
          >
            {renderInput()}
          </KeyboardAvoidingView>
          )}

      <View style={styles.submitBtn}>
        <Button
          loading={loading}
          disabled={
            attachments.uri.length > 1 && !['nin', 'id'].includes(kycType)
              ? false
              : !(['nin', 'id'].includes(kycType)
              && attachments.uri.length > 1
              && selectedType
              && ninValue.length > 1)
          }
          title="Submit Document"
          onPress={submitKyc}
        />
      </View>
      <StatusDisplay
        setError={setError}
        setSuccess={setSuccess}
        error={error}
        success={success}
        loading={loading}
        message={message}
        navigation={navigation}
        navigateTo="Return"
        close
        // navigationLink="LimitIncrease"
        closeStatusDisplay={closeStatusDisplay}
      />
    </View>
  );
};

export default KycUpload;
