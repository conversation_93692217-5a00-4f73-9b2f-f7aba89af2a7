import { StyleSheet } from 'react-native';

import { normalize } from '@/common/utilities/normalize';

export const styles = StyleSheet.create({
  asterisk: {
    flexDirection: 'row',
  },
  btnContainer: {
    marginVertical: 0,
  },
  container: {
    backgroundColor: 'white',
    bottom: 0,
    flex: 1,
    height: '100%',
    left: 0,
    paddingHorizontal: normalize(25),
    paddingTop: normalize(25),
    position: 'absolute',
    right: 0,
    width: '100%',
    zIndex: 10,
  },
  dashed: {
    marginVertical: normalize(20),
    overflow: 'hidden',
  },
  fileText: {
    color: 'rgba(60, 60, 67, 0.6)',
    textTransform: 'uppercase',
  },
  filename: {
    color: '#000',
    fontSize: normalize(12),
    fontWeight: '500',
    lineHeight: 20,
  },
  keyboardView: {
    width: '100%',
  },
  submitBtn: {
    bottom: 55,
    left: 25,
    position: 'absolute',
    right: 25,
  },
  text: {
    textAlign: 'left',
  },
  textStyle: {
    color: '#000',
    fontSize: normalize(32),
    fontWeight: '400',
    lineHeight: 42,
    textAlign: 'center',
  },
  top: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: normalize(40),
  },
  transform: {
    textTransform: 'capitalize',
  },
  uploadBtn: {
    alignItems: 'center',
    borderColor: 'rgba(60, 60, 67, 0.3)',
    borderStyle: 'dashed',
    borderWidth: 1,
    flexDirection: 'row',
    height: normalize(80),
    marginHorizontal: -2,
    marginTop: normalize(20),
    paddingHorizontal: 2,
    position: 'relative',
  },
  uploadText: {
    color: '#007AFF',
    fontSize: normalize(20),
    fontWeight: '400',
    lineHeight: 32,
    marginLeft: normalize(10),
  },
  uploadedFile: {
    alignItems: 'flex-start',
    flexDirection: 'column',
    paddingVertical: normalize(20),
  },
});
