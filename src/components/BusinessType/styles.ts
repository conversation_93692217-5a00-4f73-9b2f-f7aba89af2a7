import { StyleSheet } from 'react-native';

import { normalize } from '@/common/utilities/normalize';
import { COLORS } from '@/styles/Styles';

export const style = StyleSheet.create({
  active: {
    backgroundColor: 'rgba(44, 162, 202, 0.03)',
    borderColor: COLORS.secondary,
    borderRadius: 12,
    borderWidth: 1,
    elevation: 13,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.09,
    shadowRadius: 24,
  },
  circle: {
    borderColor: COLORS.header,
    borderRadius: 24 / 2,
    borderStyle: 'solid',
    borderWidth: 1.5,
    height: normalize(24),
    width: normalize(24),
  },
  circleActive: {
    backgroundColor: COLORS.secondary,
    borderColor: COLORS.secondary,
  },
  container: {
    alignItems: 'center',
    flexDirection: 'row',
    height: normalize(84),
    justifyContent: 'space-between',
    paddingHorizontal: normalize(10),
  },
  header: {
    color: COLORS.header,
    fontSize: normalize(16),
    fontWeight: '600',
  },
  summary: {
    color: COLORS.header,
    fontWeight: '400',
    lineHeight: 24,
  },
});
