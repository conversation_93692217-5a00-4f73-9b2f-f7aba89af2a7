import React from 'react';
import { TouchableOpacity, View } from 'react-native';

import { DisplayText } from '@/utils/DisplayText';

import { style } from './styles';

type Props = {
  businessType: string;
  summary: string;
  activeItem: number;
  id: number;
  setActiveItem: (val: number) => void;
};

const BusinessTypeItem = ({
  businessType,
  summary,
  activeItem,
  id,
  setActiveItem,
}: Props) => (
  <TouchableOpacity
    onPress={() => setActiveItem(id)}
    style={[style.container, activeItem === id && style.active]}
  >
    <View>
      <DisplayText textStyle={style.header}>{businessType}</DisplayText>
      <DisplayText textStyle={style.summary}>{summary}</DisplayText>
    </View>
    <View style={[style.circle, activeItem === id && style.circleActive]} />
  </TouchableOpacity>
);

export default BusinessTypeItem;
