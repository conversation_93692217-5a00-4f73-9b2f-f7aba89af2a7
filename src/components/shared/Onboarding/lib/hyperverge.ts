import { NativeModules } from 'react-native';

const { Hyperkyc } = NativeModules;

export type HyperKycResult = {
  status: 'auto_approved' | 'auto_declined' | 'needs_review' | 'user_cancelled' | 'error';
  message?: string;
  errorCode?: string;
  errorMessage?: string;
};

export const openCameraCheck = (
  setResult: (result: HyperKycResult) => void,
  setLoading: (arg0: boolean) => void,
  bvn_image_url: string,
  liveliness_trans_id: string,
) => {
  const configDictionary: Record<string, string | Record<string, string>> = {};
  const inputsDictionary: Record<string, string> = {};
  inputsDictionary.userImage = bvn_image_url;
  configDictionary.appId = 'cu8nu5';
  configDictionary.appKey = 'uy8i6nmmfyg2lr71p80w';
  configDictionary.workflowId = 'userOnboarding';
  configDictionary.transactionId = liveliness_trans_id;
  configDictionary.inputs = inputsDictionary;

  Hyperkyc?.launch({ ...configDictionary }, (result: HyperKycResult) => {
    setLoading(true);
    setResult(result);
  });
};
