import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { CallingIcon } from 'src/assets';

import { colors } from '@/common/styles/colors';
import { globalStyles } from '@/common/styles/globalStyles';
import { heightPixel } from '@/common/utilities/normalize';

import { PayforceText } from '../PayforceText/PayforceText';

type Props = {
  onPress: () => void;
};

const PayforceCallButton = ({ onPress }: Props) => (
  <TouchableOpacity style={style.container} onPress={onPress}>
    <View style={globalStyles.flexRowContentCenter}>
      <PayforceText.Subheader color="form">
        Receive code via
      </PayforceText.Subheader>
      <TouchableOpacity>
        <PayforceText.Body2 color="warning" style={globalStyles.ml(2)}>
          <CallingIcon />
          {' '}
          Voice call
        </PayforceText.Body2>
      </TouchableOpacity>
    </View>
  </TouchableOpacity>
);

export default PayforceCallButton;

const style = StyleSheet.create({
  container: {
    borderColor: colors.palette.FMBlack[300],
    borderRadius: 4,
    borderWidth: 1,
    height: heightPixel(50),
    justifyContent: 'center',
    width: '100%',
  },
});
