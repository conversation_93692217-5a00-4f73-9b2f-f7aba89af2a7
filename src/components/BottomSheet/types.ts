import { BottomSheetFlatList, BottomSheetFlatListMethods, BottomSheetModal, BottomSheetModalProps, BottomSheetTextInput } from '@gorhom/bottom-sheet';
import { BottomSheetFlatListProps } from '@gorhom/bottom-sheet/lib/typescript/components/bottomSheetScrollable/types';
import { BottomSheetTextInputProps } from '@gorhom/bottom-sheet/lib/typescript/components/bottomSheetTextInput';
import { BottomSheetModalMethods } from '@gorhom/bottom-sheet/lib/typescript/types';
import { ComponentType, RefAttributes } from 'react';

export const TypedBottomSheetFlatlist = BottomSheetFlatList as ComponentType<BottomSheetFlatListProps<any> & RefAttributes<BottomSheetFlatListMethods>>;
export const TypedBottomSheetModal = BottomSheetModal as ComponentType<BottomSheetModalProps & RefAttributes<BottomSheetModalMethods>>;
export const TypeBottomSheetInput = BottomSheetTextInput as ComponentType<BottomSheetTextInputProps & RefAttributes<typeof BottomSheetTextInput>>;
