import { BottomSheetModal } from '@gorhom/bottom-sheet';
import * as React from 'react';

import { capitalize } from '@/common/utilities/stringUtilities';

/**
 * <PERSON>le creating ref for bottom sheet
 * @param name
 * @param defaultValue
 * @returns array
 */

type UseCreateBottomSheetModalReturn = [
  React.MutableRefObject<BottomSheetModal | null>,
  () => void,
  () => void,
];

interface HelperRefDisplayName
  extends React.MutableRefObject<BottomSheetModal | null> {
  displayName?: string;
}

export const useCreateBottomSheetModal = (
  name: string,
  defaultValue?: BottomSheetModal | null,
): UseCreateBottomSheetModalReturn => {
  const refObj = React.useRef<BottomSheetModal>(
    defaultValue ?? null,
  ) as HelperRefDisplayName;

  const openModal = () => refObj.current?.present();
  const closeModal = () => refObj.current?.dismiss();

  refObj.displayName = `${name}ModalRef`;
  openModal.displayName = `open${capitalize(name)}Modal`;
  closeModal.displayName = `close${capitalize(name)}Modal`;

  return [refObj, openModal, closeModal];
};
