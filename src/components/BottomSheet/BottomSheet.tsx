import {
  BottomSheetBackdropProps,
  BottomSheetModal,
  type BottomSheetProps,
} from '@gorhom/bottom-sheet';
import React, { forwardRef, useMemo } from 'react';
import { StyleSheet, View } from 'react-native';
import { useDerivedValue, useSharedValue } from 'react-native-reanimated';
import { FullWindowOverlay } from 'react-native-screens';

import { constants } from '@/common/constants';
import { colors } from '@/common/styles/colors';

import CustomBackdrop from '../Backdrop';
import { TypedBottomSheetModal } from './types';

interface IFilterBottomSheetProps extends BottomSheetProps {
  children: React.ReactNode;
  handleSheetChanges?: (i: number) => void;
  closeModal?: () => void;
  snapBegin?: number | string;
  snapEnd?: number | string;
  backgroundColor?: string;
  snapMid?: number | string;
  showBackdrop?: boolean;
  panToClose?: boolean;
  enableDismissOnClose?: boolean;
  backdropBackgroundColor?: string;
  setBottomsheetPosition?: (val: number) => void;
}

const ContainerComponent = ({ children }: any) => (
  <FullWindowOverlay>{children}</FullWindowOverlay>
);

const CustomBottomSheet = forwardRef<BottomSheetModal, IFilterBottomSheetProps>(
  (
    {
      children,
      handleSheetChanges,
      snapBegin = '25%',
      snapEnd = '65%',
      closeModal = () => {},
      backgroundColor,
      snapMid = '50%',
      showBackdrop = true,
      panToClose = true,
      enableDismissOnClose = true,
      backdropBackgroundColor,
      setBottomsheetPosition,
      handleIndicatorStyle,
      ...rest
    },
    ref,
  ) => {
    const animatedPosition = useSharedValue(0);
    const snapPoints = useMemo(
      () => [`${snapBegin}`, `${snapMid}`, `${snapEnd}`],
      [snapBegin, snapEnd, snapMid],
    );

    useDerivedValue(() => {
      setBottomsheetPosition?.(animatedPosition.value);
    }, [animatedPosition, setBottomsheetPosition]);

    const backdrop = showBackdrop
      ? () => (
        <View
          style={[
            styles.backdropStyle,
            {
              ...StyleSheet.absoluteFillObject,
              backgroundColor: backgroundColor ?? colors.white,
            },
          ]}
        />
        )
      : null;

    const backdropComponent = useMemo(
      () => (props: BottomSheetBackdropProps) =>
        (
          <CustomBackdrop
            backdropBackgroundColor={backdropBackgroundColor}
            closeModal={closeModal}
            {...props}
          />
        ),
      [backdropBackgroundColor, closeModal],
    );

    return (
      <TypedBottomSheetModal
        containerComponent={constants.IS_IOS ? ContainerComponent : undefined}
        animatedPosition={animatedPosition}
        enablePanDownToClose={panToClose}
        handleIndicatorStyle={handleIndicatorStyle}
        ref={ref}
        index={1}
        backdropComponent={backdropComponent}
        snapPoints={snapPoints}
        onChange={handleSheetChanges}
        enableDismissOnClose={enableDismissOnClose}
        backgroundComponent={backdrop}
        {...rest}
      >
        {children}
      </TypedBottomSheetModal>
    );
  },
);

const styles = StyleSheet.create({
  backdropStyle: {
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
  },
});

export default CustomBottomSheet;
