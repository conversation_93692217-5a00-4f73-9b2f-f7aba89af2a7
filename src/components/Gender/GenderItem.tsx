import React from 'react';
import { TouchableOpacity, View } from 'react-native';

import GenderIcon from '@/assets/svg/genderIcon.svg';
import { COLORS } from '@/styles/Styles';
import { DisplayText } from '@/utils/DisplayText';

import { style } from './styles';

type Props = {
  gender: string;
  onPress: any;
  activeGender: string;
};

const GenderItem = ({ onPress, gender, activeGender }: Props) => (
  <TouchableOpacity style={style.container} onPress={onPress}>
    <View style={style.right}>
      <GenderIcon />
      <DisplayText textStyle={style.text} textContainer={style.textContainer}>
        {gender}
      </DisplayText>
    </View>
    <View
      style={[
        style.circle,
        {
          backgroundColor:
              activeGender === gender ? COLORS.secondary : COLORS.white,
        },
      ]}
    />
  </TouchableOpacity>
);

export default GenderItem;
