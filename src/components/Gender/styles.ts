import { StyleSheet } from 'react-native';

import { normalize } from '@/common/utilities/normalize';
import { COLORS } from '@/styles/Styles';

export const style = StyleSheet.create({
  circle: {
    borderColor: '#E8E8E8',
    borderRadius: 16 / 2,
    borderStyle: 'solid',
    borderWidth: 1,
    height: normalize(16),
    width: normalize(16),
  },
  container: {
    alignItems: 'center',
    borderColor: COLORS.border100,
    borderRadius: 8,
    borderWidth: 1,
    flexDirection: 'row',
    height: normalize(56),
    justifyContent: 'space-between',
    paddingHorizontal: normalize(5),
    paddingVertical: normalize(7),
    width: '48%',
  },
  right: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  text: {
    color: COLORS.black,
    marginLeft: normalize(10),
  },
  textContainer: {
    marginRight: normalize(5),
  },
});
