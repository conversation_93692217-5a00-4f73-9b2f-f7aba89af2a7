import React, { ReactNode } from 'react';
import { StyleSheet, View } from 'react-native';

import { colors } from '@/common/styles/colors';
import { globalStyles } from '@/common/styles/globalStyles';

type Props = {
  children: ReactNode;
};

const PayforceGender = ({ children }: Props) => <View style={style.container}>{children}</View>;

export default PayforceGender;

const style = StyleSheet.create({
  container: {
    ...globalStyles.height(58),
    ...globalStyles.flexRowSpaceBetween,
    ...globalStyles.borderWidth(1),
    ...globalStyles.borderColor(colors.palette.FMBlack[300]),
    borderRadius: 8,
    ...globalStyles.width('47%'),
    ...globalStyles.px(10),
    ...globalStyles.alignItemsCenter,
  },
});
