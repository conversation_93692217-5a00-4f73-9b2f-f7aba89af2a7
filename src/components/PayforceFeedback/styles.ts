import { Platform, StyleSheet } from 'react-native';

import { colors } from '@/common/styles/colors';
import { FONT_DMSANS } from '@/common/styles/fonts';
import {
  fontPixel,
  heightPixel,
  pixelSizeHorizontal,
  pixelSizeVertical,
  widthPixel,
} from '@/common/utilities/normalize';

const styles = StyleSheet.create({
  actionBtn: {
    flexDirection: 'row',
  },
  actionBtnText: {
    marginLeft: pixelSizeHorizontal(10),
  },
  button: {
    alignItems: 'center',
    borderRadius: 4,
    height: heightPixel(36),
    justifyContent: 'center',
    padding: 8,
    width: widthPixel(174),
  },
  buttonActive: {
    backgroundColor: colors.palette.FMGreen[500],
  },
  buttons: {
    alignItems: 'center',
    backgroundColor: colors.palette.FMGreen[50],
    borderRadius: 4,
    flexDirection: 'row',
    height: heightPixel(44),
    justifyContent: 'space-between',
    padding: 4,
    width: '100%',
  },
  closeBtn: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginBottom: pixelSizeVertical(4),
  },
  container: {
    flex: 1,
    justifyContent: 'space-between',
    paddingBottom: pixelSizeVertical(30),
    paddingHorizontal: pixelSizeHorizontal(16),
    paddingTop: Platform.OS === 'ios' ? pixelSizeVertical(60) : pixelSizeVertical(20),
  },
  counter: { alignSelf: 'flex-end' },
  handle: {
    backgroundColor: colors.white,
  },
  header: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: pixelSizeVertical(4),
  },
  input: {
    color: colors.palette.FMBlack[800],
    fontFamily: FONT_DMSANS,
    fontSize: fontPixel(14),
    lineHeight: 24,
  },
  logo: {
    height: heightPixel(40),
    width: widthPixel(41),
  },
  modal: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    bottom: 0,
    elevation: 5,
    height: '60%',
    left: 0,
    margin: 0,
    position: 'absolute',
    right: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  sheetClose: {
    alignSelf: 'flex-end',
    padding: 10,
  },
  sheetIcon: {
    height: heightPixel(64),
    width: widthPixel(64),
  },
  sheetbox: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: pixelSizeHorizontal(20),
  },
  star: {
    alignItems: 'center',
    height: heightPixel(32),
    justifyContent: 'center',
    marginRight: pixelSizeHorizontal(10),
    width: widthPixel(32),
  },
  stars: {
    alignItems: 'center',
    borderColor: colors.palette.FMBlack[200],
    borderRadius: 8,
    borderWidth: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    paddingVertical: pixelSizeVertical(8),
  },
  text2: {
    lineHeight: 20,
    textAlign: 'center',
  },
  textarea: {
    backgroundColor: colors.palette.FMBlack[50],
    borderColor: colors.palette.FMBlack[200],
    borderRadius: 4,
    borderWidth: 1,
    paddingHorizontal: pixelSizeHorizontal(20),
    paddingVertical: pixelSizeVertical(10),
  },
  transparentBtn: {
    alignItems: 'center',
    borderColor: colors.palette.FMBlack[300],
    borderRadius: 4,
    borderWidth: 1,
    height: heightPixel(52),
    justifyContent: 'center',
    width: '100%',
  },
});

export default styles;
