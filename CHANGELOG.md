# CHANGELOG

All notable changes for FMBusiness this project will be documented in this file. The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/) and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added

- Implement feature flags (PL-1830)
- Added Offline Loans (PL-1256)
- Added Loan Application Detail Screen For Merchant Lending (PL-1374)
- Added Loan Scheduling Flow for Merchant Lending(PL-1368)
- Added Loan Guarantor Flow for Merchant Lending (PL-1366)
- Added Loan Bank Statement Fetching Flow for Merchant Lending (PL-1364)
- Added Loan Offline Application And Credit Worthiness (PL-1364)
- Added Direct debit link bank flow for Offline Merchant Lending (PL-1255)
- Added Loan aplication Review (PL-1523)
- Added Loan Visiting Agent (PL-1525)
- Added Proposing of another visiting time on Merchant lending (PL-1730)
- Added utility for featureflag integration (PL-1851)

### Changed

### Fixed

- Fixed iOS vertical padding issue for Feedback modal (MBK1-1110)
- Fixed FairLock banner redirect bug (MBK1-1112)

## [v1.13.0] - (25/06/2025)

### Added

- Added hyperververge sdk for liveness verification for android and ios (KARIBU-609)

### Changed

- Refetch saved beneficiaries on delete (MBK1-978)

### Fixed

- Fix App Crash (KARIBU-762)
- Fixed outlet sub-account transaction history filter
- Fixed hyperververge sdk for liveness ios navigation bug (KARIBU-774)

## [v1.12.0] - (02/05/2025)

### Added

- Added firebase crashlytics to app
- Added version bump to 79 on ios
- Added version bump to 78 on ios


### Changed

- Updating build version and number
- Commented out android build pr workflow
- Debuged notification service signing
- Fastlane file syntax cleanup
- Redesigned transaction history page to include outlets selection (MBK1-921)
- Enable savings transaction history filter and UX changes (MBK1-931)
- Removed outlet filter options on transaction history page

### Fixed

- Ios Compilation issues fix
- Cleanup ci delivery pipeline
- Fixed transaction history UI issues on IOS
- Changed autosave settings endpoint and removed cache settings (MBK1-1058)

## [v1.11.0] - (29/04/2025)

### Added

- Added new design for savings withdrawal flow
- Implemented continuous delivery pipeline for iOS (FC-31827)

### Changed

- Change Sardine transaction status text (MBK1-1001)

### Fixed

- Fixed next schedule date in savings being blank (MBK1-920)
- Fixed issue with cancel button on autosave savings modal (MBK1-1018)
- Fixed issue where successful transactions incorrectly redirected users to the "Enter PIN" screen instead of completing the flow (MBK1-981)
- Fixed modal issue with Feedback system (MBK1-658)
- Fixed 'Home' button not responsive on the feedback system (MBK1-658)



## [v1.9.4] - (10/04/2025)

### Added

- Added sender's name to tranfer receipt (MBK1-947)
- Display outlet's account number on outlet details page (MBK1-949)
- Added close button to DateRangeSelector component (MBK1-922)
- Sardine: Transaction notification modal on transaction status (MBK1-926)
- Transaction history display on rejected transactions (MBK1-929)
- Transaction history display on pending transactions (MBK1-927)
- Installed firebase crashlytics (MBK1-944)
- Implemented Feedback system (MBK1-658)
- Added Transaction limit information to transfer page (MBK1-870)

### Changed

- Change outlet behaviour on main account drop down (MBK1-952)
- Changed 'Payforce' text to 'Fairmoney' - (MBK1-946)
- Removed Feedback feature

### Fixed

- Navigating back to home screen from fairsave with the onscreen back button takes you to smartsave settings page instead (MBK1-959)
- Replace input with password input on login (KARIBU-549)
- Show the correct saved beneficiaries for bill services (MBK1-955)
- Autosave disable toggle switch not working (MBK1-918)
- Countdown timer freeze (MBK1-956)
- Make use of `modifiedAt` value for smartsave settings (MBK1-941)
- Edidted color component
- Edied client hooks to add a new one for merchant loans
- Change Loan Visiting Agent ID to Username (PL-1773)
- Visitation Button Message Logic (PL-1774)
## [v1.9.3] - (22/02/2025)

### Added

- AppTracking transparency permission on iOS (MBK1-930)
- Design changes to create smartsave flow (MBK1-919)

### Changed

- Edit Smartsmave (MBK1-905)

### Fixed

- [Autoswipe] - OTP Resend Button Not working (MBK1-896)
- [Autoswipe] - Error Message not return when invalid otp is entered (MBK1-895)
- [Autoswipe] - OTP Countdown is missing (MBK1-897)
- Updated in-app banner design and change endpoint used to fetch banners (MKB1-847)
- Added Brother to the Next of Kin list (MBK1-892)
- Modify Sponsorcode check to be optional and also add button to verify sponsor (KARIBU-575)
- Fix Slow Rendering of Fairsave Dashboard (MBK1-914)
- Fix Autosave wallet balance showing "Invalid Amount" (MBK1-932)
- Fix broken loan navigation from transfer success modal (MBK1-898)
- Fix keyboard issue on contact us section (MBK1-901)
- Edit the texts for savings per transaction on the summary page and success message on smartsave (MBK1-937)
- Added extra check to bank transfer downtime error message (MBK1-778)
- Fix sponsor code validation (KARIBU-574)
- Fix Aggregator sponsor code validation general issue (KARIBU-577)

## [v1.9.2] - (19/01/2025)

### Added

- Direct Debit Phase 2 (Repeat User View/Create Mandate) (PL-1069)

### Changed

-

### Fixed

- Outlet not displaying on main account drop down (MBK1-890)
- Outlet does not display immediately (MBK1-891)
- Selfie not submitting large image (KARIBU-565)
- Update LGA when select state (KARIBU-568)

## [v1.9.1] - (20/12/2024)

### Added

- Direct Debit Phase 1 (PL-1071)
- Created a Bank List Modal for selecting bank

### Changed

- Refactor the useTransaction to chunks (NO-TICKET)
- Disabled transaction filter options in FairSave transaction history page (MBK1-876)
- Refactor settings page to new design (MBK1-873)
- Change 'Default' from Claim logged to Awaiting settlement (MBK1-888)

### Fixed

- Fixed Smart save settings displaying "undefined" text (MBK2-532)
- Resolve KYC General Upload issues fix (KARIBU-523)
- Updated autoswipe with Bank List Modal and updated bank list (MBK1-869)
- Refetch FairSave wallet balance on screen focus (MBK2-538)

## [v1.9.0] - (02/12/2024)

### Added

- Homepage Revamp (MBK1-745)
- Add Debugging Tools for Engineers on FairMoney Business App and Docs to help onboard new engineers ASAP (MBK1-839)

### Changed

- Fetched filters options instead of hardcoded, for FairSave transaction history (MBK2-397)
- Reduce frequency of API calls (MBK1-858)
- Change "Send to other bank" text to "Bank Transfer" for bank transfers (MBK1-857)
- Change "NoSettlement" text color to make it readable (MBK1-862)
- Changed `TransactionChannel` for dispense error to `appPF-mobile` (MBK1-865)
- Changes to Home page redesign (MBK1-863)

### Fixed

- customer service integration (ios) (MBK1-740)
- DR - Users cannot add team members (MBK1-750)
- CHANGELOG.md GitHub action not working (NO-TICKET)
- Display fairlock success bottomsheet after saving
- New Features carousel not working on Android (MBK1-766)
- Fix Dispense Error Claim flow (MBK1-403)
- Added space between words in FairSave withdrawal success modal (MBK2-450)
- [BILL PAYMENT] Pasting in phone number field changes metre number (MBK1-836)
- Show PIN reset backend error message instead of hardcoded message (KARIBU-471)
- Transfer to saved beneficiary not working (MBK1-856)
- Fixed date format in transaction statement download (MBK1-849)
- Removed search bar from transaction screen (MBK1-848)
- Validate bank account number ONLY when 10 digits is entered (MBK1-859)
- Fixed Transaction PIN Issue (NO-TICKET)
- Fixed loan banner not routing to given loan offers (PL-1070)
- Fixed GetLoanBanner call been made by removing the API Call

## [v1.8.0] - (05/11/2024)

### Added

- Add Semantic Changelog to the whole project
- Implement Not Qualified Loan BottomSheet (PL-1043)
- KYC Status Updating Flow (KARIBU-413)

### Changed

- Change the IP Address package from `react-native-network-info` to `react-native-device-info` (MBK1-806)
- Rework dispense error claim flow (MBK1-403)
- New feature & stamp duty notice (MBK1-82)

### Fixed

- Resolved bug with Carousel banner
- Resolved bug with NOK issues
- Characters too short for username input (MBK1-766)
- Fix repayment alert modal button (PL-1014)
- KYC Status fix (KARIBU-413)
- fix custom amount bottom sheet (PL-1015)


## [Unreleased]
- FMBE-276: Added android build validation and fixed CI build issues.