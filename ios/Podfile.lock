PODS:
  - appcenter-analytics (4.4.5):
    - AppCenter/Analytics (~> 4.0)
    - AppCenterReactNativeShared (~> 4.0)
    - React-Core
  - appcenter-core (4.4.5):
    - AppCenterReactNativeShared (~> 4.0)
    - React-Core
  - appcenter-crashes (4.4.5):
    - AppCenter/Crashes (~> 4.0)
    - AppCenterReactNativeShared (~> 4.0)
    - React-Core
  - AppCenter/Analytics (4.4.3):
    - AppCenter/Core
  - AppCenter/Core (4.4.3)
  - AppCenter/Crashes (4.4.3):
    - AppCenter/Core
  - AppCenterReactNativeShared (4.4.5):
    - AppCenter/Core (= 4.4.3)
  - BEMCheckBox (1.4.1)
  - boost (1.83.0)
  - BVLinearGradient (2.8.3):
    - React-Core
  - CleverTap-iOS-SDK (6.2.1):
    - SDWebImage (~> 5.11)
  - clevertap-react-native (2.2.1):
    - CleverTap-iOS-SDK (= 6.2.1)
    - React-Core
  - CredoAppBehavioral (3.4.0)
  - CredoAppCalendarEvents (3.0.1)
  - CredoAppCalendarReminders (3.0.1)
  - CredoAppContacts (3.1.0)
  - CredoAppCore (3.7.0)
  - CredoAppIovation (3.1.0)
  - CredoAppMedia (3.1.0)
  - customerio-reactnative (3.9.0):
    - customerio-reactnative/nopush (= 3.9.0)
    - CustomerIO/MessagingInApp (= 2.14.1)
    - CustomerIO/Tracking (= 2.14.1)
    - React-Core
  - customerio-reactnative/apn (3.9.0):
    - CustomerIO/MessagingInApp (= 2.14.1)
    - CustomerIO/MessagingPushAPN (= 2.14.1)
    - CustomerIO/Tracking (= 2.14.1)
    - React-Core
  - customerio-reactnative/nopush (3.9.0):
    - CustomerIO/MessagingInApp (= 2.14.1)
    - CustomerIO/MessagingPush (= 2.14.1)
    - CustomerIO/Tracking (= 2.14.1)
    - React-Core
  - CustomerIO/MessagingInApp (2.14.1):
    - CustomerIOMessagingInApp (= 2.14.1)
  - CustomerIO/MessagingPush (2.14.1):
    - CustomerIOMessagingPush (= 2.14.1)
  - CustomerIO/MessagingPushAPN (2.14.1):
    - CustomerIOMessagingPushAPN (= 2.14.1)
  - CustomerIO/Tracking (2.14.1):
    - CustomerIOTracking (= 2.14.1)
  - CustomerIOCommon (2.14.1)
  - CustomerIOMessagingInApp (2.14.1):
    - CustomerIOTracking (= 2.14.1)
  - CustomerIOMessagingPush (2.14.1):
    - CustomerIOTracking (= 2.14.1)
  - CustomerIOMessagingPushAPN (2.14.1):
    - CustomerIOMessagingPush (= 2.14.1)
  - CustomerIOTracking (2.14.1):
    - CustomerIOCommon (= 2.14.1)
  - DatadogCore (2.25.0):
    - DatadogInternal (= 2.25.0)
  - DatadogCrashReporting (2.25.0):
    - DatadogInternal (= 2.25.0)
    - PLCrashReporter (~> 1.12.0)
  - DatadogInternal (2.25.0)
  - DatadogLogs (2.25.0):
    - DatadogInternal (= 2.25.0)
  - DatadogRUM (2.25.0):
    - DatadogInternal (= 2.25.0)
  - DatadogSDKReactNative (2.7.0):
    - DatadogCore (~> 2.25.0)
    - DatadogCrashReporting (~> 2.25.0)
    - DatadogLogs (~> 2.25.0)
    - DatadogRUM (~> 2.25.0)
    - DatadogTrace (~> 2.25.0)
    - DatadogWebViewTracking (~> 2.25.0)
    - React-Core
  - DatadogTrace (2.25.0):
    - DatadogInternal (= 2.25.0)
    - OpenTelemetrySwiftApi (= 1.13.1)
  - DatadogWebViewTracking (2.25.0):
    - DatadogInternal (= 2.25.0)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.74.5)
  - Firebase/Analytics (10.29.0):
    - Firebase/Core
  - Firebase/Core (10.29.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.29.0)
  - Firebase/CoreOnly (10.29.0):
    - FirebaseCore (= 10.29.0)
  - Firebase/Crashlytics (10.29.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 10.29.0)
  - Firebase/Messaging (10.29.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.29.0)
  - FirebaseAnalytics (10.29.0):
    - FirebaseAnalytics/AdIdSupport (= 10.29.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.29.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.29.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseCore (10.29.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreExtension (10.29.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseCrashlytics (10.29.0):
    - FirebaseCore (~> 10.5)
    - FirebaseInstallations (~> 10.0)
    - FirebaseRemoteConfigInterop (~> 10.23)
    - FirebaseSessions (~> 10.5)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (~> 2.1)
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.29.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.3)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseRemoteConfigInterop (10.29.0)
  - FirebaseSessions (10.29.0):
    - FirebaseCore (~> 10.5)
    - FirebaseCoreExtension (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.13)
    - GoogleUtilities/UserDefaults (~> 7.13)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesSwift (~> 2.1)
  - fmt (9.1.0)
  - FraudForce (5.4.1)
  - glog (0.3.5)
  - GoogleAppMeasurement (10.29.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.29.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.29.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.29.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.29.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities (7.13.3):
    - GoogleUtilities/AppDelegateSwizzler (= 7.13.3)
    - GoogleUtilities/Environment (= 7.13.3)
    - GoogleUtilities/ISASwizzler (= 7.13.3)
    - GoogleUtilities/Logger (= 7.13.3)
    - GoogleUtilities/MethodSwizzler (= 7.13.3)
    - GoogleUtilities/Network (= 7.13.3)
    - "GoogleUtilities/NSData+zlib (= 7.13.3)"
    - GoogleUtilities/Privacy (= 7.13.3)
    - GoogleUtilities/Reachability (= 7.13.3)
    - GoogleUtilities/SwizzlerTestHelpers (= 7.13.3)
    - GoogleUtilities/UserDefaults (= 7.13.3)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/ISASwizzler (7.13.3):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/SwizzlerTestHelpers (7.13.3):
    - GoogleUtilities/MethodSwizzler
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - hermes-engine (0.74.5):
    - hermes-engine/Pre-built (= 0.74.5)
  - hermes-engine/Pre-built (0.74.5)
  - jail-monkey (2.8.0):
    - React-Core
  - lottie-ios (4.4.1)
  - lottie-react-native (6.7.2):
    - DoubleConversion
    - glog
    - hermes-engine
    - lottie-ios (= 4.4.1)
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - MetaMapSDK (3.18.2)
  - MMKV (2.2.2):
    - MMKVCore (~> 2.2.2)
  - MMKVCore (2.2.2)
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - OpenTelemetrySwiftApi (1.13.1)
  - Permission-Camera (3.10.1):
    - RNPermissions
  - Permission-Contacts (3.10.1):
    - RNPermissions
  - Permission-FaceID (3.10.1):
    - RNPermissions
  - Permission-LocationAccuracy (3.10.1):
    - RNPermissions
  - Permission-LocationAlways (3.10.1):
    - RNPermissions
  - Permission-LocationWhenInUse (3.10.1):
    - RNPermissions
  - Permission-Notifications (3.10.1):
    - RNPermissions
  - Permission-PhotoLibrary (3.10.1):
    - RNPermissions
  - PLCrashReporter (1.12.0)
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - RCT-Folly (2024.01.01.00):
    - boost
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Default (= 2024.01.01.00)
  - RCT-Folly/Default (2024.01.01.00):
    - boost
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
  - RCT-Folly/Fabric (2024.01.01.00):
    - boost
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
  - RCTDeprecation (0.74.5)
  - RCTRequired (0.74.5)
  - RCTTypeSafety (0.74.5):
    - FBLazyVector (= 0.74.5)
    - RCTRequired (= 0.74.5)
    - React-Core (= 0.74.5)
  - React (0.74.5):
    - React-Core (= 0.74.5)
    - React-Core/DevSupport (= 0.74.5)
    - React-Core/RCTWebSocket (= 0.74.5)
    - React-RCTActionSheet (= 0.74.5)
    - React-RCTAnimation (= 0.74.5)
    - React-RCTBlob (= 0.74.5)
    - React-RCTImage (= 0.74.5)
    - React-RCTLinking (= 0.74.5)
    - React-RCTNetwork (= 0.74.5)
    - React-RCTSettings (= 0.74.5)
    - React-RCTText (= 0.74.5)
    - React-RCTVibration (= 0.74.5)
  - React-callinvoker (0.74.5)
  - React-Codegen (0.74.5):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default (= 0.74.5)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/CoreModulesHeaders (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/Default (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/DevSupport (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default (= 0.74.5)
    - React-Core/RCTWebSocket (= 0.74.5)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTBlobHeaders (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTImageHeaders (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTTextHeaders (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTWebSocket (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default (= 0.74.5)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-CoreModules (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety (= 0.74.5)
    - React-Codegen
    - React-Core/CoreModulesHeaders (= 0.74.5)
    - React-jsi (= 0.74.5)
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTImage (= 0.74.5)
    - ReactCommon
    - SocketRocket (= 0.7.0)
  - React-cxxreact (0.74.5):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker (= 0.74.5)
    - React-debug (= 0.74.5)
    - React-jsi (= 0.74.5)
    - React-jsinspector
    - React-logger (= 0.74.5)
    - React-perflogger (= 0.74.5)
    - React-runtimeexecutor (= 0.74.5)
  - React-debug (0.74.5)
  - React-Fabric (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.74.5)
    - React-Fabric/attributedstring (= 0.74.5)
    - React-Fabric/componentregistry (= 0.74.5)
    - React-Fabric/componentregistrynative (= 0.74.5)
    - React-Fabric/components (= 0.74.5)
    - React-Fabric/core (= 0.74.5)
    - React-Fabric/imagemanager (= 0.74.5)
    - React-Fabric/leakchecker (= 0.74.5)
    - React-Fabric/mounting (= 0.74.5)
    - React-Fabric/scheduler (= 0.74.5)
    - React-Fabric/telemetry (= 0.74.5)
    - React-Fabric/templateprocessor (= 0.74.5)
    - React-Fabric/textlayoutmanager (= 0.74.5)
    - React-Fabric/uimanager (= 0.74.5)
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/animations (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/attributedstring (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistry (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistrynative (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/inputaccessory (= 0.74.5)
    - React-Fabric/components/legacyviewmanagerinterop (= 0.74.5)
    - React-Fabric/components/modal (= 0.74.5)
    - React-Fabric/components/rncore (= 0.74.5)
    - React-Fabric/components/root (= 0.74.5)
    - React-Fabric/components/safeareaview (= 0.74.5)
    - React-Fabric/components/scrollview (= 0.74.5)
    - React-Fabric/components/text (= 0.74.5)
    - React-Fabric/components/textinput (= 0.74.5)
    - React-Fabric/components/unimplementedview (= 0.74.5)
    - React-Fabric/components/view (= 0.74.5)
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/inputaccessory (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/legacyviewmanagerinterop (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/modal (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/rncore (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/root (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/safeareaview (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/scrollview (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/text (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/textinput (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/unimplementedview (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/view (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric/core (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/imagemanager (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/leakchecker (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/mounting (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/scheduler (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/telemetry (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/templateprocessor (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/textlayoutmanager (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-FabricImage (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired (= 0.74.5)
    - RCTTypeSafety (= 0.74.5)
    - React-Fabric
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor (= 0.74.5)
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - Yoga
  - React-featureflags (0.74.5)
  - React-graphics (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-Core/Default (= 0.74.5)
    - React-utils
  - React-hermes (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-cxxreact (= 0.74.5)
    - React-jsi
    - React-jsiexecutor (= 0.74.5)
    - React-jsinspector
    - React-perflogger (= 0.74.5)
    - React-runtimeexecutor
  - React-ImageManager (0.74.5):
    - glog
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
  - React-jserrorhandler (0.74.5):
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-debug
    - React-jsi
    - React-Mapbuffer
  - React-jsi (0.74.5):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
  - React-jsiexecutor (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-cxxreact (= 0.74.5)
    - React-jsi (= 0.74.5)
    - React-jsinspector
    - React-perflogger (= 0.74.5)
  - React-jsinspector (0.74.5):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-featureflags
    - React-jsi
    - React-runtimeexecutor (= 0.74.5)
  - React-jsitracing (0.74.5):
    - React-jsi
  - React-logger (0.74.5):
    - glog
  - React-Mapbuffer (0.74.5):
    - glog
    - React-debug
  - react-native-cameraroll (4.1.2):
    - React-Core
  - react-native-config (1.5.3):
    - react-native-config/App (= 1.5.3)
  - react-native-config/App (1.5.3):
    - React-Core
  - react-native-contacts (7.0.8):
    - React-Core
  - react-native-document-picker (9.3.1):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-geolocation (3.4.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-geolocation-service (5.3.1):
    - React
  - react-native-image-picker (4.10.3):
    - React-Core
  - react-native-image-resizer (3.0.11):
    - React-Core
  - react-native-images-to-pdf (0.2.1):
    - React-Core
  - react-native-metaMap-sdk (5.1.6):
    - MetaMapSDK (~> 3.18.0)
    - React
  - react-native-mmkv (2.12.2):
    - DoubleConversion
    - glog
    - hermes-engine
    - MMKV (>= 1.3.3)
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-netinfo (11.4.1):
    - React-Core
  - react-native-restart (0.0.27):
    - React-Core
  - react-native-safe-area-context (4.11.0):
    - React-Core
  - react-native-slider (4.5.3):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-tracking-transparency (0.1.2):
    - React
  - react-native-view-shot (3.8.0):
    - React-Core
  - react-native-webview (13.12.2):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-nativeconfig (0.74.5)
  - React-NativeModulesApple (0.74.5):
    - glog
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-jsinspector
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.74.5)
  - React-RCTActionSheet (0.74.5):
    - React-Core/RCTActionSheetHeaders (= 0.74.5)
  - React-RCTAnimation (0.74.5):
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTAnimationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTAppDelegate (0.74.5):
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-CoreModules
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-nativeconfig
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-RCTNetwork
    - React-rendererdebug
    - React-RuntimeApple
    - React-RuntimeCore
    - React-RuntimeHermes
    - React-runtimescheduler
    - React-utils
    - ReactCommon
  - React-RCTBlob (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-Codegen
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTFabric (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsinspector
    - React-nativeconfig
    - React-RCTImage
    - React-RCTText
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - Yoga
  - React-RCTImage (0.74.5):
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTLinking (0.74.5):
    - React-Codegen
    - React-Core/RCTLinkingHeaders (= 0.74.5)
    - React-jsi (= 0.74.5)
    - React-NativeModulesApple
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.74.5)
  - React-RCTNetwork (0.74.5):
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTNetworkHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTSettings (0.74.5):
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTText (0.74.5):
    - React-Core/RCTTextHeaders (= 0.74.5)
    - Yoga
  - React-RCTVibration (0.74.5):
    - RCT-Folly (= 2024.01.01.00)
    - React-Codegen
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-rendererdebug (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - RCT-Folly (= 2024.01.01.00)
    - React-debug
  - React-rncore (0.74.5)
  - React-RuntimeApple (0.74.5):
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-callinvoker
    - React-Core/Default
    - React-CoreModules
    - React-cxxreact
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-Mapbuffer
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-RuntimeHermes
    - React-utils
  - React-RuntimeCore (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-cxxreact
    - React-featureflags
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
  - React-runtimeexecutor (0.74.5):
    - React-jsi (= 0.74.5)
  - React-RuntimeHermes (0.74.5):
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-jsitracing
    - React-nativeconfig
    - React-RuntimeCore
    - React-utils
  - React-runtimescheduler (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-jsi
    - React-rendererdebug
    - React-runtimeexecutor
    - React-utils
  - React-utils (0.74.5):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-debug
    - React-jsi (= 0.74.5)
  - ReactCommon (0.74.5):
    - ReactCommon/turbomodule (= 0.74.5)
  - ReactCommon/turbomodule (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker (= 0.74.5)
    - React-cxxreact (= 0.74.5)
    - React-jsi (= 0.74.5)
    - React-logger (= 0.74.5)
    - React-perflogger (= 0.74.5)
    - ReactCommon/turbomodule/bridging (= 0.74.5)
    - ReactCommon/turbomodule/core (= 0.74.5)
  - ReactCommon/turbomodule/bridging (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker (= 0.74.5)
    - React-cxxreact (= 0.74.5)
    - React-jsi (= 0.74.5)
    - React-logger (= 0.74.5)
    - React-perflogger (= 0.74.5)
  - ReactCommon/turbomodule/core (0.74.5):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker (= 0.74.5)
    - React-cxxreact (= 0.74.5)
    - React-debug (= 0.74.5)
    - React-jsi (= 0.74.5)
    - React-logger (= 0.74.5)
    - React-perflogger (= 0.74.5)
    - React-utils (= 0.74.5)
  - RNBootSplash (4.7.5):
    - React-Core
  - RNCAsyncStorage (1.24.0):
    - React-Core
  - RNCCheckbox (0.5.17):
    - BEMCheckBox (~> 1.4)
    - React-Core
  - RNCClipboard (1.14.2):
    - React-Core
  - RNCPicker (2.8.1):
    - React-Core
  - RNCPushNotificationIOS (1.11.0):
    - React-Core
  - RNCredoappsdk (5.4.0):
    - CredoAppCore (= 3.7.0)
    - React
  - RNCredoappsdkBehavioral (5.1.0):
    - CredoAppBehavioral (= 3.4.0)
    - CredoAppCore
    - React
  - RNCredoappsdkIosCalendarEvents (5.1.0):
    - CredoAppCalendarEvents (= 3.0.1)
    - CredoAppCore
    - React
  - RNCredoappsdkIosCalendarReminders (5.1.0):
    - CredoAppCalendarReminders (= 3.0.1)
    - CredoAppCore
    - React
  - RNCredoappsdkIosContacts (5.2.0):
    - CredoAppContacts (= 3.1.0)
    - CredoAppCore
    - React
  - RNCredoappsdkIosIovation (5.1.0):
    - CredoAppCore
    - CredoAppIovation (= 3.1.0)
    - FraudForce (= 5.4.1)
    - React
  - RNCredoappsdkIosMedia (5.1.0):
    - CredoAppCore
    - CredoAppMedia (= 3.1.0)
    - React
  - RNDateTimePicker (8.2.0):
    - React-Core
  - RNDeviceInfo (8.7.1):
    - React-Core
  - RNFBAnalytics (20.5.0):
    - Firebase/Analytics (= 10.29.0)
    - React-Core
    - RNFBApp
  - RNFBApp (20.5.0):
    - Firebase/CoreOnly (= 10.29.0)
    - React-Core
  - RNFBCrashlytics (20.5.0):
    - Firebase/Crashlytics (= 10.29.0)
    - FirebaseCoreExtension
    - React-Core
    - RNFBApp
  - RNFBMessaging (20.5.0):
    - Firebase/Messaging (= 10.29.0)
    - FirebaseCoreExtension
    - React-Core
    - RNFBApp
  - RNFS (2.20.0):
    - React-Core
  - RNGestureHandler (2.20.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNKeychain (8.1.3):
    - React-Core
  - RNLocalize (3.2.1):
    - React-Core
  - RNPermissions (3.10.1):
    - React-Core
  - RNQrGenerator (1.4.0):
    - React
    - ZXingObjC
  - RNReactNativeHapticFeedback (2.3.3):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNReanimated (3.15.3):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated (= 3.15.3)
    - RNReanimated/worklets (= 3.15.3)
    - Yoga
  - RNReanimated/reanimated (3.15.3):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNReanimated/worklets (3.15.3):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (3.29.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNSha256 (1.4.10):
    - React-Core
  - RNShare (10.2.1):
    - React-Core
  - RNStaticSafeAreaInsets (2.2.0):
    - React-Core
  - RNSVG (12.5.1):
    - React-Core
  - SDWebImage (5.21.0):
    - SDWebImage/Core (= 5.21.0)
  - SDWebImage/Core (5.21.0)
  - seon-react-native-mobile-wrapper (2.0.0):
    - React-Core
    - SeonSDK (= 5.0.0)
  - SeonSDK (5.0.0)
  - SocketRocket (0.7.0)
  - TouchID (4.4.1):
    - React
  - VisionCamera (4.0.5):
    - VisionCamera/Core (= 4.0.5)
    - VisionCamera/React (= 4.0.5)
  - VisionCamera/Core (4.0.5)
  - VisionCamera/React (4.0.5):
    - React-Core
  - Yoga (0.0.0)
  - ZXingObjC (3.6.9):
    - ZXingObjC/All (= 3.6.9)
  - ZXingObjC/All (3.6.9)

DEPENDENCIES:
  - appcenter-analytics (from `../node_modules/appcenter-analytics`)
  - appcenter-core (from `../node_modules/appcenter`)
  - appcenter-crashes (from `../node_modules/appcenter-crashes`)
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - clevertap-react-native (from `../node_modules/clevertap-react-native`)
  - CredoAppBehavioral (~> 3.4.0)
  - CredoAppContacts (~> 3.1.0)
  - CredoAppCore (~> 3.7.0)
  - CredoAppIovation (~> 3.1.0)
  - customerio-reactnative (from `../node_modules/customerio-reactnative`)
  - customerio-reactnative/apn (from `../node_modules/customerio-reactnative`)
  - "DatadogSDKReactNative (from `../node_modules/@datadog/mobile-react-native`)"
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FirebaseCore
  - FirebaseCoreExtension
  - FirebaseCrashlytics
  - FirebaseInstallations
  - FirebaseSessions
  - fmt (from `../node_modules/react-native/third-party-podspecs/fmt.podspec`)
  - FraudForce (~> 5.4.1)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - GoogleDataTransport
  - GoogleUtilities
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - jail-monkey (from `../node_modules/jail-monkey`)
  - lottie-react-native (from `../node_modules/lottie-react-native`)
  - nanopb
  - Permission-Camera (from `../node_modules/react-native-permissions/ios/Camera`)
  - Permission-Contacts (from `../node_modules/react-native-permissions/ios/Contacts`)
  - Permission-FaceID (from `../node_modules/react-native-permissions/ios/FaceID`)
  - Permission-LocationAccuracy (from `../node_modules/react-native-permissions/ios/LocationAccuracy`)
  - Permission-LocationAlways (from `../node_modules/react-native-permissions/ios/LocationAlways`)
  - Permission-LocationWhenInUse (from `../node_modules/react-native-permissions/ios/LocationWhenInUse`)
  - Permission-Notifications (from `../node_modules/react-native-permissions/ios/Notifications`)
  - Permission-PhotoLibrary (from `../node_modules/react-native-permissions/ios/PhotoLibrary`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCT-Folly/Fabric (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTDeprecation (from `../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation`)
  - RCTRequired (from `../node_modules/react-native/Libraries/Required`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-Fabric (from `../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../node_modules/react-native/ReactCommon`)
  - React-featureflags (from `../node_modules/react-native/ReactCommon/react/featureflags`)
  - React-graphics (from `../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-ImageManager (from `../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jserrorhandler (from `../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-jsitracing (from `../node_modules/react-native/ReactCommon/hermes/executor/`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../node_modules/react-native/ReactCommon`)
  - "react-native-cameraroll (from `../node_modules/@react-native-community/cameraroll`)"
  - react-native-config (from `../node_modules/react-native-config`)
  - react-native-contacts (from `../node_modules/react-native-contacts`)
  - react-native-document-picker (from `../node_modules/react-native-document-picker`)
  - "react-native-geolocation (from `../node_modules/@react-native-community/geolocation`)"
  - react-native-geolocation-service (from `../node_modules/react-native-geolocation-service`)
  - react-native-image-picker (from `../node_modules/react-native-image-picker`)
  - "react-native-image-resizer (from `../node_modules/@bam.tech/react-native-image-resizer`)"
  - react-native-images-to-pdf (from `../node_modules/react-native-images-to-pdf`)
  - react-native-metaMap-sdk (from `../node_modules/react-native-metamap-sdk`)
  - react-native-mmkv (from `../node_modules/react-native-mmkv`)
  - "react-native-netinfo (from `../node_modules/@react-native-community/netinfo`)"
  - react-native-restart (from `../node_modules/react-native-restart`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - "react-native-slider (from `../node_modules/@react-native-community/slider`)"
  - react-native-tracking-transparency (from `../node_modules/react-native-tracking-transparency`)
  - react-native-view-shot (from `../node_modules/react-native-view-shot`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-nativeconfig (from `../node_modules/react-native/ReactCommon`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../node_modules/react-native/React`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rendererdebug (from `../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-RuntimeApple (from `../node_modules/react-native/ReactCommon/react/runtime/platform/ios`)
  - React-RuntimeCore (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-RuntimeHermes (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - RNBootSplash (from `../node_modules/react-native-bootsplash`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCCheckbox (from `../node_modules/@react-native-community/checkbox`)"
  - "RNCClipboard (from `../node_modules/@react-native-clipboard/clipboard`)"
  - "RNCPicker (from `../node_modules/@react-native-picker/picker`)"
  - "RNCPushNotificationIOS (from `../node_modules/@react-native-community/push-notification-ios`)"
  - "RNCredoappsdk (from `../node_modules/@credolab/react-core`)"
  - "RNCredoappsdkBehavioral (from `../node_modules/@credolab/react-behavioral`)"
  - "RNCredoappsdkIosCalendarEvents (from `../node_modules/@credolab/react-ios-calendar-events`)"
  - "RNCredoappsdkIosCalendarReminders (from `../node_modules/@credolab/react-ios-calendar-reminders`)"
  - "RNCredoappsdkIosContacts (from `../node_modules/@credolab/react-ios-contact`)"
  - "RNCredoappsdkIosIovation (from `../node_modules/@credolab/react-ios-iovation`)"
  - "RNCredoappsdkIosMedia (from `../node_modules/@credolab/react-ios-media`)"
  - "RNDateTimePicker (from `../node_modules/@react-native-community/datetimepicker`)"
  - RNDeviceInfo (from `../node_modules/react-native-device-info`)
  - "RNFBAnalytics (from `../node_modules/@react-native-firebase/analytics`)"
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBCrashlytics (from `../node_modules/@react-native-firebase/crashlytics`)"
  - "RNFBMessaging (from `../node_modules/@react-native-firebase/messaging`)"
  - RNFS (from `../node_modules/react-native-fs`)
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNKeychain (from `../node_modules/react-native-keychain`)
  - RNLocalize (from `../node_modules/react-native-localize`)
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNQrGenerator (from `../node_modules/rn-qr-generator`)
  - RNReactNativeHapticFeedback (from `../node_modules/react-native-haptic-feedback`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNSha256 (from `../node_modules/react-native-sha256`)
  - RNShare (from `../node_modules/react-native-share`)
  - RNStaticSafeAreaInsets (from `../node_modules/react-native-static-safe-area-insets`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - "seon-react-native-mobile-wrapper (from `../node_modules/@seontechnologies/seon-react-native-mobile-wrapper`)"
  - TouchID (from `../node_modules/react-native-touch-id`)
  - VisionCamera (from `../node_modules/react-native-vision-camera`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  "https://token:<EMAIL>/basic/credolab/proxyen-sdk/cocoapods/index.git":
    - CredoAppBehavioral
    - CredoAppCalendarEvents
    - CredoAppCalendarReminders
    - CredoAppContacts
    - CredoAppCore
    - CredoAppIovation
    - CredoAppMedia
    - FraudForce
  trunk:
    - AppCenter
    - AppCenterReactNativeShared
    - BEMCheckBox
    - CleverTap-iOS-SDK
    - CustomerIO
    - CustomerIOCommon
    - CustomerIOMessagingInApp
    - CustomerIOMessagingPush
    - CustomerIOMessagingPushAPN
    - CustomerIOTracking
    - DatadogCore
    - DatadogCrashReporting
    - DatadogInternal
    - DatadogLogs
    - DatadogRUM
    - DatadogTrace
    - DatadogWebViewTracking
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - lottie-ios
    - MetaMapSDK
    - MMKV
    - MMKVCore
    - nanopb
    - OpenTelemetrySwiftApi
    - PLCrashReporter
    - PromisesObjC
    - PromisesSwift
    - SDWebImage
    - SeonSDK
    - SocketRocket
    - ZXingObjC

EXTERNAL SOURCES:
  appcenter-analytics:
    :path: "../node_modules/appcenter-analytics"
  appcenter-core:
    :path: "../node_modules/appcenter"
  appcenter-crashes:
    :path: "../node_modules/appcenter-crashes"
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  clevertap-react-native:
    :path: "../node_modules/clevertap-react-native"
  customerio-reactnative:
    :path: "../node_modules/customerio-reactnative"
  DatadogSDKReactNative:
    :path: "../node_modules/@datadog/mobile-react-native"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  fmt:
    :podspec: "../node_modules/react-native/third-party-podspecs/fmt.podspec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2024-06-28-RNv0.74.3-7bda0c267e76d11b68a585f84cfdd65000babf85
  jail-monkey:
    :path: "../node_modules/jail-monkey"
  lottie-react-native:
    :path: "../node_modules/lottie-react-native"
  Permission-Camera:
    :path: "../node_modules/react-native-permissions/ios/Camera"
  Permission-Contacts:
    :path: "../node_modules/react-native-permissions/ios/Contacts"
  Permission-FaceID:
    :path: "../node_modules/react-native-permissions/ios/FaceID"
  Permission-LocationAccuracy:
    :path: "../node_modules/react-native-permissions/ios/LocationAccuracy"
  Permission-LocationAlways:
    :path: "../node_modules/react-native-permissions/ios/LocationAlways"
  Permission-LocationWhenInUse:
    :path: "../node_modules/react-native-permissions/ios/LocationWhenInUse"
  Permission-Notifications:
    :path: "../node_modules/react-native-permissions/ios/Notifications"
  Permission-PhotoLibrary:
    :path: "../node_modules/react-native-permissions/ios/PhotoLibrary"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTDeprecation:
    :path: "../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/Required"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-Fabric:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../node_modules/react-native/ReactCommon"
  React-featureflags:
    :path: "../node_modules/react-native/ReactCommon/react/featureflags"
  React-graphics:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-ImageManager:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-jsitracing:
    :path: "../node_modules/react-native/ReactCommon/hermes/executor/"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../node_modules/react-native/ReactCommon"
  react-native-cameraroll:
    :path: "../node_modules/@react-native-community/cameraroll"
  react-native-config:
    :path: "../node_modules/react-native-config"
  react-native-contacts:
    :path: "../node_modules/react-native-contacts"
  react-native-document-picker:
    :path: "../node_modules/react-native-document-picker"
  react-native-geolocation:
    :path: "../node_modules/@react-native-community/geolocation"
  react-native-geolocation-service:
    :path: "../node_modules/react-native-geolocation-service"
  react-native-image-picker:
    :path: "../node_modules/react-native-image-picker"
  react-native-image-resizer:
    :path: "../node_modules/@bam.tech/react-native-image-resizer"
  react-native-images-to-pdf:
    :path: "../node_modules/react-native-images-to-pdf"
  react-native-metaMap-sdk:
    :path: "../node_modules/react-native-metamap-sdk"
  react-native-mmkv:
    :path: "../node_modules/react-native-mmkv"
  react-native-netinfo:
    :path: "../node_modules/@react-native-community/netinfo"
  react-native-restart:
    :path: "../node_modules/react-native-restart"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-slider:
    :path: "../node_modules/@react-native-community/slider"
  react-native-tracking-transparency:
    :path: "../node_modules/react-native-tracking-transparency"
  react-native-view-shot:
    :path: "../node_modules/react-native-view-shot"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-nativeconfig:
    :path: "../node_modules/react-native/ReactCommon"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../node_modules/react-native/React"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rendererdebug:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-RuntimeApple:
    :path: "../node_modules/react-native/ReactCommon/react/runtime/platform/ios"
  React-RuntimeCore:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-RuntimeHermes:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNBootSplash:
    :path: "../node_modules/react-native-bootsplash"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCCheckbox:
    :path: "../node_modules/@react-native-community/checkbox"
  RNCClipboard:
    :path: "../node_modules/@react-native-clipboard/clipboard"
  RNCPicker:
    :path: "../node_modules/@react-native-picker/picker"
  RNCPushNotificationIOS:
    :path: "../node_modules/@react-native-community/push-notification-ios"
  RNCredoappsdk:
    :path: "../node_modules/@credolab/react-core"
  RNCredoappsdkBehavioral:
    :path: "../node_modules/@credolab/react-behavioral"
  RNCredoappsdkIosCalendarEvents:
    :path: "../node_modules/@credolab/react-ios-calendar-events"
  RNCredoappsdkIosCalendarReminders:
    :path: "../node_modules/@credolab/react-ios-calendar-reminders"
  RNCredoappsdkIosContacts:
    :path: "../node_modules/@credolab/react-ios-contact"
  RNCredoappsdkIosIovation:
    :path: "../node_modules/@credolab/react-ios-iovation"
  RNCredoappsdkIosMedia:
    :path: "../node_modules/@credolab/react-ios-media"
  RNDateTimePicker:
    :path: "../node_modules/@react-native-community/datetimepicker"
  RNDeviceInfo:
    :path: "../node_modules/react-native-device-info"
  RNFBAnalytics:
    :path: "../node_modules/@react-native-firebase/analytics"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBCrashlytics:
    :path: "../node_modules/@react-native-firebase/crashlytics"
  RNFBMessaging:
    :path: "../node_modules/@react-native-firebase/messaging"
  RNFS:
    :path: "../node_modules/react-native-fs"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNKeychain:
    :path: "../node_modules/react-native-keychain"
  RNLocalize:
    :path: "../node_modules/react-native-localize"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNQrGenerator:
    :path: "../node_modules/rn-qr-generator"
  RNReactNativeHapticFeedback:
    :path: "../node_modules/react-native-haptic-feedback"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSha256:
    :path: "../node_modules/react-native-sha256"
  RNShare:
    :path: "../node_modules/react-native-share"
  RNStaticSafeAreaInsets:
    :path: "../node_modules/react-native-static-safe-area-insets"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  seon-react-native-mobile-wrapper:
    :path: "../node_modules/@seontechnologies/seon-react-native-mobile-wrapper"
  TouchID:
    :path: "../node_modules/react-native-touch-id"
  VisionCamera:
    :path: "../node_modules/react-native-vision-camera"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  AppCenter: 3fd04aa1b166e16fdb03ec81dabe488aece83fbd
  appcenter-analytics: 3c06d292afa81fdbf4f48790007e189d01e77d3b
  appcenter-core: 69deb1f6818d1e489fa36ad1be543b50bb81aebf
  appcenter-crashes: d666003cd6df6b718ccfb67d07eebd23d015040c
  AppCenterReactNativeShared: f395caeabde0dc3a11609dbcb737d0f14cd40e79
  BEMCheckBox: 5ba6e37ade3d3657b36caecc35c8b75c6c2b1a4e
  boost: d3f49c53809116a5d38da093a8aa78bf551aed09
  BVLinearGradient: cb006ba232a1f3e4f341bb62c42d1098c284da70
  CleverTap-iOS-SDK: 78ea6d752d84918f0426f7b3959777d862bcb348
  clevertap-react-native: 8c7a97304df1ee9640f963cc08a9069c42b4935c
  CredoAppBehavioral: b5c739fd48d8e8981d642cf3c5d041d74ec04167
  CredoAppCalendarEvents: 37ed92270e47cd2d63d4087638c64ce83f7c5b5b
  CredoAppCalendarReminders: 6c05dd88468e92242fd8cbfc10c4ca178f07b49c
  CredoAppContacts: 07c3c16d04c52bd502fa37ab5f55e734a0972bf2
  CredoAppCore: dc6da731d2f9f0fe1079b1a2e28577dcf8066405
  CredoAppIovation: 68ec09958ffcbd4ad845ba1c8fe04efbccfe1015
  CredoAppMedia: dfcf82dc19219182c43cf15cdff31eb20f93e16a
  CustomerIO: fab11c52035a486ca5029383a60b3d50233ae07c
  customerio-reactnative: 32380ba2a9f41ac18ef04a556e81a0ab82f86bc7
  CustomerIOCommon: fdfa2fd3250771fbdd8e1f174994701d40bce52c
  CustomerIOMessagingInApp: a1d10a74f700438ab2bdfb7796debc2f4bb94e4b
  CustomerIOMessagingPush: 3446201b4102f267f003affcb8a511f8c0bd6a54
  CustomerIOMessagingPushAPN: 64cd27b1c3b52ef555ddaafd4693187d49083f23
  CustomerIOTracking: 7fba3ad0ce9462182aacc1df24144f45df0157fb
  DatadogCore: a068d264191f687d00125aafa8042110cf886cd7
  DatadogCrashReporting: b19d80a98b1eb51bdb3ec5b1a7f339769fb70b95
  DatadogInternal: e9b6cef84448ee32f73bc9b37646708a65c1b8ae
  DatadogLogs: 623d89158ef3a4a7545a8fbce9afa4cedc576324
  DatadogRUM: ff0661b42124d90c9ff2538c3cc1b806ddf7620c
  DatadogSDKReactNative: 4dcde8ad9a2ab8baecb95d3af9481707f2f43926
  DatadogTrace: 4e1aac322eafd8568fd4a966b02e58fbb5af0612
  DatadogWebViewTracking: 3b2465b5a5bf10f187807e490678a2e0a3c2daae
  DoubleConversion: 76ab83afb40bddeeee456813d9c04f67f78771b5
  FBLazyVector: ac12dc084d1c8ec4cc4d7b3cf1b0ebda6dab85af
  Firebase: cec914dab6fd7b1bd8ab56ea07ce4e03dd251c2d
  FirebaseAnalytics: 23717de130b779aa506e757edb9713d24b6ffeda
  FirebaseCore: 30e9c1cbe3d38f5f5e75f48bfcea87d7c358ec16
  FirebaseCoreExtension: 705ca5b14bf71d2564a0ddc677df1fc86ffa600f
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseCrashlytics: 34647b41e18de773717fdd348a22206f2f9bc774
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  FirebaseMessaging: 7b5d8033e183ab59eb5b852a53201559e976d366
  FirebaseRemoteConfigInterop: 6efda51fb5e2f15b16585197e26eaa09574e8a4d
  FirebaseSessions: dbd14adac65ce996228652c1fc3a3f576bdf3ecc
  fmt: 4c2741a687cc09f0634a2e2c72a838b99f1ff120
  FraudForce: 07965345109ebe6273b7b8abb8ef1b4d7705856f
  glog: fdfdfe5479092de0c4bdbebedd9056951f092c4f
  GoogleAppMeasurement: f9de05ee17401e3355f68e8fc8b5064d429f5918
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  hermes-engine: 8c1577f3fdb849cbe7729c2e7b5abc4b845e88f8
  jail-monkey: 1846061ac12e861ac5a8ec7197b0daa775b83733
  lottie-ios: e047b1d2e6239b787cc5e9755b988869cf190494
  lottie-react-native: 45707364bd70cffa7602fa1a1abb40dee5f3c0e0
  MetaMapSDK: 9f640aa0176a40d383ad002660f06ae63e5465e9
  MMKV: b4802ebd5a7c68fc0c4a5ccb4926fbdfb62d68e0
  MMKVCore: a255341a3746955f50da2ad9121b18cb2b346e61
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  OpenTelemetrySwiftApi: aaee576ed961e0c348af78df58b61300e95bd104
  Permission-Camera: cf14fb2d76432ee0f36546249ee608ef6037a23e
  Permission-Contacts: 72c74d9415090effa9e79c7fc46423ad5b3e020d
  Permission-FaceID: 57f30c981224753654df9350828dfbfc75dc5e83
  Permission-LocationAccuracy: 30c5421911024b28d8916db5cbd728097da54434
  Permission-LocationAlways: af165dee8a5a5888df6764f9f6ba98b112893709
  Permission-LocationWhenInUse: e4a1bdc6b9f4a7a598613a6a748bd186e937df34
  Permission-Notifications: e254bc5abf9c33a6ac6b9f68f4c406d5bf7ab46e
  Permission-PhotoLibrary: 6088ef4424a006e2bbf3ef6ccbca0f88fa1a52a4
  PLCrashReporter: db59ef96fa3d25f3650040d02ec2798cffee75f2
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  RCT-Folly: 5dc73daec3476616d19e8a53f0156176f7b55461
  RCTDeprecation: 3afceddffa65aee666dafd6f0116f1d975db1584
  RCTRequired: ec1239bc9d8bf63e10fb92bd8b26171a9258e0c1
  RCTTypeSafety: f5ecbc86c5c5fa163c05acb7a1c5012e15b5f994
  React: fc9fa7258eff606f44d58c5b233a82dc9cf09018
  React-callinvoker: e3fab14d69607fb7e8e3a57e5a415aed863d3599
  React-Codegen: 3963186cb6a4ef21b5e67dcf7badf359867ff6df
  React-Core: c3f589f104983dec3c3eeec5e70d61aa811bc236
  React-CoreModules: 864932ddae3ead5af5bfb05f9bbc2cedcb958b39
  React-cxxreact: bd9146108c44e6dbb99bba4568ce7af0304a2419
  React-debug: d30893c49ae1bce4037ea5cd8bb2511d2a38d057
  React-Fabric: a171830e52baf8ec2b175c6a3791e01bbb92f1fb
  React-FabricImage: ad154af0067f4b5dc5a41f607e48ee343641e903
  React-featureflags: 4ae83e72d9a92452793601ac9ac7d2280e486089
  React-graphics: ed7d57965140168de86835946e8f1210c72c65dc
  React-hermes: 177b1efdf3b8f10f4ca12b624b83fb4d4ccb2884
  React-ImageManager: 3a50d0ee0bf81b1a6f23a0c5b30388293bcd6004
  React-jserrorhandler: dcd62f5ca1c724c19637595ef7f45b78018e758f
  React-jsi: 0abe1b0881b67caf8d8df6a57778dd0d3bb9d9a5
  React-jsiexecutor: f6ca8c04f19f6a3acaa9610f7fb728f39d6e3248
  React-jsinspector: db98771eae84e6f86f0ca5d9dcc572baadbfefc0
  React-jsitracing: f8367edacc50bb3f9f056a5aeafb8cee5849fafb
  React-logger: 780b9ee9cec7d44eabc4093de90107c379078cb6
  React-Mapbuffer: f544f00b98dbdd8cbae96dd2bdb8b47f719976e0
  react-native-cameraroll: 1c66ad17d20250030cb27054fe7ef3a43ba879eb
  react-native-config: ea75335a7cca1d3326de1da384227e580a7c082e
  react-native-contacts: f551920b74ebfc5f7f6df307495f085e504a4369
  react-native-document-picker: 451699da81cba8b40b596b8076019a4deb86f46e
  react-native-geolocation: 14bc6d37fe173102d42613928c40a242dacb526f
  react-native-geolocation-service: 32b2c2a3b91e70ce2a8d0c684801aaeb0a07e0ec
  react-native-image-picker: 1f5318beec2ebed6695454ffb9bea8c4152f1598
  react-native-image-resizer: 24c5d06fae2176dc0caed4b6396e02befb44064a
  react-native-images-to-pdf: 28a997d530a79468715f4d5d82d1b5a63321ea50
  react-native-metaMap-sdk: 85520bc9a4aaacfd1a411cc6fde215b995e8e2aa
  react-native-mmkv: f8155c2efbe795cb0c7586d00ff484b1c9388af0
  react-native-netinfo: cec9c4e86083cb5b6aba0e0711f563e2fbbff187
  react-native-restart: 0bc732f4461709022a742bb29bcccf6bbc5b4863
  react-native-safe-area-context: ca0ebcd9798c5a45863c608c961d4f3f984b6ed2
  react-native-slider: 0d395253260c431fa22dd8bfb2c1bb665cc3a940
  react-native-tracking-transparency: 15eb319f2b982070eb9831582af27d87badfa624
  react-native-view-shot: d1a701eb0719c6dccbd20b4bb43b1069f304cb70
  react-native-webview: e69e694a0b2ef4c233aa9ac5058279f62817253d
  React-nativeconfig: ba9a2e54e2f0882cf7882698825052793ed4c851
  React-NativeModulesApple: 84aaad2b0e546d7b839837ca537f6e72804a4cad
  React-perflogger: ed4e0c65781521e0424f2e5e40b40cc7879d737e
  React-RCTActionSheet: 49d53ff03bb5688ca4606c55859053a0cd129ea5
  React-RCTAnimation: 3075449f26cb98a52bcbf51cccd0c7954e2a71db
  React-RCTAppDelegate: 9a419c4dda9dd039ad851411546dd297b930c454
  React-RCTBlob: e81ab773a8fc1e9dceed953e889f936a7b7b3aa6
  React-RCTFabric: 47a87a3e3fa751674f7e64d0bcd58976b8c57db9
  React-RCTImage: d570531201c6dce7b5b63878fa8ecec0cc311c4c
  React-RCTLinking: af888972b925d2811633d47853c479e88c35eb4d
  React-RCTNetwork: 5728a06ff595003eca628f43f112a804f4a9a970
  React-RCTSettings: ba3665b0569714a8aaceee5c7d23b943e333fa55
  React-RCTText: b733fa984f0336b072e47512898ba91214f66ddb
  React-RCTVibration: 0cbcbbd8781b6f6123671bae9ee5dd20d621af6c
  React-rendererdebug: 9fc8f7d0bd19f2a3fe3791982af550b5e1535ff7
  React-rncore: 4013508a2f3fcf46c961919bbbd4bfdda198977e
  React-RuntimeApple: a852a6e06ab20711658873f39cb10b0033bea19d
  React-RuntimeCore: 12e5e176c0cb09926f3e6f37403a84d2e0f203a7
  React-runtimeexecutor: 0e688aefc14c6bc8601f4968d8d01c3fb6446844
  React-RuntimeHermes: 80c03a5215520c9733764ba11cbe535053c9746d
  React-runtimescheduler: 2cbd0f3625b30bba08e8768776107f6f0203159b
  React-utils: 9fa4e5d0b5e6c6c85c958f19d6ef854337886417
  ReactCommon: 9f285823dbe955099978d9bff65a7653ca029256
  RNBootSplash: 21095c4567847829470786b03b6892c5efed5299
  RNCAsyncStorage: b6410dead2732b5c72a7fdb1ecb5651bbcf4674b
  RNCCheckbox: 450ce156f3e29e25efa0315c96cfbabe5a39ded1
  RNCClipboard: 47593431a9e763a71950798683b28f691f16b98d
  RNCPicker: f89d06ded677f77224839bccc1ca18611948e54c
  RNCPushNotificationIOS: 6c4ca3388c7434e4a662b92e4dfeeee858e6f440
  RNCredoappsdk: a198bc16523ebb84e714c4b72433cf4d0e1bb416
  RNCredoappsdkBehavioral: 5b424191b75b4de4abee1e84f27bd06e3f5c078a
  RNCredoappsdkIosCalendarEvents: 0753137e564ab76865f6e1b4e4880cb3c236054c
  RNCredoappsdkIosCalendarReminders: 5f04b2c050aedac675e98b114d3d4ba7a67f509b
  RNCredoappsdkIosContacts: 9a8f7425efcef7a96b23a660b69762c6205d2d3d
  RNCredoappsdkIosIovation: bbb5acd0dd005f44509acbdf4d94e30baae6e658
  RNCredoappsdkIosMedia: ea7b1cd5a3d31ddbb0b249e710d8e4ce755ccf19
  RNDateTimePicker: 818460dc31b0dc5ec58289003e27dd8d022fb79c
  RNDeviceInfo: d3e91ffb33ee97a7982108476edb68cb3672efa6
  RNFBAnalytics: f12c83fab147fea9b422524c0a1d9548d908ba90
  RNFBApp: 0e66b9f844efdf2ac3fa2b30e64c9db41a263b3d
  RNFBCrashlytics: 0926f2db72d9d013330fc818851e088bb7c678d2
  RNFBMessaging: 70b12c9f22c7c9d5011ac9b12ac2bafbfb081267
  RNFS: 89de7d7f4c0f6bafa05343c578f61118c8282ed8
  RNGestureHandler: fb7c9c89ab155eb8e22ff68a3802e79baa4885be
  RNKeychain: c497f7844b24577523bb84a6e8af9d217957972f
  RNLocalize: e7378161f0b6a6365407eb2377aab46cc38047d8
  RNPermissions: bd0d9ca7969ff7b999aa605ee2e5919c12522bfe
  RNQrGenerator: 60eab4f7c9e3f09db78029636fe356dca5cb585f
  RNReactNativeHapticFeedback: 1a788dc6e3b39e9a9c3131019c71af47835c5436
  RNReanimated: aacae7d77041702070652e013482b7a3653c4aa5
  RNScreens: 5d770465a09cbb7cf83e2ad43d6f049b52e62bad
  RNSha256: 4d35f112e967f58273d625a19376f4eafc72bd5c
  RNShare: 694e19d7f74ac4c04de3a8af0649e9ccc03bd8b1
  RNStaticSafeAreaInsets: 4696b82d3a11ba6f3a790159ddb7290f04abd275
  RNSVG: d7cb8bd34550cf4c4fc7edd7ac627905e2b71f3f
  SDWebImage: f84b0feeb08d2d11e6a9b843cb06d75ebf5b8868
  seon-react-native-mobile-wrapper: dbd58b06060685dec3c88ccb6d4a691a88d5e30f
  SeonSDK: 6f350696536fe9c88b3098577716a624cfc741e3
  SocketRocket: abac6f5de4d4d62d24e11868d7a2f427e0ef940d
  TouchID: 6d9af299007675845fe9e23bd2701f4354613366
  VisionCamera: fbd2ae1a0c89a6ddb6e619275c5369bdd2f8f0b2
  Yoga: 950bbfd7e6f04790fdb51149ed51df41f329fcc8
  ZXingObjC: 8898711ab495761b2dbbdec76d90164a6d7e14c5

PODFILE CHECKSUM: 6bc3769fc3b28e04c295edc0334154096510a8cf

COCOAPODS: 1.16.2
