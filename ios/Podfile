# custom source for credolab
source 'https://token:<EMAIL>/basic/credolab/proxyen-sdk/cocoapods/index.git'

plugin 'cocoapods-user-defined-build-types'

# Resolve react_native_pods.rb with node to allow for hoisting
require Pod::Executable.execute_command('node', ['-p',
  'require.resolve(
    "react-native/scripts/react_native_pods.rb",
    {paths: [process.argv[1]]},
  )', __dir__]).strip

source 'https://cdn.cocoapods.org/'


platform :ios, '13.4'
# use_modular_headers!
prepare_react_native_project!
 enable_user_defined_build_types!

# use_modular_headers!

# setup_permissions([
#   'Camera',
#   'FaceID',
#   'PhotoLibrary',
#   'AppTrackingTransparency',
#   'LocationAccuracy',
#   'LocationAlways',
#   'LocationWhenInUse',
# ])

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

target 'Payforce' do
  config = use_native_modules!

  #  credolab
  #  pod 'CredoAppModule', '~> 1.0.0'
  pod 'CredoAppCore', '~> 	3.7.0'
  pod 'CredoAppBehavioral', '~> 3.4.0'
  pod 'CredoAppContacts', '~> 3.1.0'
  pod 'CredoAppIovation', '~> 3.1.0'
  pod 'FraudForce', '~> 5.4.1'

  pod 'DatadogSDKReactNative', :path => '../node_modules/@datadog/mobile-react-native'

  # pod 'customerio-reactnative/apn', :path => '../node_modules/customerio-reactnative'

  pod 'RNSha256', :path => '../node_modules/react-native-sha256'
  pod 'RNFS', :path => '../node_modules/react-native-fs'
  pod 'clevertap-react-native', :path => '../node_modules/clevertap-react-native'
  pod 'customerio-reactnative/apn', :path => '../node_modules/customerio-reactnative', :modular_headers => true

  use_react_native!(
    :path => config[:reactNativePath],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  # Hyperverge SDK
  pod 'HyperKYC', :build_type => :static_framework

  permissions_path = '../node_modules/react-native-permissions/ios'
  pod 'RNPermissions', :path => '../node_modules/react-native-permissions'
  pod 'Permission-Notifications', :path => "#{permissions_path}/Notifications"
  pod 'Permission-Contacts', :path => "#{permissions_path}/Contacts"
  pod 'Permission-Camera', :path => "#{permissions_path}/Camera"
  pod 'Permission-FaceID', :path => "#{permissions_path}/FaceID"
  pod 'Permission-PhotoLibrary', :path => "#{permissions_path}/PhotoLibrary"
  pod 'Permission-LocationAccuracy', :path => "#{permissions_path}/LocationAccuracy"
  pod 'Permission-LocationAlways', :path => "#{permissions_path}/LocationAlways"
  pod 'Permission-LocationWhenInUse', :path => "#{permissions_path}/LocationWhenInUse"

  pod 'GoogleUtilities', :modular_headers => true
  pod 'FirebaseCore', :modular_headers => true
  pod 'FirebaseSessions', :modular_headers => true
  pod 'FirebaseCrashlytics', :modular_headers => true
  pod 'FirebaseInstallations', :modular_headers => true
  pod 'GoogleDataTransport', :modular_headers => true
  pod 'nanopb', :modular_headers => true
  pod 'FirebaseCoreExtension', :modular_headers => true

  target 'PayforceTests' do
    inherit! :complete
  end

  post_install do |installer|
    # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      # :ccache_enabled => true
    )

    installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      # Force all pods to minimum iOS 13.4
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '13.4'

      # Optional: Avoid warnings with Xcode 15
      # config.build_settings['EXCLUDED_ARCHS[sdk=iphonesimulator*]'] = 'arm64' if config.name == 'Debug'
    end
  end
  end
end

# target 'NotificationServiceExtension' do
#   pod 'customerio-reactnative-richpush/apn', :path => '../node_modules/customerio-reactnative', :modular_headers => true
# end
