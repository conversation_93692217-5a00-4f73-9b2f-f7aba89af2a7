default_platform(:ios)

platform :ios do
  # Common paths
  ARCHIVE_PATH = "./build/rc/Payforce.xcarchive"
  IPA_PATH = "#{ENV['GITHUB_WORKSPACE']}/ios/build/Payforce.ipa"

  # Common export options
  EXPORT_OPTIONS = {
    method: "app-store",
    signingStyle: "manual",
    teamID: "H4VAD2SLMY",
    uploadSymbols: true,
    uploadDsyms: true,
    signingCertificate: "iPhone Distribution: FAIRMONEY MICROFINANCE BANK LIMITED (H4VAD2SLMY)",
    provisioningProfiles: {
      "com.fairmoney.payforce" => "9652e148-852e-4c25-be6e-5c59e007786f",
      "com.fairmoney.payforce.NotificationServiceExtension" => "1936a401-1548-4cd1-9f7a-50889c46ce7d"
    }
  }

  lane :update_bundle_id_to_stage do
    update_app_identifier(
      xcodeproj: "Payforce.xcodeproj",
      new_identifier: "org.reactjs.native.Payforce.stage"
    )
  end

  lane :create_archive do
    build_ios_app(
    workspace: "Payforce.xcworkspace",
    scheme: "Payforce",
    configuration: "Release",
    archive_path: ARCHIVE_PATH,
    export_options: EXPORT_OPTIONS,
    skip_package_ipa: true
    )
  end

  desc "Create signed IPA from existing archive"
  lane :export_archive do
    gym(
      archive_path: ARCHIVE_PATH,
      export_method: "app-store",
      output_directory: "./build",
      output_name: "Payforce.ipa",
      export_options: {
        provisioningProfiles: {
          "org.reactjs.native.Payforce" => "FM Business App ***********.2025",
          "org.reactjs.native.Payforce.NotificationServiceExtension" => "FM Business Notification Service ***********.2025"
        },
        signingStyle: "manual",
        signingCertificate: "iPhone Distribution: FAIRMONEY MICROFINANCE BANK LIMITED (H4VAD2SLMY)"
      },
      skip_build_archive: true
    )
  end

  desc "Upload existing IPA to TestFlight"
  lane :upload_testflight do
    retry_count = 0
    max_retries = 3

    api_key = app_store_connect_api_key(
      key_id: ENV["P8_KEY_ID_CONTENT"],
      issuer_id: ENV["P8_ISSUER_ID_CONTENT"],
      key_filepath: "#{ENV['GITHUB_WORKSPACE']}/build/TestFlightSigning/api_key.p8",
      duration: 1200,
      in_house: false
    )

    begin
      upload_to_testflight(
        ipa: IPA_PATH,
        skip_waiting_for_build_processing: true,
        groups: ["General Purpose Testing Group"],
        api_key: api_key
      )
    rescue => e
      retry_count += 1
      if retry_count < max_retries
        UI.message("Upload attempt #{retry_count} failed. Retrying... (#{e.message})")
        sleep(30) # Wait 30 seconds before retrying
        retry
      else
        UI.error("Failed to upload after #{max_retries} attempts. Last error: #{e.message}")
        raise e
      end
    end
  end
end
