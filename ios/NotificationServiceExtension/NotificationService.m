#import "NotificationService.h"
#import <NotificationServiceExtension-Swift.h>
@interface NotificationService ()

@property (nonatomic, strong) void (^contentHandler)(UNNotificationContent *contentToDeliver);
@property (nonatomic, strong) UNMutableNotificationContent *bestAttemptContent;

@end

@implementation NotificationServiceExtension

 // Create object of class MyAppNotificationServicePushHandler
PayforceNotificationServicePushHandler* nsHandlerObj = nil;

 // Initialize the object
+ (void)initialize{
  nsHandlerObj = [[PayforceNotificationServicePushHandler alloc] init];
}
- (void)didReceiveNotificationRequest:(UNNotificationRequest *)request withContentHandler:(void (^)(UNNotificationContent * _Nonnull))contentHandler {
  [nsHandlerObj didReceive:request withContentHandler:contentHandler];
}

- (void)serviceExtensionTimeWillExpire {
  [nsHandlerObj serviceExtensionTimeWillExpire];
}

@end