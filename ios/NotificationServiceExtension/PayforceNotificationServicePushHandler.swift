//
//  PayforceNotificationServicePushHandler.swift
//  NotificationServiceExtension
//

//

import Foundation
import UserNotifications
//import CioTracking
//import CioMessagingPushAPN

 @objc
 public class PayforceNotificationServicePushHandler : NSObject {

//   public override init() {}
//
//   @objc(didReceive:withContentHandler:)
//   public func didReceive(_ request: UNNotificationRequest, withContentHandler contentHandler: @escaping (UNNotificationContent) -> Void) {
//
//    let siteId = "b4d4245f972887579418";
//    let apiKey = "379103e6e99fcfe99556";
//
//     // You can configure the SDK here
//     // Update region to .EU for your EU-based workspace 
//     CustomerIO.initialize(siteId: siteId, apiKey: apiKey, region: .US) { config in
//         config.autoTrackDeviceAttributes = true
//         config.logLevel = .info
//     }
//     MessagingPush.shared.didReceive(request, withContentHandler: contentHandler)
//   }
//
//   @objc(serviceExtensionTimeWillExpire)
//   public func serviceExtensionTimeWillExpire() {
//     MessagingPush.shared.serviceExtensionTimeWillExpire()
//   }
 }
