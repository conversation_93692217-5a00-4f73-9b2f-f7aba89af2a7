<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>FairMoney Business</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>None</string>
			<key>CFBundleURLName</key>
			<string>org.reactjs.native.Payforce</string>
		</dict>
	</array>
	<key>CleverTapAccountID</key>
	<string>TEST-9WW-975-986Z</string>
	<key>CleverTapRegion</key>
	<string>in1</string>
	<key>CleverTapToken</key>
	<string>TEST-5ac-00c</string>
	<key>CodePushDeploymentKey</key>
	<string>X32iYOMwH35XbJhLLlMKBl0Hohc9MPdNW_MKF</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>credoapp</string>
		<string>credoappsdk</string>
		<string>whatsapp</string>
		<string>fbapi</string>
		<string>fb-messenger-share-api</string>
		<string>fbauth2</string>
		<string>fbshareextension</string>
		<string>itms-apps</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>FairMoney Business needs access to your camera to allow you to take pictures of your face for biometrics identification or receipts.</string>
	<key>NSContactsUsageDescription</key>
	<string>FairMoney Business needs to access your contacts to enable easy bill purchases and transfers to your contacts</string>
	<key>NSFileProviderDomainUsageDescription</key>
	<string>FairMoney Business needs to access your files to save and share receipts</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>FairMoney Business request access to your location to enhance your security and personalize your experience. This helps us protect your account from unauthorized access and ensure a safer and more efficient interaction with our platform.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>FairMoney Business request access to your location to enhance your security and personalize your experience. This helps us protect your account from unauthorized access and ensure a safer and more efficient interaction with our platform.</string>
	<key>NSLocationUsageDescription</key>
	<string>FairMoney Business request access to your location to enhance your security and personalize your experience. This helps us protect your account from unauthorized access and ensure a safer and more efficient interaction with our platform.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>FairMoney Business request access to your location to enhance your security and personalize your experience. This helps us protect your account from unauthorized access and ensure a safer and more efficient interaction with our platform.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>FairMoney Business needs access to your Microphone</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>This app would like to save images to your device.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>FairMoney Business needs to access your photo library to save transaction receipts and also allow for query uploads</string>
	<key>NSUserTrackingUsageDescription</key>
	<string>FairMoney Business would like to collect crash logs and in-app activity in order to improve functionalities and provide you with tailored services. Your data is protected</string>
	<key>NSCalendarsUsageDescription</key>
	<string>FairMoney Business requests calendar access to help you manage your loan repayments by setting reminders and alerts for due dates. This ensures you stay on track and avoid late fees. Your privacy is our priority, and we only use this access to provide this feature.</string>
	<key>NSAppleMusicUsageDescription</key>
	<string>FairMoney Business needs access to your Apple Music library to in order to improve functionalities.</string>
	<key>UIAppFonts</key>
	<array>
		<string>poppins.ttf</string>
		<string>OpenSans-Regular.ttf</string>
		<string>Be Vietnam Pro.ttf</string>
		<string>DMSans-Bold.ttf</string>
		<string>DMSans-Medium.ttf</string>
		<string>DMSans-Regular.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
	</array>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>BootSplash</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>NSFaceIDUsageDescription</key>
	<string>Enabling Face ID allows you quick and secure access to your account.</string>
</dict>
</plist>
