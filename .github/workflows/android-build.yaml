name: "Validate Android Build"

on:
  workflow_dispatch:
  workflow_call:
    inputs:
      packages:
        description: 'Semicolon-separated list of Android SDK packages to install'
        required: false
        type: string
        default: "platform-tools build-tools;33.0.0 platforms;android-33"
      cache_name:
        description: 'Name of the cache for Android SDK'
        required: false
        type: string
        default: "android-sdk-cache"
    secrets: 
      KEYSTORE_B64:
        required: true
      KEYSTORE_FILENAME:
        required: true

jobs:
  validate-android-build:
    runs-on: [self-hosted, large-runner]

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Clean .android directory
        run: |
          rm -rf /home/<USER>/.android
          mkdir -p /home/<USER>/.android

      - name: Set up Node.js and Yarn
        uses: ./.github/actions/setup-node-yarn
        with:
          node-version: "18.19.0"
          yarn-version: "1.22.21"

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Generate artifact name
        id: meta
        uses: ./.github/actions/generate-artifact-name

      - name: Set up Android SDK
        uses: ./.github/actions/setup-android-sdk
        with:
          packages: ${{ inputs.packages }}
          cache-dir: ${{ inputs.cache_name }}

      - name: Decode Keystore
        run: |
          echo "${{ secrets.KEYSTORE_B64 }}" | base64 --decode > android/app/${{ secrets.KEYSTORE_FILENAME }}

      - name: Build Android APK
        run: |
          cd android
          ./gradlew assembleRelease

      - name: Upload Android APK
        uses: actions/upload-artifact@v4
        with:
          name: ${{ steps.meta.outputs.artifact_name }}
          path: android/app/build/outputs/apk/release/app-release.apk
          retention-days: 1

      - name: Remove label
        if: success()
        uses: ./.github/actions/auto-remove-label
        with:
          label: ready for review
          github-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Comment PR with APK link
        if: success()
        uses: ./.github/actions/comment-apk-link
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}

  # clean-artifacts:
    # runs-on: [self-hosted, standard-runner]
    # permissions:
    #   actions: write
    # needs: validate-android-build

    # steps:
    #   - name: Checkout repository
    #     uses: actions/checkout@v4
        
    #   - name: Generate artifact name
    #     id: meta
    #     uses: ./.github/actions/generate-artifact-name

    #   - name: Delete existing artifact
    #     uses: ./.github/actions/delete-artifact-if-exists
    #     with:
    #       artifact_name: ${{ steps.meta.outputs.artifact_name }}