name: Android Release Pipeline

on:
  push:
    branches:
      - 'release*'
      - 'hotfix*'
    tags:
      - '*'
  workflow_dispatch:

env:
  FIREBASE_SERVICE_KEY: ${{ secrets.FIREBASE_APP_DISTRIBUTION_KEY }}
  WALDO_UPLOAD_TOKEN: ${{ secrets.WALDO_TOKEN }}
  GRADLE_ENCRYPTION_KEY: ${{ secrets.GRADLE_ENCRYPTION_KEY }}

jobs:
  setup-and-build:
    name: Build Android App
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - name: Set up Node
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install dependencies
        run: yarn install

      - name: Set up Java & Gradle
        uses: gradle/gradle-build-action@v2

      - name: Set up Android SDK
        uses: android-actions/setup-android@v3

      - name: Decode Firebase credentials
        run: echo "${{ env.FIREBASE_SERVICE_KEY }}" | base64 -di > ./android/app/service_credentials_file.json

      - name: Set Firebase release notes
        run: echo "${GITHUB_REF##*/}" > ./android/app/release_notes.txt

      - name: Bundle Android Release
        run: ./gradlew bundleRelease

      - name: Build Universal APK (optional)
        run: ./gradlew assembleRelease

      - name: Upload to Firebase App Distribution
        run: ./gradlew appDistributionUploadRelease

      - name: Upload APK to Waldo
        uses: ./.github/actions/gh-action-upload-1.0.1
        with:
          build_path: android/app/build/outputs/apk/release/app-release.apk
          upload_token: ${{ env.WALDO_UPLOAD_TOKEN }}
