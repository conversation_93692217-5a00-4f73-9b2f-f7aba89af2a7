name: Pull Request Workflow

on:
  workflow_dispatch:
  pull_request:
    types:
      - opened
      - reopened
      - edited
      - synchronize
      - labeled
      - unlabeled
      - ready_for_review

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref }}
  cancel-in-progress: true

jobs:
  check-pr-title:
    name: Check PR Title
    runs-on: [self-hosted, standard-runner]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check PR Title
        uses: fairmoney/pr-title-checker@v1.4.3
        with:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          pass_on_octokit_error: false

  check-changelog:
    name: Check Changelog
    runs-on: [self-hosted, standard-runner]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Check Changelog
        uses: ./.github/actions/changelog-checker
        with:
          base-branch: ${{ github.base_ref }}

  validate-commit:
    name: Validate Commit
    runs-on: [self-hosted, standard-runner]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Validate Commit
        uses: ./.github/actions/validate-commit
        with:
          node-version: '18'

  check-build-labels:
    name: Check Build Labels
    runs-on: [self-hosted, standard-runner]
    outputs:
      has_android_build: ${{ steps.check-labels.outputs.has_android_build }}
      has_ios_build: ${{ steps.check-labels.outputs.has_ios_build }}
    steps:
      - name: Check Labels
        id: check-labels
        run: |
          HAS_READY_FOR_REVIEW=$(echo '${{ inputs.labels_json }}' | jq -e '
            if type == "array" then
              any(.[]?; .name == "ready for review")
            else
              false
            end
          ' && echo true || echo false)

          echo "HAS_READY_FOR_REVIEW=$HAS_READY_FOR_REVIEW"

          if [[ "$HAS_READY_FOR_REVIEW" == "true" ]]; then
            echo "has_android_build=true" >> "$GITHUB_OUTPUT"
            echo "has_ios_build=true" >> "$GITHUB_OUTPUT"
          else
            echo "has_android_build=false" >> "$GITHUB_OUTPUT"
            echo "has_ios_build=false" >> "$GITHUB_OUTPUT"
          fi

  test-android-build:
    name: Validate Android Build
    needs: check-build-labels
    if: needs.check-build-labels.outputs.has_android_build == 'true'
    uses: ./.github/workflows/android-build.yaml
    with:
       cache_name: .android-cache
    secrets:
      KEYSTORE_B64: ${{ secrets.KEYSTORE_B64 }}
      KEYSTORE_FILENAME: ${{ secrets.KEYSTORE_FILENAME }}


  test-ios-build:
    name: Validate iOS Build
    needs: check-build-labels
    if: needs.check-build-labels.outputs.has_ios_build == 'true'
    uses: ./.github/workflows/ios-release.yaml
    with:
      skip_upload: false
    secrets:
      IOS_ACCESS_TOKEN_GITHUB: ${{ secrets.IOS_ACCESS_TOKEN_GITHUB }}
      KEYCHAIN_PASSWORD: ${{ secrets.KEYCHAIN_PASSWORD }}
      SIGNING_CERTIFICATE_FILE: ${{ secrets.SIGNING_CERTIFICATE_FILE }}
      SIGNING_CERTIFICATE_PASSWORD: ${{ secrets.SIGNING_CERTIFICATE_PASSWORD }}
      TESTFLIGHT_RELEASE_PROVISIONING_PROFILE: ${{ secrets.TESTFLIGHT_RELEASE_PROVISIONING_PROFILE }}
      NOTIFICATIONS_TESTFLIGHT_RELEASE_PROVISIONING_PROFILE: ${{ secrets.NOTIFICATIONS_TESTFLIGHT_RELEASE_PROVISIONING_PROFILE }}
      P8_CERTIFICATE: ${{ secrets.P8_CERTIFICATE }}
      KEY_ID: ${{ secrets.KEY_ID }}
      ISSUER_ID: ${{ secrets.ISSUER_ID }} 