name: iOS Release

on:
  workflow_dispatch:
  workflow_call:
    inputs:
      skip_upload:
        description: 'Skip TestFlight upload'
        required: false
        type: boolean
        default: false
    secrets:
      IOS_ACCESS_TOKEN_GITHUB:
        required: true
      KEYCHAIN_PASSWORD:
        required: true
      SIGNING_CERTIFICATE_FILE:
        required: true
      SIGNING_CERTIFICATE_PASSWORD:
        required: true
      TESTFLIGHT_RELEASE_PROVISIONING_PROFILE:
        required: true
      NOTIFICATIONS_TESTFLIGHT_RELEASE_PROVISIONING_PROFILE:
        required: true
      P8_CERTIFICATE:
        required: true
      KEY_ID:
        required: true
      ISSUER_ID:
        required: true

env:
  DEVELOPER_ID: ${{ secrets.DEVELOPER_ID }}
  MATCH_PASSWORD: ${{ secrets.MATCH_PASSWORD }}
  FASTLANE_PASSWORD: ${{ secrets.FASTLANE_PASSWORD }}
  APP_STORE_CONNECT_API_KEY: ${{ secrets.APP_STORE_CONNECT_API_KEY }}

jobs:
  caches:
    runs-on: warp-macos-15-arm64-6x
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Awake mise preconditions
        uses: fairmoney/action-mise@v2
        env:
          MISE_GITHUB_TOKEN: ${{ secrets.IOS_ACCESS_TOKEN_GITHUB }}
          MISE_VERBOSE: 1

  build:
    # if: false  # Set to true to enable build
    needs: caches
    runs-on: warp-macos-15-arm64-6x
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Awake mise preconditions
        uses: fairmoney/action-mise@v2
        env:
          MISE_GITHUB_TOKEN: ${{ secrets.IOS_ACCESS_TOKEN_GITHUB }}
          MISE_VERBOSE: 1

      - name: Install Ruby
        uses: fairmoney/action-warpbuild-setup-ruby@v1.2.0
        with:
          ruby-version: "3.2.2"
          bundler-cache: true

      - name: Bootstrap environment
        run: |
          ./bootstrap.sh

      - name: Strip Bitcode from third party frameworks
        run: |
          cd ios
          FRAUDFORCE_PATH="Pods/FraudForce/FraudForce.xcframework/ios-arm64/FraudForce.framework/FraudForce"
          xcrun bitcode_strip -r "$FRAUDFORCE_PATH" -o "$FRAUDFORCE_PATH"

      - name: Prepare Keychain
        uses: ./.github/actions/prepare_keychain
        with:
          keychain_password: ${{ secrets.KEYCHAIN_PASSWORD }}
          signing_certificate_file: ${{ secrets.SIGNING_CERTIFICATE_FILE }}
          signing_certificate_password: ${{ secrets.SIGNING_CERTIFICATE_PASSWORD }}

      - name: Install Provisioning Profiles
        uses: ./.github/actions/install_provisions
        with:
          testflight_release_provisioning_profile: ${{ secrets.TESTFLIGHT_RELEASE_PROVISIONING_PROFILE }}
          notifications_testflight_release_provisioning_profile: ${{ secrets.NOTIFICATIONS_TESTFLIGHT_RELEASE_PROVISIONING_PROFILE }}

      - name: Print Provisioning Profiles
        uses: ./.github/actions/print_provisions

      - name: Create Archive
        run: |
          cd ios
          bundle exec fastlane create_archive

      - name: Export Archive
        if: ${{ !inputs.skip_upload }}
        run: |
          cd ios
          bundle exec fastlane export_archive

      - name: Cache IPA
        uses: fairmoney/action-warpbuild-cache@v1.4.5
        if: ${{ !inputs.skip_upload }}
        with:
          path: ios/build/Payforce.ipa
          key: ipa-debug-ipa-key-${{ github.run_id }}

  upload-testflight:
    needs: build
    if: ${{ !inputs.skip_upload }}
    runs-on: warp-macos-15-arm64-6x
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install Ruby
        uses: fairmoney/action-warpbuild-setup-ruby@v1.2.0
        with:
          ruby-version: "3.2.2"
          bundler-cache: true

      - name: Restore IPA from cache
        uses: fairmoney/action-warpbuild-cache@v1.4.5
        with:
          path: ios/build/Payforce.ipa
          key: ipa-debug-ipa-key-${{ github.run_id }}

      - name: Verify IPA
        run: |
          if [ ! -f "$GITHUB_WORKSPACE/ios/build/Payforce.ipa" ]; then
            echo "Error: IPA file not found!"
            exit 1
          fi
          echo "IPA file exists and is ready for upload"

      - name: Setup P8 Certificate
        uses: ./.github/actions/setup_p8_cert
        with:
          p8_certificate: ${{ secrets.P8_CERTIFICATE }}
          key_id: ${{ secrets.KEY_ID }}
          issuer_id: ${{ secrets.ISSUER_ID }}

      - name: Verify P8 Certificate
        run: |
          if [ ! -f "$GITHUB_WORKSPACE/build/TestFlightSigning/api_key.p8" ]; then
            echo "Error: P8 certificate file not found!"
            exit 1
          fi
          echo "P8 certificate file exists and is ready for upload"

      - name: Upload to TestFlight
        env:
          P8_KEY_ID_CONTENT: ${{ secrets.KEY_ID }}
          P8_ISSUER_ID_CONTENT: ${{ secrets.ISSUER_ID }}
          P8_API_KEY_CONTENT: ${{ secrets.P8_CERTIFICATE }}
        run: |
          cd ios
          bundle exec fastlane upload_testflight
