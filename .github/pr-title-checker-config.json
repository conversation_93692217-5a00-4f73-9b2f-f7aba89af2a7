{"LABEL": {"name": "title needs formatting", "color": "EEEEEE"}, "CHECKS": {"prefixes": ["fix: ", "feat: ", "feat!: ", "chore: ", "refactor: ", "docs: "], "regexp": "^(MBK1-\\d{1,7}\\s-\\s.+|MBK2-\\d{1,7}\\s-\\s.+|PL-\\d{1,7}\\s-\\s.+|KARIBU-\\d{1,7}\\s-\\s.+|NO-TICKET\\s-\\s.+)$", "regexpFlags": "i", "ignoreLabels": ["dont-check-PRs-with-this-label"]}, "MESSAGES": {"success": "PR title looks good", "failure": "Please use correct PR title format starting with one of fix:, feat:, feat!:, chore:, refactor: or docs:, followed by one of [MBK1-####], [MBK2-####], [KARIBU-####] or [PL-####]. eg: '[OPS-1234] Onboard Johnny'", "notice": ""}}