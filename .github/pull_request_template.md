## crowdforcego/fairmoney-business

Describe what has been done (Add UI Image if it's a new change)

<!-- Example:

- MBK-1234 - Change Color
-->

# Type of change

- [ ] fix - for a bug fix or business logic is changes
- [ ] feat - for a feature implementation or for a rule change that adds reported problems.
- [ ] feat! - for a backwards-incompatible enhancement or feature.
- [ ] docs - changes to documentation only.
- [ ] chore - for changes that aren’t user-facing.
- [ ] refactor - a change that doesn’t affect APIs or user experience.

<details>
<summary>PR Title Linter Fails</summary>

<br />

## Examples of correct PR titles:

```txt

fix: MBK1-123 - Fix bug in the login page
feat: MBK2-4567 - Implement new feature
docs: KARIBU-1 - Update documentation
refactor: NO-TICKET - Refactor codebase

```

## Examples of Incorrect PR titles:

```txt

fix:MBK1-123 - Fix bug in the login page
feat: [MBK2-4567] - Implement new feature
refactor:Refactor codebase
fix: MBK1-123 Fix bug in the login page (missing " - ")
docs: KARIBU - Update documentation (missing ticket number)
NO-TICKET- Refactor codebase (extra dash)

```

</details>
