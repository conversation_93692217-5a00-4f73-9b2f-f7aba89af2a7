name: Comment PR with APK Link
description: Posts a comment on the PR with a link to the build

inputs:
  github-token:
    required: true
    description: GitHub token for authentication

runs:
  using: "composite"
  steps:
    - name: Post APK comment
      shell: bash
      env:
        GH_TOKEN: ${{ inputs.github-token }}
      run: |
        PR_NUMBER=$(jq --raw-output .pull_request.number "$GITHUB_EVENT_PATH")
        MESSAGE="📦 New Android APK built for branch \`${GITHUB_HEAD_REF}\`%0A➡️ [Download from GitHub Actions](https://github.com/${GITHUB_REPOSITORY}/actions/runs/${GITHUB_RUN_ID})"

        curl -s -X POST \
          -H "Authorization: token $GH_TOKEN" \
          -H "Accept: application/vnd.github+json" \
          https://api.github.com/repos/${GITHUB_REPOSITORY}/issues/$PR_NUMBER/comments \
          -d "{\"body\": \"$MESSAGE\"}"
