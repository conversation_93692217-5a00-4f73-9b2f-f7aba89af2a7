name: "Setup Android SDK"
description: "Sets up Java, configures Android SDK, links cache, and installs requested packages"

inputs:
  packages:
    description: 'Semicolon-separated list of Android SDK packages to install'
    required: false
    default: 'tools platform-tools'
  cache-dir:
    description: 'Directory to link ~/.android to'
    required: false
    default: '/cache/.android'

runs:
  using: "composite"
  steps:
    - name: Setup Java (required for Android SDK)
      uses: actions/setup-java@v4
      with:
        distribution: temurin
        java-version: 17

    - name: Link ~/.android to shared cache directory
      shell: bash
      run: |
        rm -rf ${{ inputs.cache-dir }}
        mkdir -p ${{ inputs.cache-dir }}
        ln -s ${{ inputs.cache-dir }} ~/.android

    - name: Setup Android SDK and install packages
      uses: ./.github/actions/setup-android-3.2.1
      with:
        log-accepted-android-sdk-licenses: false
        packages: ${{ inputs.packages }}