name: 'Changelog Checker'
description: 'Verify if CHANGELOG.md has been updated in the pull request'

inputs:
  base-branch:
    description: 'Base branch to compare against'
    required: true

runs:
  using: "composite"
  steps:
    - name: Verify CHANGELOG.md
      run: |
        if ! git diff --name-only origin/${{ inputs.base-branch }}...HEAD | grep -q "CHANGELOG.md"; then
          echo "::error::CHANGELOG.md has not been updated. Please update the CHANGELOG.md file. See other PRs for reference."
          exit 1
        fi
      shell: bash 