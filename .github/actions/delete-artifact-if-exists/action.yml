name: Delete Artifact If Exists
description: Deletes an artifact by name if it exists

inputs:
  artifact_name:
    required: true
    description: Name of the artifact to delete

runs:
  using: "composite"
  steps:
    - name: Ensure jq is available
      shell: bash
      run: |
        if ! command -v jq &> /dev/null; then
          sudo apt-get update && sudo apt-get install -y jq
        fi

    - name: Find and delete artifact
      shell: bash
      run: |
        RESPONSE=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
          "https://api.github.com/repos/${{ github.repository }}/actions/artifacts")

        ARTIFACT_ID=$(echo "$RESPONSE" | jq -r ".artifacts[] | select(.name==\"${{ inputs.artifact_name }}\") | .id")

        if [ -n \"$ARTIFACT_ID\" ]; then
          echo \"🗑 Deleting artifact: ${{ inputs.artifact_name }} (ID: $ARTIFACT_ID)\"
          curl -X DELETE \
            -H \"Authorization: token ${{ secrets.GITHUB_TOKEN }}\" \
            \"https://api.github.com/repos/${{ github.repository }}/actions/artifacts/$ARTIFACT_ID\"
        else
          echo \"⚠️ Artifact not found: ${{ inputs.artifact_name }}\"
        fi
