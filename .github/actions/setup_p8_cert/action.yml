name: 'Setup P8 Certificate'
description: 'Sets up the P8 certificate for App Store Connect API authentication'

inputs:
  p8_certificate:
    description: 'Base64 encoded P8 certificate file'
    required: true
  key_id:
    description: 'Key ID for the P8 certificate'
    required: true
  issuer_id:
    description: 'Issuer ID for the P8 certificate'
    required: true

runs:
  using: "composite"
  steps:
    - name: Check for required inputs
      shell: bash
      run: |
        if [ -z "${{ inputs.p8_certificate }}" ]; then
          echo "Error: p8_certificate input is required"
          exit 1
        fi
        if [ -z "${{ inputs.key_id }}" ]; then
          echo "Error: key_id input is required"
          exit 1
        fi
        if [ -z "${{ inputs.issuer_id }}" ]; then
          echo "Error: issuer_id input is required"
          exit 1
        fi

    - name: Create build directory
      shell: bash
      run: |
        mkdir -p build/TestFlightSigning

    - name: Decode and save P8 certificate
      shell: bash
      run: |
        echo "${{ inputs.p8_certificate }}" | base64 --decode > build/TestFlightSigning/api_key.p8

    - name: Set permissions
      shell: bash
      run: |
        chmod 600 build/TestFlightSigning/api_key.p8

    - name: Set environment variables
      shell: bash
      run: |
        echo "P8_API_KEY_CONTENT=${{ inputs.p8_certificate }}" >> $GITHUB_ENV
        echo "P8_KEY_ID_CONTENT=${{ inputs.key_id }}" >> $GITHUB_ENV
        echo "P8_ISSUER_ID_CONTENT=${{ inputs.issuer_id }}" >> $GITHUB_ENV
        echo "P8_API_KEY_PATH=$GITHUB_WORKSPACE/build/TestFlightSigning/api_key.p8" >> $GITHUB_ENV

    - name: Verify P8 certificate
      shell: bash
      run: |
        echo "=== P8 Certificate Information ==="
        echo "Certificate location: $GITHUB_WORKSPACE/build/TestFlightSigning/api_key.p8"
        echo "Key ID: ${{ inputs.key_id }}"
        echo "Issuer ID: ${{ inputs.issuer_id }}"
        
        if [ -f build/TestFlightSigning/api_key.p8 ]; then
          echo "Certificate file exists and has correct permissions:"
          ls -la build/TestFlightSigning/api_key.p8
        else
          echo "Certificate file not found!"
          exit 1
        fi 