name: Auto Remove Label
description: Removes a label from the current pull request

inputs:
  label:
    description: 'Label to remove'
    required: true
  github-token:
    description: GitHub token for authentication
    required: true

runs:
  using: "composite"
  steps:
    - name: Remove label using GitHub API
      shell: bash
      env:
        GH_TOKEN: ${{ inputs.github-token }}
        LABEL_NAME: ${{ inputs.label }}
      run: |
        PR_NUMBER=$(jq --raw-output .pull_request.number "$GITHUB_EVENT_PATH")
        echo "🔍 PR #$PR_NUMBER — Removing label: $LABEL_NAME"

        curl -X DELETE \
          -H "Authorization: token $GH_TOKEN" \
          -H "Accept: application/vnd.github+json" \
          "https://api.github.com/repos/${{ github.repository }}/issues/$PR_NUMBER/labels/${LABEL_NAME// /%20}"
        echo "✅ Label '$LABEL_NAME' removed from PR #$PR_NUMBER"