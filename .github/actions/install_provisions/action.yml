name: 'Install Provisioning Profiles'
description: 'Installs iOS provisioning profiles for production'

inputs:
  testflight_release_provisioning_profile:
    description: 'Base64 encoded TestFlight release provisioning profile'
    required: true
  notifications_testflight_release_provisioning_profile:
    description: 'Base64 encoded Notifications TestFlight release provisioning profile'
    required: true

runs:
  using: "composite"
  steps:
    - name: Check for required inputs
      shell: bash
      run: |
        if [ -z "${{ inputs.testflight_release_provisioning_profile }}" ]; then
          echo "Error: testflight_release_provisioning_profile input is required"
          exit 1
        fi
        if [ -z "${{ inputs.notifications_testflight_release_provisioning_profile }}" ]; then
          echo "Error: notifications_testflight_release_provisioning_profile input is required"
          exit 1
        fi

    - name: Create provisioning profiles directory
      shell: bash
      run: |
        mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles

    - name: Decode and install main app provisioning profile
      shell: bash
      run: |
        echo "${{ inputs.testflight_release_provisioning_profile }}" | base64 --decode > ~/Library/MobileDevice/Provisioning\ Profiles/testflight_release.mobileprovision

    - name: Decode and install notifications extension provisioning profile
      shell: bash
      run: |
        echo "${{ inputs.notifications_testflight_release_provisioning_profile }}" | base64 --decode > ~/Library/MobileDevice/Provisioning\ Profiles/notifications_testflight_release.mobileprovision

    - name: Verify provisioning profiles
      shell: bash
      run: |
        echo "Installed provisioning profiles:"
        ls -la ~/Library/MobileDevice/Provisioning\ Profiles/ 