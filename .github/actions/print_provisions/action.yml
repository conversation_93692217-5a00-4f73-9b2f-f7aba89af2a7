name: 'Print Provisioning Profiles'
description: 'Prints information about installed iOS provisioning profiles'

runs:
  using: "composite"
  steps:
    - name: Print provisioning profiles information
      shell: bash
      run: |
        echo "=== Provisioning Profiles Information ==="
        echo "Listing all provisioning profiles:"
        ls -la ~/Library/MobileDevice/Provisioning\ Profiles/
        
        echo -e "\n=== Main App Profile ==="
        if [ -f ~/Library/MobileDevice/Provisioning\ Profiles/testflight_release.mobileprovision ]; then
          echo "TestFlight Release Profile:"
          security cms -D -i ~/Library/MobileDevice/Provisioning\ Profiles/testflight_release.mobileprovision | grep -A 1 "Name\|UUID\|TeamName\|ExpirationDate"
        else
          echo "TestFlight Release Profile not found"
        fi
        
        echo -e "\n=== Notifications Extension Profile ==="
        if [ -f ~/Library/MobileDevice/Provisioning\ Profiles/notifications_testflight_release.mobileprovision ]; then
          echo "Notifications TestFlight Release Profile:"
          security cms -D -i ~/Library/MobileDevice/Provisioning\ Profiles/notifications_testflight_release.mobileprovision | grep -A 1 "Name\|UUID\|TeamName\|ExpirationDate"
        else
          echo "Notifications TestFlight Release Profile not found"
        fi 