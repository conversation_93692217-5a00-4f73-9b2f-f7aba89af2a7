name: 'Prepare Keychain'
description: 'Prepares the keychain for iOS code signing'

inputs:
  keychain_password:
    description: 'Password for the keychain'
    required: true
  signing_certificate_file:
    description: 'Base64 encoded signing certificate file'
    required: true
  signing_certificate_password:
    description: 'Password for the signing certificate'
    required: true

runs:
  using: "composite"
  steps:
    - name: Check for required inputs
      shell: bash
      run: |
        if [ -z "${{ inputs.keychain_password }}" ]; then
          echo "Error: keychain_password input is required"
          exit 1
        fi
        if [ -z "${{ inputs.signing_certificate_file }}" ]; then
          echo "Error: signing_certificate_file input is required"
          exit 1
        fi
        if [ -z "${{ inputs.signing_certificate_password }}" ]; then
          echo "Error: signing_certificate_password input is required"
          exit 1
        fi

    - name: Remove existing Keychain
      shell: bash
      run: |
        security delete-keychain ~/action-runners/ios-app-build.keychain || true

    - name: Create and configure Keychain
      shell: bash
      env:
        KEYCHAIN_PASSWORD: ${{ inputs.keychain_password }}
      run: |
        security create-keychain -p "$KEYCHAIN_PASSWORD" ~/action-runners/ios-app-build.keychain
        security set-keychain-settings -lut 21600 ~/action-runners/ios-app-build.keychain
        security unlock-keychain -p "$KEYCHAIN_PASSWORD" ~/action-runners/ios-app-build.keychain

    - name: Import signing certificate
      shell: bash
      env:
        SIGNING_CERTIFICATE_FILE: ${{ inputs.signing_certificate_file }}
        SIGNING_CERTIFICATE_PASSWORD: ${{ inputs.signing_certificate_password }}
        KEYCHAIN_PASSWORD: ${{ inputs.keychain_password }}
      run: |
        echo "$SIGNING_CERTIFICATE_FILE" | base64 --decode > certificate.p12
        security import certificate.p12 -k ~/action-runners/ios-app-build.keychain -P "$SIGNING_CERTIFICATE_PASSWORD" -T /usr/bin/codesign
        security set-key-partition-list -S apple-tool:,apple: -s -k "$KEYCHAIN_PASSWORD" ~/action-runners/ios-app-build.keychain

    - name: Set Keychain as default
      shell: bash
      run: |
        security list-keychains -s ~/action-runners/ios-app-build.keychain
        security default-keychain -s ~/action-runners/ios-app-build.keychain

    - name: Unlock Keychain
      shell: bash
      env:
        KEYCHAIN_PASSWORD: ${{ inputs.keychain_password }}
      run: |
        security unlock-keychain -p "$KEYCHAIN_PASSWORD" ~/action-runners/ios-app-build.keychain

    - name: Verify Keychain setup
      shell: bash
      run: |
        echo "Listing available Keychains:"
        security list-keychains || true
        echo "Checking for certificates in Keychain:"
        security find-identity -p codesigning -v ios-app-build.keychain || true
        echo "Verifying private keys in Keychain:"
        security find-key -l -k ios-app-build.keychain || true 