name: 'Setup Android SDK Tools'
author: 'Android-Actions'
description: 'Setup the Android SDK Tools and add them to the path'

inputs:
  cmdline-tools-version:
    description: 'cmdline-tools-version. See https://developer.android.com/studio#command-line-tools-only'
    required: false
    default: '10406996'

  accept-android-sdk-licenses:
    description: 'Android SDK is usable only after the license agreement. Should setup-android agree to the licences, provided by "sdkmanager --licenses"'
    required: false
    default: 'true'

  log-accepted-android-sdk-licenses:
    description: 'Should accepted licenses be logged. If not, accepted licences will be accepted silently'
    required: false
    default: 'true'

  packages:
    description: 'Additional packages to install'
    required: false
    default: 'tools platform-tools'

runs:
  using: node20
  main: 'dist/index.js'

branding:
  icon: 'list'
  color: 'green'
