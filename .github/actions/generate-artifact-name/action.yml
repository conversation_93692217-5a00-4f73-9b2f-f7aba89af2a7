name: Generate Artifact Name
description: Converts the current branch name into a safe artifact name

outputs:
  artifact_name:
    description: "Branch-safe artifact name"
    value: ${{ steps.generate.outputs.artifact_name }}

runs:
  using: "composite"
  steps:
    - id: generate
      shell: bash
      run: |
        BRANCH_NAME="${GITHUB_HEAD_REF:-${GITHUB_REF##*/}}"
        SAFE_NAME=$(echo "$BRANCH_NAME" | tr '[:upper:]' '[:lower:]' | tr ' /' '--')
        echo "artifact_name=$SAFE_NAME" >> $GITHUB_OUTPUT
