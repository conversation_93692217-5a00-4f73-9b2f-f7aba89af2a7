name: 'Validate Commit'
description: 'Run lint, TypeScript and tests validation'

inputs:
  node-version:
    description: 'Node.js version to use'
    required: true
    default: '18'

runs:
  using: "composite"
  steps:
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ inputs.node-version }}

    - name: Install Yarn
      run: npm install -g yarn
      shell: bash

    - name: Install dependencies
      run: yarn install
      shell: bash

    - name: Run ESLint
      run: yarn lint
      shell: bash

    - name: Run TypeScript compiler
      run: yarn tsc
      shell: bash 