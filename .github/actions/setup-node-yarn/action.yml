name: Setup Node & Yarn with Cache
description: Reusable action to setup Node.js, install specific Yarn version, and cache dependencies

inputs:
  node-version:
    description: Node.js version to install
    required: true
  yarn-version:
    description: Yarn version to install
    required: true

runs:
  using: "composite"
  steps:
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ inputs.node-version }}

    - name: Install specific Yarn version
      shell: bash
      run: |
        npm install -g yarn@${{ inputs.yarn-version }}
        yarn --version

    - name: Cache Yarn dependencies
      uses: actions/cache@v4
      with:
        path: |
          **/node_modules
          **/.yarn/cache
        key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
        restore-keys: |
          ${{ runner.os }}-yarn-
